import { post } from '@/shared/utils/request';
import type { VenuePageVO, VenuePageQueryParams, VenueVO, VenueQueryParams } from '@/entities/store/venue';

/**
 * 分页查询门店列表
 * @param params 查询参数
 * @returns 分页结果
 */
export function queryVenueList(params: VenuePageQueryParams) {
    return post<VenuePageVO>('/api/business/venue/list', params);
}

/**
 * 查询门店列表（非分页）
 * @param params 查询参数
 * @returns 门店列表
 */
export function queryVenues(params: VenueQueryParams) {
    return post<VenueVO[]>('/api/business/venue/query', params);
} 