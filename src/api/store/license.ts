import { post } from '@/shared/utils/request';
import type { 
  VenueAuthStatusVO, 
  AuditVenueReq, 
  GenerateAuthReq,
  GenerateAuthRes,
  ActivateAuthReq,
  GetAuthStatusReq,
  QueryAuthCodesReq,
  AuthCodeItem
} from '@/entities/store/license';


export function getAuthStatus(data: GetAuthStatusReq) {
  return post<VenueAuthStatusVO>('/api/business/venue/auth/status', data);
}

export function auditVenue(data: AuditVenueReq) {
  return post<any>('/api/business/venue/auth/audit', data);
}

export function generateAuth(data: GenerateAuthReq) {
  return post<GenerateAuthRes>('/api/business/venue/auth/generate', data);
}

export function activateAuth(data: ActivateAuthReq) {
  return post<any>('/api/business/venue/auth/activate', data);
}

export function queryVenueAuthCodes(params?: {
  venueId?: string;
  status?: number;
  pageNum?: number;
  pageSize?: number;
}) {
  return post<any>('/api/business/venue/auth/auth-codes', params || {});
}

/**
 * 查询授权码列表（新接口）
 * 返回类型为AuthCodeItem[]，因为request工具已经处理了响应并只返回data部分
 */
export function queryAuthCodesList(data: QueryAuthCodesReq) {
  return post<AuthCodeItem[]>('/api/business/venue/auth/non-system-codes', data);
}