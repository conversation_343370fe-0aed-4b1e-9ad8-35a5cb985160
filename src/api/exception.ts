import { post } from '@/shared/utils/request'
import type {
  ForceCloseRoomRequest,
  ForceCloseRoomResponse,
  GetKtvListResponse,
  GetRoomListResponse,
  GetOperationHistoryResponse
} from '@/entities/exception'

// 异常处理模块API接口

/**
 * 获取KTV列表
 */
export const getKtvList = (params?: {
  name?: string
  status?: string
  auditStatus?: number
  pageNum?: number
  pageSize?: number
}): Promise<GetKtvListResponse> => {
  return post('/api/business/venue/list', params || {})
}

/**
 * 根据KTV ID获取房间列表
 * @param ktvId KTV ID
 */
export const getRoomList = (ktvId: string, params?: {
  name?: string
  status?: string
  pageNum?: number
  pageSize?: number
}): Promise<GetRoomListResponse> => {
  return post('/api/business/room/list', {
    venueId: ktvId,
    ...params
  })
}

/**
 * 强制关房操作
 * @param data 强制关房请求参数
 */
export const forceCloseRoom = (data: ForceCloseRoomRequest): Promise<ForceCloseRoomResponse> => {
  return post('/api/business/room/close/force', data)
}

/**
 * 获取操作历史记录
 * @param params 查询参数
 */
export const getOperationHistory = (params?: {
  pageNum?: number
  pageSize?: number
  venueId?: string
  operationType?: string
  startTime?: string
  endTime?: string
}): Promise<GetOperationHistoryResponse> => {
  return post('/api/business/roomException/list', params || {})
}