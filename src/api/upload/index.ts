import { post } from '@/shared/utils/request'

/**
 * 上传图片到七牛云，通过后台转发
 * @param file 文件对象
 * @returns 图片访问 URL
 */
export function uploadImage(file: File): Promise<string> {
  const formData = new FormData()
  formData.append('file', file)

  // 这里不需要手动设置 Content-Type，浏览器会自动添加带 boundary 的 multipart/form-data
  return post<string>('/upload/image', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  })
} 