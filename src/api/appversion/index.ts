import { post } from '@/shared/utils/request'
import type {
  // 请求类型
  CreateAppVersionReq,
  UpdateAppVersionReq,
  ListAppVersionReq,
  CreateH5VersionReq,
  UpdateH5VersionReq,
  GetVersionTreeReq,
  // 实体类型
  AppVersion,
  H5Version,
  VersionTree,
  PageResponse
} from '@/entities/appversion/types'

// =============================================================================
// APP版本管理API
// =============================================================================

/**
 * 创建APP版本
 */
export function createAppVersion(data: CreateAppVersionReq): Promise<AppVersion> {
  return post<AppVersion>('/api/app-upgrade/create', data)
}

/**
 * 更新APP版本
 */
export function updateAppVersion(data: UpdateAppVersionReq): Promise<AppVersion> {
  return post<AppVersion>('/api/app-upgrade/update', data)
}

/**
 * 查询APP版本列表
 */
export function listAppVersions(data: ListAppVersionReq): Promise<PageResponse<AppVersion>> {
  return post<PageResponse<AppVersion>>('/api/app-upgrade/list', data)
}

/**
 * 删除APP版本
 */
export function deleteAppVersion(id: string): Promise<void> {
  return post<void>('/api/app-upgrade/delete', { id })
}

// =============================================================================
// H5版本管理API
// =============================================================================

/**
 * 创建H5版本
 */
export function createH5Version(data: CreateH5VersionReq): Promise<H5Version> {
  return post<H5Version>('/api/app-h5-version/create', data)
}

/**
 * 更新H5版本
 */
export function updateH5Version(data: UpdateH5VersionReq): Promise<H5Version> {
  return post<H5Version>('/api/app-h5-version/update', data)
}

/**
 * 删除H5版本
 */
export function deleteH5Version(id: string): Promise<void> {
  return post<void>('/api/app-h5-version/delete', { id })
}

// =============================================================================
// 版本树形结构API
// =============================================================================

/**
 * 获取版本树形结构（APP版本 + 其下的H5版本列表）
 */
export function getVersionTree(data: GetVersionTreeReq): Promise<PageResponse<VersionTree>> {
  return post<PageResponse<VersionTree>>('/api/app-upgrade/version-tree', data)
} 