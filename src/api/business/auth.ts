import { post } from '@/shared/utils/request'
import type { BusinessLoginReqDto } from '@/entities/auth/BusinessLoginReqDto'
import type { BusinessLoginVO } from '@/entities/auth/BusinessLoginVO'

/**
 * 商务认证相关接口
 */
export const businessAuthApi = {
  /**
   * 商务登录
   * @param data 登录请求参数
   * @returns 登录响应
   */
  login(data: BusinessLoginReqDto): Promise<BusinessLoginVO> {
    return post<BusinessLoginVO>('/api/business/auth/login', data)
  },

  /**
   * 商务登出
   */
  logout(): Promise<void> {
    return post('/api/business/auth/logout')
  }
} 