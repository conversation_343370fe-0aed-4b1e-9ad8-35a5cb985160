import { post } from '@/shared/utils/request'
import type {
  StockImportPreviewReqDto,
  StockImportPreviewVO,
  StockImportExecuteReqDto,
  StockImportExecuteVO,
  VenueStockListReqDto,
  VenueStockListVO
} from '@/entities/business/inventory'

/**
 * 商务后台库存管理API
 */
export const businessInventoryApi = {
  /**
   * 预览导入库存数据
   * @param data 预览导入请求参数
   * @returns 预览导入响应数据
   */
  previewImportStock: (data: StockImportPreviewReqDto) => {
    return post<StockImportPreviewVO>('/api/business/inventory/import/preview', data)
  },

  /**
   * 执行导入库存数据
   * @param data 执行导入请求参数
   * @returns 执行导入响应数据
   */
  executeImportStock: (data: StockImportExecuteReqDto) => {
    return post<StockImportExecuteVO>('/api/business/inventory/import/execute', data)
  },

  /**
   * 查询门店库存列表
   * @param data 查询库存列表请求参数
   * @returns 库存列表响应数据
   */
  getVenueStockList: (data: VenueStockListReqDto) => {
    return post<VenueStockListVO>('/api/business/inventory/stock/list', data)
  }
} 