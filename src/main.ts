import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import { router } from './router'
import { createPinia } from 'pinia'
import App from './App.vue'
import './style.css'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const app = createApp(App)
const pinia = createPinia()

// 注册持久化插件到 Pinia
pinia.use(piniaPluginPersistedstate)

// 配置Element Plus
app.use(ElementPlus, {
  size: 'default',
  zIndex: 3000
})

app.use(router)
app.use(pinia)

app.mount('#app')

// 添加全局类型声明
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $filters: {
      formatDate: (value: string) => string
    }
  }
}