<template>
  <router-view v-slot="{ Component, route }">
    <template v-if="route.path === '/login'">
      <transition name="fade" mode="out-in">
        <component :is="Component" />
      </transition>
    </template>
    <template v-else>
      <AppLayout>
        <transition name="fade" mode="out-in">
          <component :is="Component" />
        </transition>
      </AppLayout>
    </template>
  </router-view>
</template>

<script setup lang="ts">
import AppLayout from '@/shared/components/layout/AppLayout.vue'
</script>

<style>
/* 可以添加全局样式 */
html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#app {
  height: 100%;
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.2s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
</style>
