// 登录状态常量
export const LoginStatus = {
    PENDING_REVIEW: 0, // 待审核
    NORMAL: 1,        // 正常
    DISABLED: 2       // 禁用
} as const;

// API错误码定义
export const ApiErrorCode = {
    SUCCESS: 0,           // 成功
    UNAUTHORIZED: 402,    // 未授权（无token）
    INVALID_MOBILE: 4003, // 手机号不存在
    PENDING_REVIEW: 4004, // 账号待审核
    INVALID_PASSWORD: 4005, // 密码错误
    ACCOUNT_LOCKED: 4002  // 账号已锁定
} as const;

export interface BusinessLoginVO {
    // 认证令牌
    token: string;
    // 员工ID
    staffId: string;
    // 员工姓名
    staffName: string;
    // 手机号
    mobile: string;
    // 权限等级（0-无权限，1-完全权限）
    permissionLevel: number;
    // 令牌过期时间戳（单位：秒）
    expireTime: number;
    // 状态（0-待审核 1-正常 2-禁用）
    status: number;
}