// 异常处理模块实体类型定义

export interface KtvEntity {
  id: string
  name: string
  address?: string
  status?: string
  contact?: string        // 联系人
  contactPhone?: string   // 联系电话
  createTime?: string
  updateTime?: string
}

export interface RoomEntity {
  id: string
  name: string
  status: string
  venueId: string  // 对应KTV ID
  roomType?: string
  capacity?: number
  createTime?: string
  updateTime?: string
}

export interface ForceCloseRoomRequest {
  venueId: string
  roomId: string
  venueName: string
  roomName: string
  reason?: string
  operator?: string
  exceptionDescription?: string  // 关闭原因备注
}

export interface ForceCloseRoomResponse {
  success: boolean
  message: string
  data?: {
    operationId: string
    timestamp: string
  }
}

export interface OperationHistoryEntity {
  id: string
  operationId: string
  venueId: string
  venueName: string
  roomId: string
  roomName: string
  operationType: 'FORCE_CLOSE' | 'FORCE_OPEN' | 'RESET'
  operator: string
  operatorName: string
  operatorId: string
  exceptionTime: string
  exceptionDescription: string  // 关闭原因
  exceptionType: string         // 类型
  status: 'SUCCESS' | 'FAILED' | 'PENDING'
  reason?: string
  createTime: string
  updateTime?: string
}

export interface GetKtvListResponse {
  success: boolean
  data: KtvEntity[]
  total: number
}

export interface GetRoomListResponse {
  success: boolean
  data: RoomEntity[]
  total: number
}

export interface GetOperationHistoryResponse {
  success: boolean
  data: OperationHistoryEntity[]
  total: number
  pageNum: number
  pageSize: number
} 