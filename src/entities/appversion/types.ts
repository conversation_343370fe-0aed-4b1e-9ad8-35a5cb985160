// 版本管理系统实体类型定义

// 新增客户端类型枚举
export type ClientType = 'CASHIER_ANDROID' | 'CASHIER_WINDOWS' | 'MOBILE_ORDER'

// 环境枚举
export type Environment = 'TEST' | 'PREVIEW' | 'PRODUCTION'

// APP版本实体
export interface AppVersion {
  id?: string                    // 版本ID
  clientType: ClientType  // 客户端类型
  environment: Environment // 环境
  versionCode: number           // 版本号（用于比较）
  versionName: string          // 版本名称（显示给用户）
  upgradeTitle?: string        // 升级标题
  upgradeContent?: string      // 升级内容描述
  downloadUrl: string          // APK/IPA下载地址
  fileSize?: number           // 文件大小（字节）
  fileMd5?: string           // 文件MD5校验值
  forceUpgrade: boolean      // 是否强制升级
  createTime?: number        // 创建时间戳（毫秒）
  updateTime?: number        // 更新时间戳（毫秒）
}

// H5版本实体
export interface H5Version {
  id?: string              // H5版本ID
  appUpgradeId: string    // 关联的APP版本ID
  environment: Environment // 环境类型
  h5Tag: string          // H5版本标签
  h5Url: string         // H5链接地址
  description?: string   // 版本描述
  isActive: boolean     // 是否激活
  state?: number        // 状态
  version?: number      // 版本号（乐观锁）
  createTime?: number   // 创建时间戳（毫秒）
  updateTime?: number   // 更新时间戳（毫秒）
}

// 版本树形结构（APP版本 + 其下的H5版本列表）
export interface VersionTree extends AppVersion {
  children: H5Version[]  // H5版本列表
}

// =============================================================================
// API 请求类型
// =============================================================================

// 创建APP版本请求
export interface CreateAppVersionReq {
  clientType: ClientType
  environment: Environment
  versionCode: number
  versionName: string
  upgradeTitle?: string
  upgradeContent?: string
  downloadUrl: string
  fileSize?: number
  fileMd5?: string
  forceUpgrade: boolean
}

// 更新APP版本请求
export interface UpdateAppVersionReq extends CreateAppVersionReq {
  id: string  // 要更新的版本ID
}

// 查询APP版本列表请求
export interface ListAppVersionReq {
  clientType: ClientType
  environment?: Environment
  pageNum: number
  pageSize: number
}

// 创建H5版本请求
export interface CreateH5VersionReq {
  appUpgradeId: string
  environment: Environment
  h5Tag: string
  h5Url: string
  description?: string
  isActive: boolean
}

// 更新H5版本请求
export interface UpdateH5VersionReq extends CreateH5VersionReq {
  id: string  // 要更新的H5版本ID
}

// 查询版本树形结构请求
export interface GetVersionTreeReq {
  clientType?: ClientType
  pageNum: number
  pageSize: number
}

// =============================================================================
// API 响应类型
// =============================================================================

// 通用API响应
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应
export interface PageResponse<T = any> {
  pageNum: number
  pageSize: number
  total: number
  data: T[]
}

// APP版本创建/更新响应
export type AppVersionResponse = ApiResponse<AppVersion>

// APP版本列表响应
export type AppVersionListResponse = ApiResponse<PageResponse<AppVersion>>

// H5版本创建/更新响应
export type H5VersionResponse = ApiResponse<H5Version>

// 版本树形结构响应
export type VersionTreeResponse = ApiResponse<PageResponse<VersionTree>> 