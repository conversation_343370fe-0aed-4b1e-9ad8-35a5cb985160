/**
 * 商务后台库存管理相关实体类型定义
 */

/**
 * 库存导入项DTO (根据真实API规范)
 */
export interface StockImportItemDto {
  /** 产品ID */
  productId: string;
  /** 库存数量 */
  stock: number;
}

/**
 * 库存导入预览请求DTO (根据真实API规范)
 */
export interface StockImportPreviewReqDto {
  /** 门店ID */
  venueId: string;
  /** 导入项列表 */
  items: StockImportItemDto[];
  /** 创建时间 */
  ctime: number;
  /** 更新时间 */
  utime: number;
}

/**
 * 库存导入预览项VO (根据真实API规范)
 */
export interface StockImportPreviewItemVO {
  /** 产品ID */
  productId?: string;
  /** 产品名称 */
  productName?: string;
  /** 当前库存 */
  currentStock?: number;
  /** 新库存 */
  stock?: number;
  /** 操作类型 */
  operation?: string;
  /** 是否有效 */
  valid?: boolean;
  /** 原因/错误信息 */
  reason?: string;
}

/**
 * 库存导入预览响应VO (根据真实API规范)
 */
export interface StockImportPreviewVO {
  /** 门店ID */
  venueId?: string;
  /** 总项目数 */
  totalItems?: number;
  /** 有效项目数 */
  validItems?: number;
  /** 无效项目数 */
  invalidItems?: number;
  /** 预览项目列表 */
  previewItems?: StockImportPreviewItemVO[];
  /** 导入令牌 */
  importToken?: string;
  /** 令牌过期时间 */
  tokenExpireTime?: number;
}

/**
 * 库存导入执行请求DTO (根据真实API规范)
 */
export interface StockImportExecuteReqDto {
  /** 门店ID */
  venueId: string;
  /** 导入令牌 */
  importToken: string;
}

/**
 * 库存导入失败项VO (根据真实API规范)
 */
export interface StockImportFailedItemVO {
  /** 产品ID */
  productId?: string;
  /** 库存数量 */
  stock?: number;
  /** 失败原因 */
  reason?: string;
}

/**
 * 库存导入执行响应VO (根据真实API规范)
 */
export interface StockImportExecuteVO {
  /** 门店ID */
  venueId?: string;
  /** 总数量 */
  totalCount?: number;
  /** 成功数量 */
  successCount?: number;
  /** 失败数量 */
  failedCount?: number;
  /** 失败项目列表 */
  failedItems?: StockImportFailedItemVO[];
  /** 导入时间 */
  importTime?: number;
  /** 结果消息 */
  message?: string;
}

/**
 * 门店库存列表查询请求DTO
 */
export interface VenueStockListReqDto {
  /** 门店ID - 必填 */
  venueId: string;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 分类ID */
  categoryId?: string;
  /** 库存状态 */
  stockStatus?: 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK';
  /** 页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 排序字段 */
  sortBy?: string;
  /** 排序方向 */
  sortOrder?: 'ASC' | 'DESC';
}

/**
 * 门店库存列表响应VO
 */
export interface VenueStockListVO {
  /** 库存列表 */
  list?: VenueStockItem[];
  /** 总记录数 */
  total?: number;
  /** 当前页码 */
  pageNum?: number;
  /** 每页大小 */
  pageSize?: number;
  /** 总页数 */
  pages?: number;
}

/**
 * 门店库存项
 */
export interface VenueStockItem {
  /** 库存ID */
  stockId?: string;
  /** 门店ID */
  venueId?: string;
  /** 门店名称 */
  venueName?: string;
  /** 产品ID */
  productId?: string;
  /** 产品编码 */
  productCode?: string;
  /** 产品名称 */
  productName?: string;
  /** 产品分类 */
  categoryName?: string;
  /** 当前库存数量 */
  currentStock?: number;
  /** 可用库存数量 */
  availableStock?: number;
  /** 预留库存数量 */
  reservedStock?: number;
  /** 单位 */
  unit?: string;
  /** 成本价 */
  costPrice?: number;
  /** 售价 */
  salePrice?: number;
  /** 库存状态 */
  stockStatus?: 'IN_STOCK' | 'OUT_OF_STOCK' | 'LOW_STOCK';
  /** 最低库存预警值 */
  minStockAlert?: number;
  /** 最后更新时间 */
  lastUpdateTime?: string;
  /** 创建时间 */
  createTime?: string;
} 