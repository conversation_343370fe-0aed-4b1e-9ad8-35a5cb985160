/**
 * 商务后台产品管理相关实体类型定义
 */

/**
 * 产品查询请求DTO
 */
export interface ProductQueryReqDto {
  /** 所属门店ID */
  venueId?: string;
  /** 产品名称 */
  name?: string;
  /** 条形码 */
  barcode?: string;
  /** 商品分类 */
  category?: string;
  /** 产品类型ID */
  productTypeId?: string;
  /** 套餐类型ID */
  productPackageTypeId?: string;
  /** 产品类型 */
  type?: string;
  /** 是否上架展示 */
  isDisplayed?: boolean;
  /** 是否洁清 */
  isSoldOut?: boolean;
  /** 是否为实价产品 */
  isRealPriceProduct?: boolean;
  /** 推广 */
  isPromotion?: boolean;
  /** 是否支持外送 */
  supportsExternalDelivery?: boolean;
  /** 支持重复购买 */
  allowRepeatBuy?: boolean;
  /** 支持员工赠送 */
  allowStaffGift?: boolean;
  /** 支持存酒 */
  allowWineStorage?: boolean;
  /** 计算库存 */
  calculateInventory?: boolean;
  /** 计入低消 */
  countToMinCharge?: boolean;
  /** 计算业绩 */
  countToPerformance?: boolean;
  /** 指定投放区域 */
  isAreaSpecified?: boolean;
  /** 指定投放包厢类型 */
  isRoomTypeSpecified?: boolean;
  /** 当前价格 */
  currentPrice?: number;
  /** 支付价格 */
  payPrice?: number;
  /** 外送价格 */
  externalDeliveryPrice?: number;
  /** 最小销售数量 */
  minimumSaleQuantity?: number;
  /** 低库存数 */
  lowStockThreshold?: number;
  /** 送达超时时间 */
  deliveryTimeout?: number;
  /** 单位 */
  unit?: string;
  /** 商品图片 */
  image?: string;
  /** 商品介绍 */
  description?: string;
  /** 商品口味 */
  flavors?: string;
  /** 辅料配方 */
  ingredients?: string;
  /** 不同区域价格 */
  areaPrices?: string;
  /** 时段价格 */
  timeSlotPrices?: string;
  /** 支持折扣 */
  discounts?: string;
  /** 分销渠道 */
  distributionChannels?: string;
  /** 买赠方案 */
  buyGiftPlan?: string;
  /** 消费赠券 */
  giftVoucher?: string;
  /** 会员卡结账限制 */
  memberCardLimits?: string;
  /** 会员卡支付限制 */
  memberCardPaymentRestrictions?: string;
  /** 推荐搭配 */
  recommendCombos?: string;
  /** 指定的投放区域 */
  selectedAreas?: string;
  /** 指定的投放包厢类型 */
  selectedRoomTypes?: string;
  /** 支付标签 */
  payMark?: string;
  /** 辅助公式 */
  auxiliaryFormula?: string;
  /** 投放开始时间 */
  startTime?: string;
  /** 投放结束时间 */
  endTime?: string;
  /** ID */
  id?: string;
  /** ID列表 */
  ids?: string[];
  /** 页码 */
  pageNum?: number;
  /** 每页记录数 */
  pageSize?: number;
}

/**
 * 产品查询响应VO (标准API响应格式)
 */
export interface ProductQueryVO {
  /** 响应码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 产品列表数据 */
  data?: ProductItem[];
  /** 请求ID */
  requestID?: string;
  /** 服务器时间 */
  serverTime?: number;
  /** 链路追踪ID */
  traceId?: string;
  /** 附件信息 */
  attachments?: Record<string, string>;
}

/**
 * 产品项 (根据真实API规范定义)
 */
export interface ProductItem {
  /** ID */
  id?: string;
  /** 产品名称 */
  name?: string;
  /** 条形码 */
  barcode?: string;
  /** 商品分类 */
  category?: string;
  /** 产品类型 */
  type?: string;
  /** 是否上架展示 */
  isDisplayed?: boolean;
  /** 是否洁清 */
  isSoldOut?: boolean;
  /** 洁清时间 */
  soldOutTime?: number;
  /** 是否为实价产品 */
  isRealPriceProduct?: boolean;
  /** 推广 */
  isPromotion?: boolean;
  /** 是否支持外送 */
  supportsExternalDelivery?: boolean;
  /** 支持重复购买 */
  allowRepeatBuy?: boolean;
  /** 支持员工赠送 */
  allowStaffGift?: boolean;
  /** 支持存酒 */
  allowWineStorage?: boolean;
  /** 计算库存 */
  calculateInventory?: boolean;
  /** 计入低消 */
  countToMinCharge?: boolean;
  /** 计算业绩 */
  countToPerformance?: boolean;
  /** 指定投放区域 */
  isAreaSpecified?: boolean;
  /** 指定投放包厢类型 */
  isRoomTypeSpecified?: boolean;
  /** 当前价格 */
  currentPrice?: number;
  /** 外送价格 */
  externalDeliveryPrice?: number;
  /** 最小销售数量 */
  minimumSaleQuantity?: number;
  /** 低库存数 */
  lowStockThreshold?: number;
  /** 送达超时时间 */
  deliveryTimeout?: number;
  /** 单位 */
  unit?: string;
  /** 商品图片 */
  image?: string;
  /** 商品介绍 */
  description?: string;
  /** 商品口味 */
  flavors?: string;
  /** 辅料配方 */
  ingredients?: string;
  /** 不同区域价格 */
  areaPrices?: string;
  /** 时段价格 */
  timeSlotPrices?: string;
  /** 分销渠道 */
  distributionChannels?: string;
  /** 买赠方案 */
  buyGiftPlan?: string;
  /** 消费赠券 */
  giftVoucher?: string;
  /** 会员卡结账限制 */
  memberCardLimits?: string;
  /** 会员卡支付限制 */
  memberCardPaymentRestrictions?: string;
  /** 推荐搭配 */
  recommendCombos?: string;
  /** 指定的投放区域 */
  selectedAreas?: string;
  /** 指定的投放包厢类型 */
  selectedRoomTypes?: string;
  /** 支付标签 */
  payMark?: string;
  /** 辅助公式 */
  auxiliaryFormula?: string;
  /** 投放开始时间 */
  startTime?: string;
  /** 投放结束时间 */
  endTime?: string;
  /** 状态 */
  state?: number;
  /** 库存数量 */
  stock?: number;
  /** 所属门店ID */
  venueId?: string;
  /** 版本 */
  version?: number;
  /** 创建时间 */
  ctime?: number;
  /** 更新时间 */
  utime?: number;
  /** 是否支持会员折扣 */
  isMemberDiscountable?: boolean;
  /** 是否支持商家折扣 */
  isOrderDiscountable?: boolean;
  /** 是否支持商家减免 */
  isOrderReduceable?: boolean;
} 