/**
 * 门店授权状态视图对象，对应服务端 VO
 */
export interface VenueAuthStatusVO {
    venue_id: string;         // 门店ID
    auth_status: number;      // 授权状态：0-未授权 1-试用中 2-正式授权 3-已过期
    expire_time: string;      // 到期时间
    remain_days: number;      // 剩余天数
    auth_type: number;        // 授权类型：1-试用授权 2-正式授权
    last_auth_time: string;   // 最近授权时间
    last_audit_time: string;  // 最近审核时间
    audit_remark: string;     // 审核备注
    contract_no: string;      // 合同编号
    contract_name: string;    // 合同名称
}

/**
 * 审核门店请求参数
 */
export interface AuditVenueReq {
    venue_id: string; // 门店ID
    status: number;   // 操作状态：例如 0-待审核 1-通过 2-拒绝
    remark?: string;  // 可选的审核备注
}

/**
 * 生成授权码请求参数
 */
export interface GenerateAuthReq {
    venue_id?: string;        // 门店ID
    valid_period?: number;    // 有效期(天)
    remark?: string;          // 备注
    contractNo: string;       // 合同编号
    contractName: string;     // 合同名称
    creator_id?: string;      // 创建人ID
}

/**
 * 生成授权码响应
 */
export interface GenerateAuthRes {
    authCode: string;         // 生成的授权码
}

/**
 * 激活授权请求参数
 */
export interface ActivateAuthReq {
    venue_id: string;   // 门店ID
    auth_code: string;  // 生成的授权码
}

/**
 * 获取授权状态请求参数（对应服务端GetAuthStatusReqDto）
 */
export interface GetAuthStatusReq {
  venue_id: string; // 门店ID
}

/**
 * 查询授权码列表请求参数
 */
export interface QueryAuthCodesReq {
    status?: number;          // 状态过滤：0-未使用 1-已使用 2-已过期
    creatorId?: string;       // 创建人ID（可选参数，根据实际API确定）
}

/**
 * 授权码项目（根据真实API返回值定义）
 */
export interface AuthCodeItem {
    id: string;               // 授权码ID
    venueId: string;          // 门店ID
    authCode: string;         // 授权码
    validPeriod: number;      // 有效期(天)
    startTime: number;        // 开始时间（时间戳）
    expireTime: number;       // 过期时间（时间戳）
    status: number;           // 状态：0-未使用 1-已使用 2-已过期
    creatorId: string;        // 创建人ID
    remark: string;           // 备注
    contractNo: string;       // 合同编号
    contractName: string;     // 合同名称
    ctime: number;            // 创建时间（时间戳）
    utime: number;            // 更新时间（时间戳）
    state: number;            // 状态字段
    version: number;          // 版本号
    venueVO?: VenueVO  // 门店信息对象（可选）
}

/**
 * 查询授权码列表响应（根据真实API返回值定义）
 */
export interface QueryAuthCodesRes {
    code: number;             // 响应码
    message: string;          // 响应消息
    data: AuthCodeItem[];     // 授权码列表
    requestID: string;        // 请求ID
    serverTime: number;       // 服务器时间
}

// 门店信息对象
export interface VenueVO {
  id: string
  name: string
  venueType: number
  logo: string
  province: string
  city: string
  district: string
  address: string
  isLeshuaPay: number
  contact: string
  contactPhone: string
  startHours: string
  endHours: string
  earlyStartHours: string
  earlyEndHours: string
  noonStartHours: string
  noonEndHours: string
  lateStartHours: string
  lateEndHours: string
  photos: string
  description: string
  ctime: number
  utime: number
  state: number
  version: number
  appId: string
  appKey: string
  isThunderVOD: number
  auditStatus: number
}
