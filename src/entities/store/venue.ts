import type { PageVO } from '@/entities/common/api'

/** 场馆基础信息 */
export interface VenueVO {
    id: string;          // 门店唯一标识
    name: string;        // 门店名称
    venueType: number;   // 门店类型：1-量贩式 2-商务KTV 3-酒吧 4-其他
    logo: string;        // 门店logo URL地址
    province: string;    // 所在省份
    city: string;        // 所在城市
    district: string;   // 所在区县
    address: string;    // 详细地址
    contact: string;    // 联系人姓名
    contactPhone: string; // 联系人手机号
    startHours: string; // 每日营业开始时间（格式HH:mm）
    endHours: string;   // 每日营业结束时间（格式HH:mm）
    photos: string;     // 门店实景照片URL列表（多个用逗号分隔）
    appId: string;      // VOD应用ID（与加密狗绑定）
    appKey: string;     // VOD应用密钥（与加密狗绑定）
    isThunderVOD: number; // 是否使用雷石VOD点歌系统：1-是 0-否
    auditStatus: number; // 审核状态：0-待审核 1-已通过 2-已拒绝
    ctime: number;      // 创建时间戳
    utime: number;      // 更新时间戳
}


/** 查询参数 */
export interface VenueQueryParams {
    id?: string;                   // ID
    name?: string;                 // 场地名称
    address?: string;             // 场地地址
    startHour?: string;           // 开始营业时间
    endHour?: string;             // 结束营业时间
    logo?: string;                // 场地logo URL
    photos?: string;              // 场地照片URL列表
    auditStatus?: number;         // 审核状态：0-待审核 1-已通过 2-已拒绝
}

/** 分页查询参数 */
export interface VenuePageQueryParams extends VenueQueryParams {
    pageNum?: number;              // 页码
    pageSize?: number;             // 每页数量
}

/** 分页查询响应类型 */
export type VenuePageVO = PageVO<VenueVO>; 