<template>
  <div class="auth-code-manage-container">
    <!-- 页面标题区和筛选条件整合 -->
    <div class="header-section bg-white p-4 rounded-lg shadow-sm mb-6">
      <!-- 标题、筛选条件和操作按钮在同一行 -->
      <div class="flex items-center justify-between gap-6">
        <!-- 标题 -->
        <h2 class="text-2xl font-bold flex-shrink-0">授权码管理</h2>
        
        <!-- 筛选条件 -->
        <div class="flex-1">
          <el-form :inline="true" :model="vm.state.filters" class="flex items-center gap-4">
            <!-- 状态过滤 -->
            <el-form-item label="状态">
              <el-select 
                v-model="vm.state.filters.status"
                placeholder="请选择状态"
                @change="vm.actions.handleStatusChange"
                style="width: 140px;"
              >
                <el-option label="全部" :value="null" />
                <el-option label="未使用" :value="0" />
                <el-option label="已使用" :value="1" />
                <el-option label="已过期" :value="2" />
              </el-select>
            </el-form-item>

            <!-- 创建人搜索 -->
            <el-form-item label="创建人">
              <el-input
                v-model="vm.state.filters.creatorId"
                placeholder="请输入创建人"
                @input="vm.actions.handleCreatorChange"
                style="width: 160px;"
              >
                <template #suffix>
                  <el-icon><User /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 关键词搜索 -->
            <el-form-item label="关键词">
              <el-input
                v-model="vm.state.filters.keyword"
                placeholder="门店/授权码/合同"
                @input="vm.actions.handleKeywordInput"
                style="width: 200px;"
              >
                <template #suffix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item>
              <el-button type="primary" @click="vm.actions.handleSearch">刷新</el-button>
              <el-button @click="vm.actions.handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 生成授权码按钮 -->
        <el-button 
          type="primary" 
          size="large"
          @click="vm.actions.handleShowGenerateDialog"
          class="generate-btn flex-shrink-0"
        >
          <el-icon class="mr-1"><Plus /></el-icon>
          生成授权码
        </el-button>
      </div>

      <!-- 统计信息 -->
      <div class="text-sm text-gray-600 mt-3">
        共找到 {{ vm.computes.filteredList.value.length }} 条授权码记录
        <span v-if="vm.state.filters.status !== null || vm.state.filters.creatorId || vm.state.filters.keyword">
          （已过滤，原始数据 {{ vm.state.list.length }} 条）
        </span>
      </div>
    </div>

    <!-- 列表内容区 - 固定高度，内部滚动 -->
    <div class="list-container bg-white rounded-lg shadow-sm">
      <!-- 加载状态 -->
      <div v-if="vm.state.loading" class="flex justify-center items-center py-12">
        <el-skeleton :rows="5" animated />
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="vm.computes.isEmpty.value" description="暂无授权码数据" class="py-12" />

      <!-- 过滤后无数据 -->
      <el-empty 
        v-else-if="vm.computes.filteredList.value.length === 0" 
        description="当前筛选条件下暂无数据" 
        class="py-12"
      />

      <!-- 表格列表 - 可滚动区域 -->
      <div v-else class="table-scroll-container">
        <el-table
          :data="vm.computes.filteredList.value"
          stripe
          border
          style="width: 100%"
          :header-cell-style="{ background: '#f5f7fa' }"
          height="100%"
        >
          <!-- 授权码列 -->
          <el-table-column prop="authCode" label="授权码" width="180" fixed="left">
            <template #default="{ row }">
              <div class="flex items-center">
                <span class="font-mono mr-2">{{ row.authCode }}</span>
                <el-button 
                  size="small" 
                  text 
                  type="primary"
                  @click="vm.actions.copyAuthCode(row.authCode)"
                  title="复制授权码"
                >
                  <el-icon><CopyDocument /></el-icon>
                </el-button>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="vm.computes.getStatusType(row.status)" size="small">
                {{ vm.computes.getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 激活时间列 -->
          <el-table-column label="激活时间" width="150" align="center">
            <template #default="{ row }">
              <span v-if="row.startTime && row.startTime !== 0">
                {{ vm.computes.formatTimestamp(row.startTime) }}
              </span>
              <span v-else class="text-gray-400">未激活</span>
            </template>
          </el-table-column>

          <!-- 门店名称列 -->
          <el-table-column label="门店名称" width="200" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.venueVO && row.venueVO.name">{{ row.venueVO.name }}</span>
              <span v-else class="text-gray-400">待绑定</span>
            </template>
          </el-table-column>

          <!-- 创建人列 -->
          <el-table-column prop="creatorId" label="创建人" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.creatorId">{{ row.creatorId }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <!--合同编号-->
          <el-table-column prop="contractNo" label="合同编号" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.contractNo">{{ row.contractNo }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <!--合同名称-->
          <el-table-column prop="contractName" label="合同名称" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.contractName">{{ row.contractName }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
          <!--备注-->
          <el-table-column prop="remark" label="备注" width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span v-if="row.remark">{{ row.remark }}</span>
              <span v-else class="text-gray-400">-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 生成授权码弹窗 -->
    <el-dialog
      v-model="vm.state.generateDialog.visible"
      title="生成授权码"
      width="380px"
      :close-on-click-modal="false"
    >
      <el-form 
        :model="vm.state.generateDialog.form" 
        label-width="80px"
        class="generate-form"
      >
        <el-form-item label="合同编号">
          <el-input
            v-model="vm.state.generateDialog.form.contractNo"
            placeholder="请输入合同编号（选填）"
            maxlength="100"
          />
        </el-form-item>

        <el-form-item label="合同名称">
          <el-input
            v-model="vm.state.generateDialog.form.contractName"
            placeholder="请输入合同名称（选填）"
            maxlength="200"
          />
        </el-form-item>

        <el-form-item label="备注">
          <el-input
            v-model="vm.state.generateDialog.form.remark"
            type="textarea"
            rows="3"
            placeholder="请输入备注信息（选填）"
            maxlength="500"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="flex justify-end space-x-2">
          <el-button @click="vm.actions.handleGenerateCancel">取消</el-button>
          <el-button 
            type="primary" 
            @click="vm.actions.handleGenerateConfirm"
            :loading="vm.state.generateDialog.loading"
          >
            生成授权码
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Plus, Search, User, CopyDocument } from '@element-plus/icons-vue'
import { useAuthCodeManagePresenter } from './presenter'

const vm = useAuthCodeManagePresenter()
</script>

<style scoped>
.auth-code-manage-container {
  padding: 1rem;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header-section {
  border: 1px solid #e4e7ed;
  flex-shrink: 0;
}

.generate-btn {
  font-size: 16px;
  padding: 12px 24px;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.generate-btn:hover {
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.4);
  transform: translateY(-1px);
}



.header-section .el-form-item {
  margin-bottom: 0;
  margin-right: 0;
}

.header-section .el-form {
  margin: 0;
}

.list-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  border: 1px solid #e4e7ed;
}

.table-scroll-container {
  flex: 1;
  height: 0; /* 关键：让flex子项能够正确计算高度 */
}

.generate-form .el-form-item {
  margin-bottom: 22px;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
  height: 100% !important;
}

:deep(.el-table th) {
  font-weight: 600;
}

:deep(.el-table .cell) {
  padding: 8px 12px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .header-section .flex {
    flex-wrap: wrap;
    gap: 1rem;
  }
  
  .header-section .flex-1 {
    order: 3;
    flex-basis: 100%;
  }
  
  .header-section .generate-btn {
    order: 2;
  }
}

@media (max-width: 768px) {
  .auth-code-manage-container {
    padding: 0.5rem;
  }
  
  .header-section {
    padding: 1rem;
  }

  .header-section .flex {
    flex-direction: column;
    align-items: stretch;
    gap: 1rem;
  }

  .header-section .el-form {
    flex-direction: column;
    gap: 0.75rem;
  }

  .header-section .el-form-item {
    margin-bottom: 0;
  }

  .header-section .el-select,
  .header-section .el-input {
    width: 100% !important;
  }

  .generate-btn {
    width: 100%;
  }

  /* 移动端表格样式 */
  :deep(.el-table) {
    font-size: 12px;
  }
  
  :deep(.el-table .cell) {
    padding: 6px 8px;
  }
}

/* 表格滚动条样式 */
:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  height: 8px;
  width: 8px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: #f1f1f1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: #c1c1c1;
  border-radius: 4px;
}

:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: #a8a8a8;
}
</style> 