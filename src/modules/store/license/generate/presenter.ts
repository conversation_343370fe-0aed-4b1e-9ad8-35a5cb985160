import { reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { 
  IAuthCodeManageViewModel,
  IAuthCodeManageState,
  IAuthCodeManageComputed,
  IAuthCodeManageActions
} from './viewmodel'
import { useAuthCodeManageConverter } from './converter'
import { useAuthCodeManageInteractor } from './interactor'

export function useAuthCodeManagePresenter(): IAuthCodeManageViewModel {
  const converter = useAuthCodeManageConverter()
  const interactor = useAuthCodeManageInteractor()

  // 状态管理
  const state = reactive<IAuthCodeManageState>(converter.createInitialState())

  // 计算属性
  const computes: IAuthCodeManageComputed = {
    isEmpty: computed(() => !state.loading && state.list.length === 0),
    
    getStatusType: (status: number) => {
      return converter.getStatusInfo(status).type
    },
    
    getStatusText: (status: number) => {
      return converter.getStatusInfo(status).text
    },
    
    formatTimestamp: (timestamp: number) => {
      return interactor.formatTimestamp(timestamp)
    },
    
    formatRemainDays: (expireTimestamp: number) => {
      const remainDays = interactor.calculateRemainDays(expireTimestamp)
      if (remainDays <= 0) return '已过期'
      if (remainDays <= 7) return `${remainDays}天后过期`
      return `${remainDays}天`
    },
    
    isExpired: (expireTimestamp: number) => {
      return interactor.isExpired(expireTimestamp)
    },

    // 客户端过滤列表
    filteredList: computed(() => {
      let filtered = [...state.list]

      // 状态过滤
      if (state.filters.status !== null) {
        filtered = filtered.filter(item => item.status === state.filters.status)
      }

      // 创建人过滤
      if (state.filters.creatorId.trim()) {
        const creatorId = state.filters.creatorId.toLowerCase().trim()
        filtered = filtered.filter(item => 
          item.creatorId.toLowerCase().includes(creatorId)
        )
      }

      // 关键词过滤
      filtered = interactor.filterByKeyword(filtered, state.filters.keyword)

      return filtered
    })
  }

  // 动作处理
  const actions: IAuthCodeManageActions = {
    // 筛选相关 - 客户端实时过滤，无需重新请求
    handleStatusChange: () => {
      // 状态过滤由计算属性处理，无需额外操作
    },
    
    handleCreatorChange: () => {
      // 创建人过滤由计算属性处理，无需额外操作
    },
    
    handleKeywordInput: () => {
      // 关键词过滤由计算属性处理，无需额外操作
    },
    
    handleSearch: () => {
      // 客户端过滤，无需重新请求，直接刷新数据
      loadAuthCodeList()
    },
    
    handleReset: () => {
      state.filters = converter.resetFilters()
    },
    
    // 列表操作
    handleRefresh: () => {
      loadAuthCodeList()
    },
    
    copyAuthCode: async (authCode: string) => {
      try {
        await interactor.copyToClipboard(authCode)
        ElMessage.success('授权码已复制到剪贴板')
      } catch (error) {
        ElMessage.error('复制失败，请手动复制')
      }
    },
    
    // 生成授权码操作
    handleShowGenerateDialog: () => {
      state.generateDialog.visible = true
      state.generateDialog.form = converter.resetGenerateForm()
    },
    
    handleGenerateCancel: () => {
      state.generateDialog.visible = false
    },
    
    handleGenerateConfirm: async () => {
      if (!actions.validateForm()) {
        return
      }
      
      try {
        state.generateDialog.loading = true
        const params = converter.toGenerateParams(state.generateDialog.form)
        const result = await interactor.generateAuthCode(params)
        
        ElMessage.success('授权码生成成功')
        state.generateDialog.visible = false
        
        // 刷新列表
        loadAuthCodeList()
      } catch (error) {
        ElMessage.error(error instanceof Error ? error.message : '生成授权码失败')
      } finally {
        state.generateDialog.loading = false
      }
    },
    
    validateForm: () => {
      // 所有字段都是选填，暂时不需要验证
      // 如果后续需要验证其他字段，可以在这里添加
      return true
    }
  }

  // 加载授权码列表
  const loadAuthCodeList = async () => {
    try {
      state.loading = true
      const query = converter.toQueryParams(state)
      const data = await interactor.getAuthCodeList(query)
      const newState = converter.toViewState(data)
      Object.assign(state, newState)
    } catch (error) {
      ElMessage.error(error instanceof Error ? error.message : '加载列表失败')
    } finally {
      state.loading = false
    }
  }

  // 生命周期
  onMounted(() => {
    loadAuthCodeList()
  })

  return {
    state,
    computes,
    actions
  }
} 