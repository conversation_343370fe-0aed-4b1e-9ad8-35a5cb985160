import type { ComputedRef } from 'vue'
import type { AuthCodeItem } from '@/entities/store/license'

// 生成授权码表单数据结构
export interface GenerateAuthForm {
  contractNo: string
  contractName: string
  remark: string
}

// UI状态接口
export interface IAuthCodeManageState {
  // 列表数据
  list: AuthCodeItem[]
  loading: boolean
  
  // 筛选条件
  filters: {
    status: number | null     // 状态过滤：null-全部 0-未使用 1-已使用 2-已过期
    creatorId: string         // 创建人ID
    keyword: string           // 关键词搜索
  }

  // 生成授权码弹窗
  generateDialog: {
    visible: boolean
    loading: boolean
    form: GenerateAuthForm
  }
}

// UI计算属性接口
export interface IAuthCodeManageComputed {
  // 是否为空列表
  isEmpty: ComputedRef<boolean>
  // 获取状态类型（Element Plus Tag类型）
  getStatusType: (status: number) => string
  // 获取状态文本
  getStatusText: (status: number) => string
  // 格式化日期时间戳
  formatTimestamp: (timestamp: number) => string
  // 格式化剩余天数
  formatRemainDays: (expireTimestamp: number) => string
  // 检查是否过期
  isExpired: (expireTimestamp: number) => boolean
  // 过滤后的列表
  filteredList: ComputedRef<AuthCodeItem[]>
}

// UI动作接口
export interface IAuthCodeManageActions {
  // 筛选相关
  handleStatusChange: () => void
  handleCreatorChange: () => void
  handleKeywordInput: () => void
  handleSearch: () => void
  handleReset: () => void
  
  // 列表操作
  handleRefresh: () => void
  copyAuthCode: (authCode: string) => Promise<void>
  
  // 生成授权码操作
  handleShowGenerateDialog: () => void
  handleGenerateCancel: () => void
  handleGenerateConfirm: () => Promise<void>
  
  // 表单验证
  validateForm: () => boolean
}

// 组合接口
export interface IAuthCodeManageViewModel {
  state: IAuthCodeManageState
  computes: IAuthCodeManageComputed
  actions: IAuthCodeManageActions
} 