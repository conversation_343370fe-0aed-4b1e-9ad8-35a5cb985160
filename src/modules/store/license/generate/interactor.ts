import { generateAuth, queryAuthCodesList } from '@/api/store/license'
import { useAuthStore } from '@/shared/store/authStore'
import type { 
  GenerateAuthReq, 
  GenerateAuthRes, 
  QueryAuthCodesReq, 
  AuthCodeItem 
} from '@/entities/store/license'

// 授权码列表查询参数
export interface AuthCodeListQuery {
  status?: number
  creatorId?: string
}

// 生成授权码参数
export interface GenerateAuthParams {
  validPeriod: number
  contractNo: string
  contractName: string
  remark?: string
}

export class AuthCodeManageInteractor {
  /**
   * 获取授权码列表
   */
  async getAuthCodeList(query: AuthCodeListQuery): Promise<AuthCodeItem[]> {
    try {
      const params: QueryAuthCodesReq = {
        status: query.status,
        creatorId: query.creatorId || undefined
      }
      
      // request工具已经处理了响应，成功时直接返回data数组
      const data = await queryAuthCodesList(params)
      return data || []
    } catch (error) {
      console.error('获取授权码列表失败:', error)
      // 提供更详细的错误信息
      if (error instanceof Error) {
        throw new Error(`获取授权码列表失败: ${error.message}`)
      }
      throw new Error('获取授权码列表失败')
    }
  }

  /**
   * 生成授权码
   */
  async generateAuthCode(params: GenerateAuthParams): Promise<GenerateAuthRes> {
    try {
      // 获取当前登录用户信息
      const authStore = useAuthStore()
      const currentUserName = authStore.userName // 使用用户名而不是用户ID
      
      const reqData: GenerateAuthReq = {
        // venue_id 不传，创建时不绑定门店
        valid_period: params.validPeriod,
        contractNo: params.contractNo,
        contractName: params.contractName,
        remark: params.remark,
        creator_id: currentUserName // 使用当前登录用户名
      }
      
      return await generateAuth(reqData)
    } catch (error) {
      console.error('生成授权码失败:', error)
      if (error instanceof Error) {
        throw new Error(`生成授权码失败: ${error.message}`)
      }
      throw new Error('生成授权码失败')
    }
  }

  /**
   * 复制文本到剪贴板
   */
  async copyToClipboard(text: string): Promise<void> {
    try {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        await navigator.clipboard.writeText(text)
      } else {
        // 降级方案：使用传统的复制方法
        const textArea = document.createElement('textarea')
        textArea.value = text
        textArea.style.position = 'fixed'
        textArea.style.left = '-9999px'
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
      }
    } catch (error) {
      console.error('复制失败:', error)
      throw new Error('复制失败')
    }
  }

  /**
   * 格式化时间戳
   */
  formatTimestamp(timestamp: number): string {
    if (!timestamp || timestamp === 0) return '-'
    
    // 处理不同格式的时间戳
    let date: Date
    if (timestamp.toString().length === 14) {
      // 格式：20250226155228
      const timeStr = timestamp.toString()
      const year = parseInt(timeStr.substring(0, 4))
      const month = parseInt(timeStr.substring(4, 6)) - 1 // 月份从0开始
      const day = parseInt(timeStr.substring(6, 8))
      const hour = parseInt(timeStr.substring(8, 10))
      const minute = parseInt(timeStr.substring(10, 12))
      const second = parseInt(timeStr.substring(12, 14))
      date = new Date(year, month, day, hour, minute, second)
    } else {
      // 标准时间戳
      date = new Date(timestamp * 1000)
    }
    
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  /**
   * 计算剩余天数
   */
  calculateRemainDays(expireTimestamp: number): number {
    if (!expireTimestamp || expireTimestamp === 0) return 0
    
    let expireDate: Date
    if (expireTimestamp.toString().length === 14) {
      // 格式：20250328155228
      const timeStr = expireTimestamp.toString()
      const year = parseInt(timeStr.substring(0, 4))
      const month = parseInt(timeStr.substring(4, 6)) - 1
      const day = parseInt(timeStr.substring(6, 8))
      const hour = parseInt(timeStr.substring(8, 10))
      const minute = parseInt(timeStr.substring(10, 12))
      const second = parseInt(timeStr.substring(12, 14))
      expireDate = new Date(year, month, day, hour, minute, second)
    } else {
      expireDate = new Date(expireTimestamp * 1000)
    }
    
    const now = new Date()
    const diffTime = expireDate.getTime() - now.getTime()
    return Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  }

  /**
   * 检查是否过期
   */
  isExpired(expireTimestamp: number): boolean {
    return this.calculateRemainDays(expireTimestamp) <= 0
  }

  /**
   * 根据关键词过滤列表
   */
  filterByKeyword(list: AuthCodeItem[], keyword: string): AuthCodeItem[] {
    if (!keyword.trim()) return list
    
    const lowerKeyword = keyword.toLowerCase().trim()
    return list.filter(item => 
      item.venueId.toLowerCase().includes(lowerKeyword) ||
      item.authCode.toLowerCase().includes(lowerKeyword) ||
      item.contractNo.toLowerCase().includes(lowerKeyword) ||
      item.contractName.toLowerCase().includes(lowerKeyword) ||
      item.creatorId.toLowerCase().includes(lowerKeyword) ||
      item.remark.toLowerCase().includes(lowerKeyword)
    )
  }
}

export function useAuthCodeManageInteractor() {
  return new AuthCodeManageInteractor()
} 