import type { 
  IAuthCodeManageState, 
  GenerateAuthForm 
} from './viewmodel'
import type { 
  AuthCodeItem
} from '@/entities/store/license'
import type { AuthCodeListQuery, GenerateAuthParams } from './interactor'

export class AuthCodeManageConverter {
  /**
   * 创建初始状态
   */
  createInitialState(): IAuthCodeManageState {
    return {
      list: [],
      loading: false,
      filters: {
        status: null,
        creatorId: '',
        keyword: ''
      },
      generateDialog: {
        visible: false,
        loading: false,
        form: {
          contractNo: '',
          contractName: '',
          remark: ''
        }
      }
    }
  }

  /**
   * 转换API响应为视图状态
   */
  toViewState(data: AuthCodeItem[]): Partial<IAuthCodeManageState> {
    return {
      list: data || [],
      loading: false
    }
  }

  /**
   * 转换筛选条件为查询参数
   */
  toQueryParams(state: IAuthCodeManageState): AuthCodeListQuery {
    const { filters } = state
    
    return {
      status: filters.status ?? undefined,
      creatorId: filters.creatorId || undefined
    }
  }

  /**
   * 转换表单数据为生成授权码参数
   */
  toGenerateParams(form: GenerateAuthForm): GenerateAuthParams {
    return {
      validPeriod: 36135, // 固定99年 (99 * 365 = 36135天)
      contractNo: form.contractNo.trim(),
      contractName: form.contractName.trim(),
      remark: form.remark.trim() || undefined
    }
  }

  /**
   * 重置表单
   */
  resetGenerateForm(): GenerateAuthForm {
    return {
      contractNo: '',
      contractName: '',
      remark: ''
    }
  }

  /**
   * 重置筛选条件
   */
  resetFilters(): IAuthCodeManageState['filters'] {
    return {
      status: null,
      creatorId: '',
      keyword: ''
    }
  }

  /**
   * 获取状态显示信息
   */
  getStatusInfo(status: number): { text: string; type: string } {
    switch (status) {
      case 0:
        return { text: '未使用', type: 'info' }
      case 1:
        return { text: '已使用', type: 'success' }
      case 2:
        return { text: '已过期', type: 'danger' }
      default:
        return { text: '未知', type: 'warning' }
    }
  }
}

export function useAuthCodeManageConverter() {
  return new AuthCodeManageConverter()
} 