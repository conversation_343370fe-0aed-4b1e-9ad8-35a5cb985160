// 数据导入模块 - 主入口文件

// 核心类型和接口
export type { 
  DataImportPlugin,
  PluginInfo,
  FieldConfig,
  ValidationRule,
  TemplateData,
  ValidationResult,
  ImportResult,
  PluginManager,
  PluginState
} from './core/types'

// 基础类
export {
  BaseDataImportPlugin,
  BaseTemplateGenerator,
  BaseDataValidator,
  BaseDataPreviewer,
  BaseDataImporter
} from './core/BasePlugin'

// 插件管理器
export { DataImportPluginManager, pluginManager } from './core/PluginManager'

// 插件注册系统
export { PluginRegistry, pluginRegistry } from './core/PluginRegistry'

// 工具函数
import { pluginRegistry as registry } from './core/PluginRegistry'

export const getEnabledPlugins = () => {
  return registry.getEnabledPlugins()
}

export const getPlugin = (pluginId: string) => {
  return registry.getPlugin(pluginId)
}

export const getAllPlugins = () => {
  return registry.getRegisteredPlugins()
}

// 插件状态管理
export const enablePlugin = (pluginId: string) => {
  registry.enablePlugin(pluginId)
}

export const disablePlugin = (pluginId: string) => {
  registry.disablePlugin(pluginId)
}

// 模块信息
export const MODULE_INFO = {
  name: 'data-import',
  displayName: '数据导入',
  version: '1.0.0',
  description: '基于插件化架构的数据导入模块',
  author: 'ERP System'
} 