<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航 -->
    <div class="bg-white border-b border-gray-200 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-6 py-4">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <el-button 
              @click="vm.actions.goBack" 
              type="text" 
              :icon="ArrowLeft"
            >
              返回
            </el-button>
            <div class="h-6 w-px bg-gray-300"></div>
            <div class="flex items-center gap-2">
              <el-icon class="text-blue-600" :size="20">
                <component :is="vm.computed.pluginIcon.value || 'Upload'" />
              </el-icon>
              <h1 class="text-xl font-semibold text-gray-900">
                {{ vm.computed.pluginDisplayName.value || '数据导入' }}
              </h1>
            </div>
          </div>
          
          <!-- 步骤指示器 -->
          <div class="flex items-center gap-4">
            <div 
              v-for="(step, index) in vm.state.steps.value" 
              :key="step.key"
              class="flex items-center gap-2"
            >
              <div 
                :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  index <= vm.state.currentStep.value 
                    ? 'bg-blue-600 text-white' 
                    : 'bg-gray-200 text-gray-500'
                ]"
              >
                {{ index + 1 }}
              </div>
              <span 
                :class="[
                  'text-sm font-medium',
                  index <= vm.state.currentStep.value ? 'text-gray-900' : 'text-gray-500'
                ]"
              >
                {{ step.title }}
              </span>
              <div 
                v-if="index < vm.state.steps.value.length - 1" 
                class="w-8 h-px bg-gray-200 ml-2"
              ></div>
            </div>
          </div>
        </div>
        
        <!-- 进度条 -->
        <div class="mt-4">
          <el-progress 
            :percentage="vm.computed.progressPercentage.value" 
            :show-text="false"
            :stroke-width="3"
            color="#409EFF"
          />
        </div>
      </div>
    </div>

    <!-- 主内容区域 -->
    <div class="max-w-7xl mx-auto px-6 py-8">
      <div class="bg-white rounded-lg shadow-sm border border-gray-200 min-h-[600px]">
        <div class="p-6">
          <!-- 动态步骤内容 -->
          <component 
            :is="vm.computed.currentStepComponent.value" 
            v-if="vm.computed.currentStepComponent.value"
            v-bind="vm.computed.currentStepProps.value"
            @update:step-data="vm.actions.handleStepDataUpdate"
            @next="vm.actions.nextStep"
            @prev="vm.actions.prevStep"
            @complete="vm.actions.handleComplete"
          />
          
          <!-- 加载状态 -->
          <div v-else class="flex items-center justify-center py-12">
            <el-icon class="animate-spin text-2xl text-blue-600 mr-3">
              <Loading />
            </el-icon>
            <span class="text-gray-600">加载中...</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bg-white border-t border-gray-200 py-4 sticky bottom-0">
      <div class="max-w-7xl mx-auto px-6">
        <div class="flex justify-between items-center">
          <div>
            <el-button 
              v-if="vm.state.currentStep.value > 0 && !vm.computed.isLastStep.value" 
              @click="vm.actions.prevStep"
              :disabled="vm.state.isProcessing.value"
            >
              上一步
            </el-button>
          </div>
          <div class="flex gap-3">
            <!-- 处理状态指示 -->
            <div v-if="vm.state.isProcessing.value" class="flex items-center gap-2 text-blue-600 mr-3">
              <el-icon class="animate-spin"><Loading /></el-icon>
              <span class="text-sm">处理中...</span>
            </div>
            
            <el-button 
              v-if="!vm.computed.isLastStep.value" 
              type="primary" 
              @click="vm.actions.nextStep"
              :disabled="!vm.computed.canProceed.value || vm.state.isProcessing.value"
              :loading="vm.state.isProcessing.value"
            >
              {{ vm.state.isProcessing.value ? '处理中...' : '下一步' }}
            </el-button>
            <el-button 
              v-if="vm.computed.isLastStep.value" 
              type="primary" 
              @click="vm.actions.handleComplete"
              :disabled="!vm.computed.canProceed.value || vm.state.isProcessing.value"
              :loading="vm.state.isProcessing.value"
            >
              {{ vm.state.isProcessing.value ? '导入中...' : '完成导入' }}
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ArrowLeft, Upload, Loading } from '@element-plus/icons-vue'
import { useImportWizardPresenter } from './presenter'
import type { IImportWizardViewModel } from './viewmodel'

// 获取 ViewModel 实例 - 这是VIPER架构中的View层与Presenter层的连接点
const vm: IImportWizardViewModel = useImportWizardPresenter()

// 辅助函数：获取步骤状态样式
const getStepStatus = (index: number) => {
  const currentStep = vm.state.currentStep.value
  if (index < currentStep) return 'finish'
  if (index === currentStep) return 'process'
  return 'wait'
}
</script>

<style scoped>
.import-wizard {
  min-height: 100vh;
  background-color: #f9fafb;
}

.wizard-header {
  background: white;
  border-bottom: 1px solid #e5e7eb;
  position: sticky;
  top: 0;
  z-index: 10;
}

.wizard-content {
  flex: 1;
  padding: 2rem 0;
}

.wizard-footer {
  background: white;
  border-top: 1px solid #e5e7eb;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

/* 动画效果 */
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .wizard-header .flex {
    flex-direction: column;
    gap: 1rem;
  }
  
  .wizard-footer .flex {
    flex-direction: column;
    gap: 1rem;
  }
}
</style> 