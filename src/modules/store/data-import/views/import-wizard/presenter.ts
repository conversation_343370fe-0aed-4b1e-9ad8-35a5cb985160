import { computed, onMounted, onUnmounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import type { 
  IImportWizardViewModel, 
  IImportWizardState, 
  IImportWizardComputed, 
  IImportWizardActions
} from './viewmodel'
import { useImportWizardInteractor, ImportWizardInteractor } from './interactor'
import { ImportWizardConverter, type PluginDisplayData, type StepProgressData } from './converter'
import { useDataImportState, ImportStep, type StepConfig } from '../../stores/dataImportState'

// 默认步骤组件
import StepSelectStore from '../../components/steps/StepSelectStore.vue'
import StepDownloadTemplate from '../../components/steps/StepDownloadTemplate.vue'
import StepUploadFile from '../../components/steps/StepUploadFile.vue'
import StepValidatePreview from '../../components/steps/StepValidatePreview.vue'
import StepImportResult from '../../components/steps/StepImportResult.vue'

/**
 * 导入向导Presenter是核心协调者，实现IViewModel接口。
 * 现在直接使用dataImportState作为唯一的状态管理，移除了pageState的重复功能。
 */
export class ImportWizardPresenter implements IImportWizardViewModel {
  private interactor: ImportWizardInteractor = useImportWizardInteractor()
  private dataImportState = useDataImportState()
  private router = useRouter()
  private route = useRoute()

  // --- IImportWizardState 实现 (直接从 dataImportState 获取) ---
  public state: IImportWizardState = {
    currentStep: computed(() => this.dataImportState.getCurrentSession?.currentStep ?? 0),
    currentPlugin: computed(() => this.dataImportState.getCurrentSession?.plugin ?? null),
    isProcessing: computed(() => this.dataImportState.getCurrentSession?.isProcessing ?? false),
    stepData: computed(() => this.dataImportState.getStepData()),
    steps: computed(() => this.dataImportState.getCurrentSession?.steps ?? [] as StepConfig[])
  }

  // --- IImportWizardComputed 实现 (基于响应式 state 计算) ---
  public computed: IImportWizardComputed = {
    isLastStep: computed((): boolean => {
      return this.state.currentStep.value === this.state.steps.value.length - 1
    }),

    currentStepComponent: computed((): any => {
      const currentStep = this.state.currentStep.value
      const steps = this.state.steps.value
      const step = steps[currentStep]
      
      console.log(`Presenter: currentStepComponent 计算 - 当前步骤: ${currentStep}, 总步骤数: ${steps.length}`)
      console.log(`Presenter: 当前步骤信息:`, step)
      
      if (!step) {
        console.log(`Presenter: 步骤 ${currentStep} 不存在`)
        return null
      }

      // 如果步骤定义了自定义组件，使用自定义组件
      if (step.component) {
        console.log(`Presenter: 使用自定义组件:`, step.component)
        return step.component
      }

      // 否则使用默认组件
      console.log(`Presenter: 使用默认组件，步骤键: ${step.key}`)
      switch (step.key) {
        case 'select-store':
          console.log(`Presenter: 返回 StepSelectStore 组件`)
          return StepSelectStore
        case 'download-template':
          return StepDownloadTemplate
        case 'upload-file':
          return StepUploadFile
        case 'validate-preview':
          return StepValidatePreview
        case 'import-result':
          return StepImportResult
        default:
          console.log(`Presenter: 未知步骤键: ${step.key}`)
          return null
      }
    }),

    currentStepProps: computed((): Record<string, any> => {
      return ImportWizardConverter.formatStepProps(
        this.state.currentPlugin.value,
        this.state.stepData.value,
        this.state.currentStep.value,
        this.state.isProcessing.value
      )
    }),

    canProceed: computed((): boolean => {
      // 直接使用State中的状态
      return this.dataImportState.currentStepInfo?.canProceed ?? false
    }),

    progressPercentage: computed((): number => {
      const progressData: StepProgressData = ImportWizardConverter.calculateStepProgress(
        this.state.currentStep.value,
        this.state.steps.value
      )
      return progressData.percentage
    }),

    currentStepTitle: computed((): string => {
      const step = this.state.steps.value[this.state.currentStep.value]
      return step?.title || ''
    }),

    pluginDisplayName: computed((): string => {
      const displayData: PluginDisplayData = ImportWizardConverter.formatPluginDisplay(
        this.state.currentPlugin.value
      )
      return displayData.displayName
    }),

    pluginIcon: computed((): string => {
      const displayData: PluginDisplayData = ImportWizardConverter.formatPluginDisplay(
        this.state.currentPlugin.value
      )
      return displayData.icon
    })
  }

  // --- IImportWizardActions 实现 (业务流程编排) ---
  public actions: IImportWizardActions = {
    nextStep: async (): Promise<void> => {
      if (this.state.isProcessing.value || this.computed.isLastStep.value) return

      this.dataImportState.setProcessing(true)

      try {
        // 执行当前步骤的完成逻辑
        const stepResult = await this.interactor.executeStepCompletion(
          this.state.currentStep.value,
          this.state.steps.value,
          this.state.stepData.value,
          this.state.currentPlugin.value!
        )

        if (!stepResult.success) {
          throw new Error(stepResult.error || '步骤执行失败')
        }

        // 更新步骤数据
        if (stepResult.data) {
          this.dataImportState.updateStepData(stepResult.data)
        }

        // 进入下一步
        const success = this.dataImportState.nextStep()
        if (success) {
          // 更新State中的步骤信息
          const nextStepKey = this.getStepKeyByIndex(this.state.currentStep.value)
          this.dataImportState.updateCurrentStep(this.state.currentStep.value, nextStepKey)
          
          console.log(`Presenter: 进入步骤 ${this.state.currentStep.value}`)
        }

      } catch (error) {
        console.error('Presenter: 下一步失败', error)
        ElMessage.error('操作失败：' + ImportWizardConverter.formatError(error))
      } finally {
        this.dataImportState.setProcessing(false)
      }
    },

    prevStep: (): void => {
      if (this.state.currentStep.value <= 0) return

      const success = this.dataImportState.prevStep()
      if (success) {
        // 更新State中的步骤信息
        const prevStepKey = this.getStepKeyByIndex(this.state.currentStep.value)
        this.dataImportState.updateCurrentStep(this.state.currentStep.value, prevStepKey)
        
        console.log(`Presenter: 返回步骤 ${this.state.currentStep.value}`)
      }
    },

    jumpToStep: (step: number): void => {
      const success = this.dataImportState.jumpToStep(step)
      if (success) {
        // 更新State中的步骤信息
        const stepKey = this.getStepKeyByIndex(step)
        this.dataImportState.updateCurrentStep(step, stepKey)
        
        console.log(`Presenter: 跳转到步骤 ${step}`)
      }
    },

    handleStepDataUpdate: (data: Record<string, any>): void => {
      // 直接更新State状态
      this.dataImportState.updateStepData(data)
      
      console.log('Presenter: 步骤数据更新', data)
    },

    handleComplete: async (): Promise<void> => {
      if (this.state.isProcessing.value) return

      // 检查是否已经完成真实导入（在导入预览页面已经执行过API导入）
      if (this.state.stepData.value.importCompleted) {
        console.log('Presenter: 导入已完成，直接返回首页')
        // 重置导入状态并直接回到导入导航首页
        this.dataImportState.reset()
        this.router.push('/store/data-import')
        return
      }

      // 如果还没有完成导入，提示用户先完成导入流程
      ElMessage.warning('请先完成导入预览步骤')
    },

    loadPlugin: async (): Promise<void> => {
      try {
        console.log('Presenter: 开始加载插件...')
        
        // 解析路由参数
        const routeParams = ImportWizardConverter.parseRouteParams(this.route.params)
        if (!routeParams.isValid) {
          throw new Error(routeParams.error || '路由参数无效')
        }
        console.log('Presenter: 路由参数解析完成', routeParams.pluginId)

        this.dataImportState.setPluginId(routeParams.pluginId)
        console.log('Presenter: 插件ID设置完成')

        // 加载插件
        const pluginResult = await this.interactor.loadPlugin(routeParams.pluginId)
        if (!pluginResult.success) {
          throw new Error(pluginResult.error || '插件加载失败')
        }
        console.log('Presenter: 插件加载完成')

        // 先创建新会话
        const sessionId = this.dataImportState.createSession(routeParams.pluginId, pluginResult.plugin!)
        this.dataImportState.setSessionId(sessionId)
        console.log('Presenter: 会话创建完成', sessionId)

        // 然后设置插件和步骤
        this.dataImportState.setCurrentPlugin(pluginResult.plugin!)
        console.log('Presenter: 插件设置完成')
        
        const steps = this.interactor.generateSteps(pluginResult.plugin!)
        this.dataImportState.setSteps(steps)
        console.log('Presenter: 步骤生成完成', steps.length, '个步骤')

        // 最后设置初始化状态
        this.dataImportState.setInitialized(true)

        console.log(`Presenter: 插件加载成功 - ${routeParams.pluginId}`)

      } catch (error) {
        console.error('Presenter: 插件加载失败', error)
        ElMessage.error(ImportWizardConverter.formatError(error))
        this.router.push('/store/data-import')
      }
    },

    goBack: (): void => {
      // 重置导入状态
      this.dataImportState.reset()
      this.router.push('/store/data-import')
    },

    resetWizard: (): void => {
      this.dataImportState.reset()
      this.actions.loadPlugin()
    },

    initialize: async (): Promise<void> => {
      console.log('Presenter: 初始化导入向导')
      
      // 检查是否有现有会话需要恢复
      if (this.dataImportState.hasActiveSession) {
        const currentSession = this.dataImportState.getCurrentSession!
        const routePluginId = this.route.params.pluginId as string
        
        // 验证当前会话的插件是否匹配
        if (currentSession.pluginId === routePluginId) {
          console.log('Presenter: 恢复现有导入会话')
          this.restoreFromState()
          return
        } else {
          console.log('Presenter: 插件不匹配，创建新会话')
          this.dataImportState.resetCurrentSession()
        }
      }

      // 创建新会话
      await this.actions.loadPlugin()
    },

    cleanup: (): void => {
      console.log('Presenter: 清理资源')
      this.interactor.cleanup()
    }
  }

  // --- 私有辅助方法 ---

  /**
   * 根据步骤索引获取步骤键
   */
  private getStepKeyByIndex(index: number): ImportStep {
    const stepKeys = [
      ImportStep.SELECT_STORE,
      ImportStep.DOWNLOAD_TEMPLATE,
      ImportStep.UPLOAD_FILE,
      ImportStep.VALIDATE_PREVIEW,
      ImportStep.IMPORT_RESULT
    ]
    return stepKeys[index] || ImportStep.SELECT_STORE
  }

  /**
   * 从State恢复状态
   */
  private restoreFromState(): void {
    if (!this.dataImportState.getCurrentSession) return

    const session = this.dataImportState.getCurrentSession
    
    // 恢复插件和步骤配置
    if (session.plugin) {
      this.dataImportState.setCurrentPlugin(session.plugin)
      const steps = this.interactor.generateSteps(session.plugin)
      this.dataImportState.setSteps(steps)
    }

    this.dataImportState.setInitialized(true)
    console.log('Presenter: 会话恢复完成')
  }

  // --- 生命周期钩子 ---
  constructor() {
    // 监听路由参数变化（但不在初始化时立即执行）
    watch(() => this.route.params.pluginId, (newPluginId, oldPluginId) => {
      // 只有在插件ID真正变化时才重新初始化
      if (newPluginId && typeof newPluginId === 'string' && newPluginId !== oldPluginId) {
        console.log(`Presenter: 路由插件变化 ${oldPluginId} -> ${newPluginId}`)
        this.actions.initialize()
      }
    }, { immediate: false })

    onMounted(() => {
      console.log('Presenter: 组件挂载，开始初始化')
      this.actions.initialize()
    })

    onUnmounted(() => {
      this.actions.cleanup()
    })
  }
}

/**
 * Presenter 工厂函数
 */
export function useImportWizardPresenter(): IImportWizardViewModel {
  return new ImportWizardPresenter()
} 