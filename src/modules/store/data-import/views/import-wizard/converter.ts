import type { DataImportPlugin } from '../../core/types'
import type { ImportSession, StepConfig } from '../../stores/dataImportState'

/**
 * 插件信息格式化数据
 */
export interface PluginDisplayData {
  displayName: string
  icon: string
  description: string
}

/**
 * 步骤进度数据
 */
export interface StepProgressData {
  currentStep: number
  totalSteps: number
  percentage: number
  currentStepTitle: string
}

/**
 * Store同步数据格式
 */
export interface StoreSyncData {
  currentStep: number
  stepData: Record<string, any>
  sessionId: string
  pluginId: string
  isProcessing: boolean
}

/**
 * 导入向导数据格式转换器
 * 
 * 职责：
 * 1. Store数据格式转换和处理
 * 2. 插件配置数据格式化
 * 3. 步骤进度计算和格式化
 * 4. 路由参数处理
 */
export class ImportWizardConverter {
  /**
   * 将Store会话数据转换为同步格式
   */
  static toStoreSyncData(session: ImportSession): StoreSyncData {
    return {
      currentStep: session.currentStep,
      stepData: {
        selectedStoreId: session.selectedStoreId,
        selectedStore: session.selectedStore,
        uploadedFile: session.uploadedFile,
        parsedData: session.parsedData,
        validationResult: session.validationResult,
        validationErrors: session.validationErrors,
        validationWarnings: session.validationWarnings,
        importResult: session.importResult,
        ...session.pluginConfig
      },
      sessionId: session.sessionId,
      pluginId: session.pluginId,
      isProcessing: session.isProcessing
    }
  }

  /**
   * 格式化插件显示信息
   */
  static formatPluginDisplay(plugin: DataImportPlugin | null): PluginDisplayData {
    if (!plugin) {
      return {
        displayName: '数据导入',
        icon: 'Upload',
        description: '请选择导入插件'
      }
    }

    return {
      displayName: plugin.displayName || plugin.name,
      icon: plugin.icon || 'Upload',
      description: plugin.description || ''
    }
  }

  /**
   * 计算步骤进度信息
   */
  static calculateStepProgress(
    currentStep: number, 
    steps: StepConfig[]
  ): StepProgressData {
    const totalSteps = steps.length
    const percentage = totalSteps > 0 ? Math.round((currentStep + 1) / totalSteps * 100) : 0
    const currentStepTitle = steps[currentStep]?.title || ''

    return {
      currentStep,
      totalSteps,
      percentage,
      currentStepTitle
    }
  }

  /**
   * 生成默认步骤配置
   */
  static generateDefaultSteps(): StepConfig[] {
    return [
      { 
        key: 'select-store', 
        title: '选择门店', 
        component: null 
      },
      { 
        key: 'download-template', 
        title: '下载模板', 
        component: null 
      },
      { 
        key: 'upload-file', 
        title: '上传文件', 
        component: null 
      },
      { 
        key: 'validate-preview', 
        title: '验证预览', 
        component: null 
      },
      { 
        key: 'import-result', 
        title: '导入结果', 
        component: null 
      }
    ]
  }

  /**
   * 根据插件配置生成步骤
   */
  static generateStepsFromPlugin(plugin: DataImportPlugin): StepConfig[] {
    // 如果插件定义了自定义步骤，使用插件的步骤
    if (plugin.componentConfig?.customSteps) {
      return plugin.componentConfig.customSteps.map(step => ({
        key: step.key,
        title: step.title,
        component: step.component,
        canProceed: step.canProceed
      }))
    }

    // 否则使用默认步骤，但可能替换某些组件
    const defaultSteps = ImportWizardConverter.generateDefaultSteps()
    
    return defaultSteps.map(step => {
      const customComponents = plugin.customComponents
      
      switch (step.key) {
        case 'validate-preview':
          if (customComponents?.validationPanel && plugin.componentConfig?.useCustomValidationPreview) {
            return { ...step, component: customComponents.validationPanel }
          }
          break
        case 'import-result':
          if (customComponents?.resultPanel && plugin.componentConfig?.useCustomResultPage) {
            return { ...step, component: customComponents.resultPanel }
          }
          break
      }
      
      return step
    })
  }

  /**
   * 验证步骤是否可以继续
   */
  static validateCanProceed(
    currentStep: number,
    steps: StepConfig[],
    stepData: Record<string, any>
  ): boolean {
    const step = steps[currentStep]
    if (!step) return false

    // 如果步骤定义了自定义验证逻辑
    if (step.canProceed && typeof step.canProceed === 'function') {
      return step.canProceed(stepData)
    }

    // 默认验证逻辑
    switch (step.key) {
      case 'select-store':
        return !!stepData.selectedStoreId
      case 'download-template':
        return true
      case 'upload-file':
        return !!stepData.uploadedFile && !!stepData.parsedData
      case 'validate-preview':
        return !stepData.validationErrors?.length
      case 'import-result':
        return true
      default:
        return true
    }
  }

  /**
   * 格式化步骤属性数据
   */
  static formatStepProps(
    plugin: DataImportPlugin | null,
    stepData: Record<string, any>,
    currentStep: number,
    isProcessing: boolean
  ): Record<string, any> {
    return {
      plugin,
      stepData,
      currentStep,
      isProcessing
    }
  }

  /**
   * 处理路由参数
   */
  static parseRouteParams(routeParams: Record<string, any>): {
    pluginId: string
    isValid: boolean
    error?: string
  } {
    const pluginId = routeParams.pluginId

    if (!pluginId || typeof pluginId !== 'string') {
      return {
        pluginId: '',
        isValid: false,
        error: '缺少插件ID参数'
      }
    }

    return {
      pluginId,
      isValid: true
    }
  }

  /**
   * 格式化错误信息
   */
  static formatError(error: unknown): string {
    if (error instanceof Error) {
      return error.message
    }
    
    if (typeof error === 'string') {
      return error
    }
    
    return '未知错误'
  }

  /**
   * 生成会话ID
   */
  static generateSessionId(pluginId: string): string {
    return `import_${pluginId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * 验证步骤数据完整性
   */
  static validateStepData(stepData: Record<string, any>): {
    isValid: boolean
    missingFields: string[]
    warnings: string[]
  } {
    const missingFields: string[] = []
    const warnings: string[] = []

    // 检查必要字段
    if (!stepData.selectedStoreId) {
      missingFields.push('selectedStoreId')
    }

    // 检查文件相关
    if (stepData.uploadedFile && !stepData.parsedData) {
      warnings.push('文件已上传但未解析')
    }

    // 检查验证结果
    if (stepData.parsedData && !stepData.validationResult) {
      warnings.push('数据已解析但未验证')
    }

    return {
      isValid: missingFields.length === 0,
      missingFields,
      warnings
    }
  }
} 