import type { ComputedRef, WritableComputedRef, Ref } from 'vue'
import type { DataImportPlugin } from '../../core/types'
import type { StepConfig } from '../../stores/dataImportState'

// 视图直接绑定的状态接口
export interface IImportWizardState {
  readonly currentStep: ComputedRef<number>
  readonly currentPlugin: ComputedRef<DataImportPlugin | null>
  readonly isProcessing: ComputedRef<boolean>
  readonly stepData: ComputedRef<Record<string, any>>
  readonly steps: ComputedRef<StepConfig[]>
}

// 视图使用的计算属性接口
export interface IImportWizardComputed {
  readonly isLastStep: ComputedRef<boolean>
  readonly currentStepComponent: ComputedRef<any>
  readonly currentStepProps: ComputedRef<Record<string, any>>
  readonly canProceed: ComputedRef<boolean>
  readonly progressPercentage: ComputedRef<number>
  readonly currentStepTitle: ComputedRef<string>
  readonly pluginDisplayName: ComputedRef<string>
  readonly pluginIcon: ComputedRef<string>
}

// 视图可触发的操作接口
export interface IImportWizardActions {
  // 步骤控制
  nextStep(): Promise<void>
  prevStep(): void
  jumpToStep(step: number): void
  
  // 数据更新
  handleStepDataUpdate(data: Record<string, any>): void
  
  // 导入操作
  handleComplete(): Promise<void>
  
  // 插件管理
  loadPlugin(): Promise<void>
  
  // 路由操作
  goBack(): void
  resetWizard(): void
  
  // 生命周期
  initialize(): Promise<void>
  cleanup(): void
}

// 组合的视图模型接口
export interface IImportWizardViewModel {
  state: IImportWizardState
  computed: IImportWizardComputed
  actions: IImportWizardActions
} 