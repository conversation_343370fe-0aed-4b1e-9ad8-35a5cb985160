// import-wizard Props和Emits接口定义

// 路由参数接口
export interface ImportWizardRouteParams {
  pluginId: string
}

// 组件Props接口（如果需要的话，当前是路由页面，通常无props）
export interface ImportWizardProps {
  // 暂时无props，通过路由参数获取插件ID
}

// 组件Emits接口（如果需要的话）
export interface ImportWizardEmits {
  // 暂时无emits，导入完成后通过路由跳转
}

// 组件对外暴露的数据结构
export interface ImportWizardExposedData {
  currentStep: number
  currentPlugin: any
  isProcessing: boolean
  stepData: Record<string, any>
}

// 注意：StepConfig 现在在 dataImportState 中定义，避免重复定义 