import { getPlugin } from '../../index'
import type { DataImportPlugin, ImportResult } from '../../core/types'
import { ImportWizardConverter } from './converter'
import type { StepConfig } from '../../stores/dataImportState'

/**
 * 插件加载结果
 */
export interface PluginLoadResult {
  success: boolean
  plugin: DataImportPlugin | null
  error?: string
}

/**
 * 步骤执行结果
 */
export interface StepExecutionResult {
  success: boolean
  data?: Record<string, any>
  error?: string
  canProceed?: boolean
}

/**
 * 导入执行结果
 */
export interface ImportExecutionResult {
  success: boolean
  result?: ImportResult
  error?: string
}

/**
 * 导入向导业务逻辑处理器
 * 
 * 职责：
 * 1. 插件加载和管理
 * 2. 步骤执行逻辑
 * 3. 数据导入操作
 * 4. 错误处理和恢复
 */
export class ImportWizardInteractor {
  /**
   * 加载指定插件
   */
  async loadPlugin(pluginId: string): Promise<PluginLoadResult> {
    try {
      console.log(`Interactor: 开始加载插件 ${pluginId}`)
      
      const plugin = getPlugin(pluginId)
      
      if (!plugin) {
        return {
          success: false,
          plugin: null,
          error: `插件 "${pluginId}" 不存在`
        }
      }

      // 验证插件配置
      const validationResult = this.validatePlugin(plugin)
      if (!validationResult.isValid) {
        return {
          success: false,
          plugin: null,
          error: `插件配置无效: ${validationResult.errors.join(', ')}`
        }
      }

      console.log(`Interactor: 插件 ${pluginId} 加载成功`)
      return {
        success: true,
        plugin
      }
    } catch (error) {
      console.error(`Interactor: 插件加载失败`, error)
      return {
        success: false,
        plugin: null,
        error: ImportWizardConverter.formatError(error)
      }
    }
  }

  /**
   * 验证插件配置
   */
  validatePlugin(plugin: DataImportPlugin): {
    isValid: boolean
    errors: string[]
  } {
    const errors: string[] = []

    // 检查必要属性
    if (!plugin.id) {
      errors.push('插件ID不能为空')
    }

    if (!plugin.name) {
      errors.push('插件名称不能为空')
    }

    // 检查必要组件
    if (!plugin.templateGenerator) {
      errors.push('缺少模板生成器')
    }

    if (!plugin.dataValidator) {
      errors.push('缺少数据验证器')
    }

    if (!plugin.dataPreviewer) {
      errors.push('缺少数据预览器')
    }

    if (!plugin.dataImporter) {
      errors.push('缺少数据导入器')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 生成步骤配置
   */
  generateSteps(plugin: DataImportPlugin): StepConfig[] {
    return ImportWizardConverter.generateStepsFromPlugin(plugin)
  }

  /**
   * 执行步骤完成逻辑
   */
  async executeStepCompletion(
    currentStep: number,
    steps: StepConfig[],
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): Promise<StepExecutionResult> {
    try {
      const step = steps[currentStep]
      if (!step) {
        return {
          success: false,
          error: '无效的步骤'
        }
      }

      console.log(`Interactor: 执行步骤完成逻辑 - ${step.key}`)

      switch (step.key) {
        case 'select-store':
          return this.executeSelectStoreCompletion(stepData)
        
        case 'download-template':
          return this.executeDownloadTemplateCompletion(stepData, plugin)
        
        case 'upload-file':
          return this.executeUploadFileCompletion(stepData, plugin)
        
        case 'validate-preview':
          return this.executeValidatePreviewCompletion(stepData, plugin)
        
        case 'import-preview':
          return this.executeImportPreviewCompletion(stepData, plugin)
        
        case 'import-result':
          return this.executeImportResultCompletion(stepData)
        
        default:
          // 对于自定义步骤，如果插件有beforeImport钩子，可以在这里调用
          // 目前暂时返回成功，后续可以扩展自定义步骤处理逻辑
          return {
            success: true,
            data: {}
          }
      }
    } catch (error) {
      console.error(`Interactor: 步骤执行失败`, error)
      return {
        success: false,
        error: ImportWizardConverter.formatError(error)
      }
    }
  }

  /**
   * 执行门店选择完成逻辑
   */
  private executeSelectStoreCompletion(stepData: Record<string, any>): StepExecutionResult {
    if (!stepData.selectedStoreId) {
      return {
        success: false,
        error: '请选择门店'
      }
    }

    return {
      success: true,
      data: {
        selectedStoreId: stepData.selectedStoreId,
        selectedStore: stepData.selectedStore
      },
      canProceed: true
    }
  }

  /**
   * 执行模板下载完成逻辑
   */
  private executeDownloadTemplateCompletion(
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): StepExecutionResult {
    // 模板下载步骤通常不需要特殊处理，直接允许继续
    return {
      success: true,
      data: {},
      canProceed: true
    }
  }

  /**
   * 执行文件上传完成逻辑
   */
  private async executeUploadFileCompletion(
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): Promise<StepExecutionResult> {
    if (!stepData.uploadedFile) {
      return {
        success: false,
        error: '请上传文件'
      }
    }

    if (!stepData.parsedData) {
      return {
        success: false,
        error: '文件解析失败'
      }
    }

    // 可以在这里添加额外的文件验证逻辑
    const fileValidation = this.validateUploadedFile(stepData.uploadedFile, stepData.parsedData)
    if (!fileValidation.isValid) {
      return {
        success: false,
        error: fileValidation.error
      }
    }

    return {
      success: true,
      data: {
        uploadedFile: stepData.uploadedFile,
        parsedData: stepData.parsedData
      },
      canProceed: true
    }
  }

  /**
   * 执行验证预览完成逻辑
   */
  private executeValidatePreviewCompletion(
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): StepExecutionResult {
    if (!stepData.validationResult) {
      return {
        success: false,
        error: '数据验证未完成'
      }
    }

    // 检查是否有阻断性错误
    const hasBlockingErrors = stepData.validationErrors?.some((error: any) => 
      error.severity === 'error' || error.blocking === true
    )

    if (hasBlockingErrors) {
      return {
        success: false,
        error: '存在阻断性错误，请修正后重试'
      }
    }

    return {
      success: true,
      data: {
        validationResult: stepData.validationResult,
        validationErrors: stepData.validationErrors,
        validationWarnings: stepData.validationWarnings
      },
      canProceed: true
    }
  }

  /**
   * 执行导入预览完成逻辑
   */
  private executeImportPreviewCompletion(
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): StepExecutionResult {
    if (!stepData.stockImportPreview) {
      return {
        success: false,
        error: '预览数据不存在'
      }
    }

    // 检查用户是否确认导入
    if (!stepData.confirmImport) {
      return {
        success: false,
        error: '请确认导入操作'
      }
    }

    return {
      success: true,
      data: {
        stockImportPreview: stepData.stockImportPreview,
        confirmImport: stepData.confirmImport
      },
      canProceed: true
    }
  }

  /**
   * 执行导入结果完成逻辑
   */
  private executeImportResultCompletion(stepData: Record<string, any>): StepExecutionResult {
    // 导入结果步骤是最后一步，通常不需要特殊处理
    return {
      success: true,
      data: {
        importResult: stepData.importResult
      },
      canProceed: false // 最后一步，不能继续
    }
  }

  /**
   * 验证上传的文件
   */
  private validateUploadedFile(file: File, parsedData: any): {
    isValid: boolean
    error?: string
  } {
    // 文件大小检查（10MB限制）
    const maxSize = 10 * 1024 * 1024
    if (file.size > maxSize) {
      return {
        isValid: false,
        error: '文件大小不能超过10MB'
      }
    }

    // 数据行数检查
    if (parsedData.data && parsedData.data.length > 10000) {
      return {
        isValid: false,
        error: '数据行数不能超过10,000行'
      }
    }

    // 检查是否有数据
    if (!parsedData.data || parsedData.data.length === 0) {
      return {
        isValid: false,
        error: '文件中没有有效数据'
      }
    }

    return {
      isValid: true
    }
  }

  /**
   * 执行数据导入
   */
  async executeImport(
    stepData: Record<string, any>,
    plugin: DataImportPlugin
  ): Promise<ImportExecutionResult> {
    try {
      console.log(`Interactor: 开始执行数据导入`)

      // 验证导入前提条件
      const preConditionCheck = this.validateImportPreConditions(stepData)
      if (!preConditionCheck.isValid) {
        return {
          success: false,
          error: preConditionCheck.error
        }
      }

      // 过滤有效数据
      const validData = this.filterValidData(stepData)

      // 执行导入
      const result = await plugin.dataImporter.import(
        validData,
        stepData.selectedStoreId
      )

      // 执行导入后处理
      if (plugin.afterImport) {
        await plugin.afterImport(result)
      }

      console.log(`Interactor: 数据导入完成`, result)
      
      return {
        success: true,
        result
      }
    } catch (error) {
      console.error(`Interactor: 数据导入失败`, error)
      return {
        success: false,
        error: ImportWizardConverter.formatError(error)
      }
    }
  }

  /**
   * 验证导入前提条件
   */
  private validateImportPreConditions(stepData: Record<string, any>): {
    isValid: boolean
    error?: string
  } {
    if (!stepData.selectedStoreId) {
      return {
        isValid: false,
        error: '未选择门店'
      }
    }

    if (!stepData.parsedData || !stepData.parsedData.data) {
      return {
        isValid: false,
        error: '没有可导入的数据'
      }
    }

    if (!stepData.validationResult) {
      return {
        isValid: false,
        error: '数据未经过验证'
      }
    }

    return {
      isValid: true
    }
  }

  /**
   * 过滤有效数据
   */
  private filterValidData(stepData: Record<string, any>): any[] {
    const allData = stepData.parsedData.data
    const errors = stepData.validationErrors || []

    // 过滤掉有错误的行
    return allData.filter((_: any, index: number) => 
      !errors.some((error: any) => error.row === index && error.severity === 'error')
    )
  }

  /**
   * 清理资源
   */
  cleanup(): void {
    console.log(`Interactor: 清理资源`)
    // 在这里可以清理临时文件、取消未完成的请求等
  }
}

/**
 * 工厂函数获取 Interactor 实例
 */
export function useImportWizardInteractor(): ImportWizardInteractor {
  return new ImportWizardInteractor()
} 