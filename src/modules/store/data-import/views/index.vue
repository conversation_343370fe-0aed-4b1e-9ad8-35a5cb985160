<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 页面头部 -->
    <div class="bg-white border-b border-gray-200 py-6">
      <div class="max-w-7xl mx-auto px-6">
        <div class="mb-4">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item :to="{ path: '/dashboard' }">控制台</el-breadcrumb-item>
            <el-breadcrumb-item>数据导入</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <div>
          <h1 class="text-2xl font-semibold text-gray-900 mb-2">数据导入</h1>
          <p class="text-gray-600">选择要导入的数据类型，批量导入门店业务数据</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="py-6">
      <div class="max-w-7xl mx-auto px-6">
        <!-- 插件卡片区域 -->
        <div class="mb-8">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            <div
              v-for="plugin in enabledPlugins"
              :key="plugin.id"
              class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 cursor-pointer transition-all hover:shadow-md hover:-translate-y-1"
              @click="navigateToPlugin(plugin.id)"
            >
              <div class="flex items-center justify-center w-16 h-16 bg-gray-100 rounded-xl mb-4 text-gray-600">
                <el-icon :size="32">
                  <component :is="getPluginIcon(plugin.icon)" />
                </el-icon>
              </div>
              <div class="mb-4">
                <h3 class="text-lg font-semibold text-gray-900 mb-2">{{ plugin.displayName }}</h3>
                <p class="text-gray-600 text-sm leading-relaxed">{{ plugin.description }}</p>
              </div>
                          <div class="flex justify-between items-center">
              <span class="text-xs text-gray-500">v{{ plugin.version }}</span>
              <div class="flex gap-2">
                <el-button size="small" @click.stop="downloadTemplate(plugin)">
                  下载模板
                </el-button>
                <el-button type="primary" size="small">
                  开始导入
                </el-button>
              </div>
            </div>
            </div>
          </div>
        </div>
        

      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { House, Box, User, Goods, Coffee, Files } from '@element-plus/icons-vue'
import { getEnabledPlugins } from '../index'
import type { DataImportPlugin } from '../core/types'
import { generateTemplateCSV } from '../core/utils'
import { ElMessage } from 'element-plus'

const router = useRouter()

// 响应式数据
const enabledPlugins = ref<DataImportPlugin[]>([])

// 图标映射
const iconMap = {
  House,
  Box,
  User,
  Goods,
  Wine: Coffee,
  Package: Files
}

// 获取插件图标组件
const getPluginIcon = (iconName: string) => {
  return iconMap[iconName as keyof typeof iconMap] || Box
}

// 加载插件数据
const loadPlugins = () => {
  try {
    enabledPlugins.value = getEnabledPlugins()
    console.log('已加载插件:', enabledPlugins.value.length, '个')
    console.log('插件详情:', enabledPlugins.value.map(p => ({ id: p.id, name: p.displayName, enabled: p.enabled })))
  } catch (error) {
    console.error('加载插件失败:', error)
  }
}

// 下载模板
const downloadTemplate = (plugin: DataImportPlugin) => {
  try {
    const templateData = plugin.templateGenerator.getTemplateData()
    generateTemplateCSV(templateData, plugin.displayName)
    ElMessage.success(`${plugin.displayName}模板下载成功`)
  } catch (error) {
    console.error('模板下载失败:', error)
    ElMessage.error(`${plugin.displayName}模板下载失败`)
  }
}

// 导航到插件页面
const navigateToPlugin = (pluginId: string) => {
  router.push(`/store/data-import/${pluginId}`)
}

onMounted(() => {
  loadPlugins()
})
</script>

 