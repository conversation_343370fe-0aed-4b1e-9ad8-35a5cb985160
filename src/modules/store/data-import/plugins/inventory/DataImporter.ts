// 库存数据导入 - 数据导入器

import type { DataImporter, ImportResult } from '../../core/types'

/**
 * 库存数据导入器
 */
export class InventoryDataImporter implements DataImporter {
  private progressCallback?: (progress: number) => void
  private isCancelled = false
  private currentProgress = 0

  async import(data: any[], storeId: string): Promise<ImportResult> {
    this.resetImportState()
    
    const startTime = Date.now()
    let successCount = 0
    let errorCount = 0
    const errors: any[] = []

    try {
      for (let i = 0; i < data.length; i++) {
        if (this.checkCancellation()) {
          break
        }

        const row = data[i]
        
        try {
          // 模拟导入过程
          await this.simulateImportRow(row, storeId)
          successCount++
        } catch (error) {
          errorCount++
          errors.push({
            row: i + 1,
            field: '',
            value: '',
            message: (error as Error).message,
            type: 'error'
          })
        }

        // 更新进度
        this.updateProgress((i + 1) / data.length * 100)
      }

      const executionTime = Date.now() - startTime

      return {
        success: errorCount === 0,
        totalRows: data.length,
        successRows: successCount,
        errorRows: errorCount,
        warnings: [],
        errors,
        executionTime,
        timestamp: Date.now(),
        summary: errorCount === 0 
          ? `成功导入 ${successCount} 条库存记录`
          : `导入完成，成功 ${successCount} 条，失败 ${errorCount} 条`
      }
    } catch (error) {
      return {
        success: false,
        totalRows: data.length,
        successRows: successCount,
        errorRows: data.length - successCount,
        warnings: [],
        errors: [{
          row: 0,
          field: '',
          value: '',
          message: `导入过程中发生错误：${(error as Error).message}`,
          type: 'error'
        }],
        executionTime: Date.now() - startTime,
        timestamp: Date.now(),
        summary: '导入失败'
      }
    }
  }

  cancel(): void {
    this.isCancelled = true
  }

  getProgress(): number {
    return this.currentProgress
  }

  onProgress(callback: (progress: number) => void): void {
    this.progressCallback = callback
  }

  private resetImportState(): void {
    this.isCancelled = false
    this.currentProgress = 0
  }

  private checkCancellation(): boolean {
    return this.isCancelled
  }

  private updateProgress(progress: number): void {
    this.currentProgress = Math.min(100, Math.max(0, progress))
    this.progressCallback?.(this.currentProgress)
  }

  private async simulateImportRow(row: any, storeId: string): Promise<void> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 50))
    
    // 模拟随机失败（10%概率）
    if (Math.random() < 0.1) {
      throw new Error(`商品 ${row['商品编号']} 导入失败：库存更新异常`)
    }

    // 模拟库存业务逻辑
    const productCode = row['商品编号']
    const stockQuantity = parseInt(row['库存数量']) || 0
    const safeStock = parseInt(row['安全库存']) || 0

    console.log(`导入库存: 门店${storeId}, 商品${productCode}, 库存${stockQuantity}, 安全库存${safeStock}`)
  }
} 