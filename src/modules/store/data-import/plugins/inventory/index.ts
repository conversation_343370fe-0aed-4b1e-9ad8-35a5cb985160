// 库存数据导入插件 (新架构)

import { defineAsyncComponent } from 'vue'
import type { DataImportPlugin } from '../../core/types'

// 核心功能模块
import { InventoryTemplateGenerator } from './TemplateGenerator'
import { InventoryDataValidator } from './DataValidator'
import { InventoryDataPreviewer } from './DataPreviewer'
import { InventoryDataImporter } from './DataImporter'

// 自定义组件
const InventoryValidationPanel = defineAsyncComponent(() => import('./components/inventory-validation/index.vue'))
const InventoryPreviewPanel = defineAsyncComponent(() => import('./components/InventoryPreviewPanel.vue'))
const InventoryImportResultPanel = defineAsyncComponent(() => import('./components/InventoryImportResultPanel.vue'))

/**
 * 库存数据导入插件
 * 支持商品库存数量批量更新，提供增量更新和全量同步两种模式
 * 具备完整的数据验证、预览确认和导入结果展示功能
 */
export const inventoryImportPlugin: DataImportPlugin = {
  // 基础信息
  id: 'inventory',
  name: 'inventory',
  displayName: '库存数据导入',
  description: '支持商品库存数量批量更新，提供增量更新和全量同步两种模式，具备智能匹配、数据验证和预警功能',
  version: '2.0.0',
  author: 'ERP System',
  icon: 'Package',
  enabled: true,
  order: 4,

  // 核心功能模块
  templateGenerator: new InventoryTemplateGenerator(),
  dataValidator: new InventoryDataValidator(),
  dataPreviewer: new InventoryDataPreviewer(),
  dataImporter: new InventoryDataImporter(),

  // 自定义组件配置
  customComponents: {
    validationPanel: InventoryValidationPanel,
    previewPanel: InventoryPreviewPanel,
    resultPanel: InventoryImportResultPanel
  },

  // 组件使用配置
  componentConfig: {
    useCustomValidationPreview: false, // 使用自定义步骤配置
    useCustomResultPage: true,        // 使用自定义结果页面
    // 自定义步骤配置 - 库存导入有两个预览步骤
    customSteps: [
      { 
        key: 'select-store', 
        title: '选择门店', 
        component: null 
      },
      { 
        key: 'download-template', 
        title: '下载模板', 
        component: null 
      },
      { 
        key: 'upload-file', 
        title: '上传文件', 
        component: null 
      },
      { 
        key: 'validate-preview', 
        title: '数据校验', 
        component: InventoryValidationPanel,
        canProceed: (stepData: Record<string, any>) => {
          // 验证步骤可以继续的条件：
          // 1. 验证完成且没有阻断性错误
          // 2. 或者用户确认跳过错误
          return !!stepData.stockImportPreview && (
            !stepData.validationErrors?.length || 
            stepData.skipErrors === true
          )
        }
      },
      { 
        key: 'import-preview', 
        title: '导入预览', 
        component: InventoryPreviewPanel,
        canProceed: (stepData: Record<string, any>) => {
          // 导入预览步骤可以继续的条件：
          // 1. 有预览数据
          // 2. 用户确认导入
          return !!stepData.stockImportPreview && stepData.confirmImport === true
        }
      },
      { 
        key: 'import-result', 
        title: '导入结果', 
        component: InventoryImportResultPanel 
      }
    ]
  },

  // 生命周期钩子
  async onInstall() {
    console.log('📦 库存数据导入插件已安装')
  },

  async onEnable() {
    console.log('📦 库存数据导入插件已启用')
  },

  async beforeImport(data: any[]) {
    console.log(`📦 准备导入库存数据: ${data.length} 条记录`)

    // 数据预处理：检查重复商品名称
    const productNames = data.map(item => item['商品名称'])
    const duplicates = productNames.filter((name, index) => productNames.indexOf(name) !== index)

    if (duplicates.length > 0) {
      console.warn('发现重复的商品名称:', [...new Set(duplicates)])
    }

    // 零库存检查
    const zeroStockItems = data.filter(item => (parseInt(item['库存数量']) || 0) === 0)
    if (zeroStockItems.length > 0) {
      console.warn(`发现 ${zeroStockItems.length} 个商品将被设置为零库存`)
    }

    // 大数量检查
    const largeStockItems = data.filter(item => (parseInt(item['库存数量']) || 0) > 10000)
    if (largeStockItems.length > 0) {
      console.warn(`发现 ${largeStockItems.length} 个商品库存数量异常大（>10000）`)
    }
  },

  async afterImport(result) {
    console.log(`📦 库存数据导入完成: ${result.successRows}/${result.totalRows} 条成功`)

    if (result.success) {
      console.log('📦 库存数据导入成功')
    } else {
      console.error('📦 库存数据导入存在错误，请检查错误信息')
    }

    // 导入统计分析
    const successRate = (result.successRows / result.totalRows) * 100
    console.log(`📦 导入成功率: ${successRate.toFixed(2)}%`)

    // 记录导入操作日志
    console.log(`📦 导入耗时: ${result.executionTime}ms`)
  }
}

// 导出插件实例 (保持向后兼容)
export default inventoryImportPlugin 