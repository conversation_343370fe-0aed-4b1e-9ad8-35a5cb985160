// 库存数据导入 - 模板生成器

import type { TemplateGenerator, TemplateData } from '../../core/types'

/**
 * 库存模板生成器
 */
export class InventoryTemplateGenerator implements TemplateGenerator {
  getTemplateData(): TemplateData {
    return {
      headers: ['商品名称', '库存数量'],
      fields: [
        {
          key: '商品名称',
          label: '商品名称',
          type: 'string',
          required: true,
          maxLength: 100,
          description: '商品的名称，必须与系统中的商品名称完全一致',
          example: '茅台酒'
        },
        {
          key: '库存数量',
          label: '库存数量',
          type: 'number',
          required: true,
          description: '当前实际库存数量',
          example: '100'
        }
      ],
      validationRules: [
        {
          field: '商品名称',
          type: 'required',
          message: '商品名称不能为空'
        },
        {
          field: '库存数量',
          type: 'required',
          message: '库存数量不能为空'
        },
        {
          field: '库存数量',
          type: 'type',
          message: '库存数量必须是数字'
        }
      ],
      exampleData: [
        {
          '商品名称': '茅台酒',
          '库存数量': '100'
        },
        {
          '商品名称': '五粮液',
          '库存数量': '50'
        },
        {
          '商品名称': '剑南春',
          '库存数量': '200'
        }
      ],
      instructions: [
        '请按照以下要求填写库存信息：',
        '• 商品名称：必须与系统中的商品名称完全一致，不能重复',
        '• 库存数量：当前实际库存数量，必须为非负整数',
        '',
        '注意事项：',
        '• 商品名称不能为空且不能重复',
        '• 库存数量必须填写有效数值',
        '• 保存为Excel或CSV格式文件，编码为UTF-8',
        '• 建议先下载模板文件，在模板基础上填写数据',
        '• 不存在的商品将被跳过，仅导入有效的商品库存'
      ],
      description: '库存信息导入模板，支持批量更新商品库存数量'
    }
  }

  generateCSV(): string {
    const templateData = this.getTemplateData()
    const headers = templateData.headers.join(',')
    const examples = templateData.exampleData.map(row => 
      templateData.headers.map(header => row[header] || '').join(',')
    ).join('\n')
    
    return `${headers}\n${examples}`
  }

  getExampleData(): Record<string, any>[] {
    return this.getTemplateData().exampleData
  }
} 