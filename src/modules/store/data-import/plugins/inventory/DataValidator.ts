// 库存数据导入 - 数据验证器

import type { DataValidator, FieldConfig, ValidationResult, ValidationError } from '../../core/types'

/**
 * 库存数据验证器
 */
export class InventoryDataValidator implements DataValidator {
  getFieldConfig(): FieldConfig[] {
    return [
      {
        key: '商品编号',
        label: '商品编号',
        type: 'string',
        required: true,
        maxLength: 50,
        description: '商品的唯一标识',
        example: 'P001'
      },
      {
        key: '库存数量',
        label: '库存数量',
        type: 'number',
        required: true,
        description: '当前实际库存数量',
        example: '100'
      },
      {
        key: '安全库存',
        label: '安全库存',
        type: 'number',
        required: false,
        description: '库存预警阈值',
        example: '20'
      }
    ]
  }

  validate(data: any[]): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    let validRows = 0

    // 检查商品编号重复
    const productCodes = new Map<string, number[]>()
    
    data.forEach((row, index) => {
      const productCode = row['商品编号']
      if (productCode) {
        if (!productCodes.has(productCode)) {
          productCodes.set(productCode, [])
        }
        productCodes.get(productCode)!.push(index)
      }
    })

    // 标记重复的商品编号
    productCodes.forEach((indices, productCode) => {
      if (indices.length > 1) {
        indices.forEach(index => {
          errors.push({
            row: index,
            field: '商品编号',
            value: productCode,
            message: `商品编号重复，在第 ${indices.map(i => i + 1).join(', ')} 行出现`,
            type: 'error'
          })
        })
      }
    })

    // 逐行验证
    data.forEach((row, index) => {
      const rowErrors = this.validateRow(row, index)
      errors.push(...rowErrors.filter(e => e.type === 'error'))
      warnings.push(...rowErrors.filter(e => e.type === 'warning'))
      
      if (rowErrors.filter(e => e.type === 'error').length === 0) {
        validRows++
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      validRows,
      totalRows: data.length
    }
  }

  validateRow(row: any, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const configs = this.getFieldConfig()

    configs.forEach(config => {
      const error = this.validateField(row[config.key], config, index)
      if (error) {
        errors.push(error)
      }
    })

    // 业务逻辑验证
    const stockQuantity = parseInt(row['库存数量']) || 0
    const safeStock = parseInt(row['安全库存']) || 0

    // 检查库存数量是否为负数
    if (stockQuantity < 0) {
      errors.push({
        row: index,
        field: '库存数量',
        value: row['库存数量'],
        message: '库存数量不能为负数',
        type: 'error'
      })
    }

    // 检查安全库存是否为负数
    if (safeStock < 0) {
      errors.push({
        row: index,
        field: '安全库存',
        value: row['安全库存'],
        message: '安全库存不能为负数',
        type: 'error'
      })
    }

    // 警告：库存低于安全库存
    if (safeStock > 0 && stockQuantity <= safeStock) {
      errors.push({
        row: index,
        field: '库存数量',
        value: row['库存数量'],
        message: `当前库存(${stockQuantity})低于安全库存(${safeStock})`,
        type: 'warning'
      })
    }

    // 警告：零库存
    if (stockQuantity === 0) {
      errors.push({
        row: index,
        field: '库存数量',
        value: row['库存数量'],
        message: '当前库存为零，建议及时补货',
        type: 'warning'
      })
    }

    return errors
  }

  validateField(value: any, config: FieldConfig, rowIndex: number): ValidationError | null {
    // 必填验证
    if (config.required && (value === undefined || value === null || value === '')) {
      return {
        row: rowIndex,
        field: config.key,
        value,
        message: `${config.label}不能为空`,
        type: 'error'
      }
    }

    // 如果值为空且非必填，跳过后续验证
    if (value === undefined || value === null || value === '') {
      return null
    }

    // 类型验证
    if (config.type === 'number') {
      const numValue = Number(value)
      if (isNaN(numValue)) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}必须是有效数字`,
          type: 'error'
        }
      }
    }

    // 长度验证
    if (config.type === 'string' && config.maxLength) {
      if (String(value).length > config.maxLength) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}长度不能超过${config.maxLength}个字符`,
          type: 'error'
        }
      }
    }

    // 正则验证
    if (config.pattern && config.type === 'string') {
      const regex = new RegExp(config.pattern)
      if (!regex.test(String(value))) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}格式不正确`,
          type: 'error'
        }
      }
    }

    return null
  }
} 