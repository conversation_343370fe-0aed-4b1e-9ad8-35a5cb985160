// 库存数据导入 - 数据预览器

import type { DataPreviewer } from '../../core/types'

/**
 * 库存数据预览器
 */
export class InventoryDataPreviewer implements DataPreviewer {
  getPreviewColumns(): Array<{ key: string; label: string; width?: number }> {
    return [
      { key: '商品编号', label: '商品编号', width: 120 },
      { key: '库存数量', label: '库存数量', width: 100 },
      { key: '安全库存', label: '安全库存', width: 100 }
    ]
  }

  formatPreviewData(data: any[]): any[] {
    return data.map(row => ({
      '商品编号': row['商品编号'] || '',
      '库存数量': this.formatNumber(row['库存数量']),
      '安全库存': this.formatNumber(row['安全库存']),
      // 添加计算字段
      '库存状态': this.getStockStatus(row),
      '差值': this.getStockDifference(row)
    }))
  }

  getPreviewSummary(data: any[]): Record<string, any> {
    const totalRows = data.length
    const totalQuantity = data.reduce((sum, row) => {
      return sum + (parseInt(row['库存数量']) || 0)
    }, 0)
    
    const uniqueProducts = new Set(data.map(row => row['商品编号'])).size
    
    const lowStockCount = data.filter(row => {
      const current = parseInt(row['库存数量']) || 0
      const safe = parseInt(row['安全库存']) || 0
      return safe > 0 && current <= safe
    }).length

    const zeroStockCount = data.filter(row => 
      (parseInt(row['库存数量']) || 0) === 0
    ).length

    return {
      总记录数: totalRows,
      商品种类: uniqueProducts,
      总库存量: totalQuantity.toLocaleString(),
      低库存商品: lowStockCount,
      零库存商品: zeroStockCount,
      库存充足商品: totalRows - lowStockCount - zeroStockCount
    }
  }

  private formatNumber(value: any): string {
    const num = parseInt(value) || 0
    return num.toLocaleString()
  }

  private getStockStatus(row: any): string {
    const current = parseInt(row['库存数量']) || 0
    const safe = parseInt(row['安全库存']) || 0
    
    if (current === 0) return '零库存'
    if (safe > 0 && current <= safe) return '低库存'
    return '正常'
  }

  private getStockDifference(row: any): string {
    const current = parseInt(row['库存数量']) || 0
    const safe = parseInt(row['安全库存']) || 0
    
    if (safe === 0) return '-'
    const diff = current - safe
    return diff >= 0 ? `+${diff}` : `${diff}`
  }
} 