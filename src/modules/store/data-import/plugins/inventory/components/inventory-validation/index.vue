<template>
  <div class="inventory-validation-panel">
    <!-- 验证摘要 -->
    <div class="validation-summary mb-6">
      <h3 class="text-xl font-semibold mb-4">库存数据验证</h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600 font-medium">总记录数</div>
          <div class="text-2xl font-bold text-blue-700">{{ vm.computed.totalRows.value }}</div>
        </div>
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600 font-medium">有效记录</div>
          <div class="text-2xl font-bold text-green-700">{{ vm.computed.validRows.value }}</div>
        </div>
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <div class="text-sm text-red-600 font-medium">错误记录</div>
          <div class="text-2xl font-bold text-red-700">{{ vm.computed.errorRows.value }}</div>
        </div>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <div class="text-sm text-yellow-600 font-medium">警告记录</div>
          <div class="text-2xl font-bold text-yellow-700">{{ vm.computed.warningRows.value }}</div>
        </div>
      </div>
    </div>

    <!-- 库存数据统计 -->
    <div class="inventory-stats mb-6">
      <h4 class="text-lg font-semibold mb-4">库存数据统计</h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">商品总数</div>
          <div class="text-xl font-bold text-gray-800">{{ vm.computed.uniqueProducts.value }}</div>
        </div>
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">总库存量</div>
          <div class="text-xl font-bold text-gray-800">{{ vm.computed.totalQuantity.value.toLocaleString() }}</div>
        </div>
        <div class="bg-gray-50 border rounded-lg p-4">
          <div class="text-sm text-gray-600">库存较低商品</div>
          <div class="text-xl font-bold" :class="vm.computed.lowStockCount.value > 0 ? 'text-yellow-600' : 'text-gray-800'">
            {{ vm.computed.lowStockCount.value }}
          </div>
        </div>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="vm.state.isLoading.value" class="flex items-center justify-center py-8">
      <el-icon class="animate-spin mr-2" :size="20">
        <Loading />
      </el-icon>
      <span class="text-gray-600">正在验证数据...</span>
    </div>

    <!-- 错误和警告详情 -->
    <div v-else-if="vm.state.hasErrors.value || vm.state.hasWarnings.value" class="validation-details mb-6">
      <h4 class="text-lg font-semibold mb-4">验证详情</h4>
      
      <!-- 错误和警告选项卡 -->
      <el-tabs v-model="vm.state.activeTab.value" type="border-card">
        <!-- 商品不存在的错误 -->
        <el-tab-pane 
          v-if="vm.computed.productNotExistErrors.value.length > 0" 
          :label="`商品不存在 (${vm.computed.productNotExistErrors.value.length})`" 
          name="product-not-exist"
        >
          <div class="max-h-60 overflow-y-auto space-y-4">
            <div 
              v-for="(item, index) in vm.computed.productNotExistErrors.value" 
              :key="`product-not-exist-${index}`"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-red-500 mt-1"><WarningFilled /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-red-700">
                    第 {{ item.rowIndex }} 行 - {{ item.productName }}
                  </div>
                  <div class="text-sm text-red-600 mt-1">{{ item.errorMessage || '商品不存在于门店商品列表中' }}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    导入库存: {{ item.newStock }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 门店商品未在导入数据中找到 -->
        <el-tab-pane 
          v-if="vm.computed.stockNotExistWarnings.value.length > 0" 
          :label="`库存数据缺失 (${vm.computed.stockNotExistWarnings.value.length})`" 
          name="stock-not-exist"
        >
          <div class="max-h-60 overflow-y-auto space-y-4">
            <div 
              v-for="(item, index) in vm.computed.stockNotExistWarnings.value" 
              :key="`stock-not-exist-${index}`"
              class="border border-orange-200 rounded-lg p-4 bg-orange-50"
            >
              <div class="flex items-start gap-3">
                <el-icon class="text-orange-500 mt-1"><Warning /></el-icon>
                <div class="flex-1">
                  <div class="text-sm font-medium text-orange-700">
                    {{ item.productName }}
                  </div>
                  <div class="text-sm text-orange-600 mt-1">{{ item.warningMessage }}</div>
                  <div class="text-xs text-gray-500 mt-1">
                    当前库存: {{ item.currentStock }} | 商品ID: {{ item.productId }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>


      </el-tabs>
    </div>

    <!-- 库存数据预览表格 -->
    <div v-if="!vm.state.isLoading.value" class="data-preview mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold">数据预览</h4>
        <div class="flex items-center gap-3">
          <el-input
            v-model="vm.state.searchKeyword.value"
            placeholder="搜索商品名称..."
            size="small"
            style="width: 200px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select 
            v-model="vm.state.filterStatus.value" 
            placeholder="筛选状态" 
            size="small" 
            style="width: 120px;"
          >
            <el-option label="全部" value="all" />
            <el-option label="正常" value="normal" />
            <el-option label="库存较低" value="low" />
            <el-option label="零库存" value="zero" />
          </el-select>
          <el-date-picker
            v-model="vm.state.dateFilter.value"
            type="datetime"
            placeholder="过滤创建时间"
            size="small"
            style="width: 180px;"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            clearable
          />
        </div>
      </div>

      <el-table 
        :data="vm.computed.filteredPreviewItems.value" 
        style="width: 100%"
        max-height="400"
        stripe
        border
      >
        <el-table-column type="index" label="序号" width="60" fixed="left" />
        <el-table-column prop="productId" label="商品ID" width="140" fixed="left">
          <template #default="{ row }">
            <span v-if="row.productId" class="font-mono text-sm">{{ row.productId }}</span>
            <span v-else class="text-gray-400 text-sm">-</span>
          </template>
        </el-table-column>
        <el-table-column prop="productName" label="商品名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <span :class="row.isMatched ? 'text-gray-900' : 'text-red-600'">
              {{ row.productName }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="导入库存" width="120" align="right">
          <template #default="{ row }">
            <span class="font-semibold text-blue-600">
              {{ row.newStock }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="验证状态" width="150" align="center">
          <template #default="{ row }">
            <div class="flex items-center justify-center gap-2">
              <el-icon 
                v-if="!row.isMatched"
                class="text-red-500"
              >
                <CircleCloseFilled />
              </el-icon>
              <el-icon 
                v-else-if="row.warningMessage"
                class="text-yellow-500"
              >
                <WarningFilled />
              </el-icon>
              <el-icon 
                v-else
                class="text-green-500"
              >
                <CircleCheckFilled />
              </el-icon>
              
              <span class="text-sm">
                <span v-if="!row.isMatched" class="text-red-600">
                  商品不存在
                </span>
                <span v-else-if="row.warningMessage" class="text-yellow-600">
                  {{ row.warningMessage }}
                </span>
                <span v-else class="text-green-600">
                  匹配成功
                </span>
              </span>
            </div>
          </template>
        </el-table-column>
      </el-table>


    </div>

    <!-- 底部操作按钮 -->
    <div class="mt-6 flex justify-between items-center">
      <div class="flex items-center gap-4">
        <div class="text-sm text-gray-500">
          <span v-if="vm.state.hasErrors.value" class="text-red-600">
            请修复所有错误后继续，或选择跳过错误记录
          </span>
          <span v-else class="text-green-600">
            验证通过，可以继续导入
          </span>
        </div>
        
        <!-- 导出匹配数据按钮 -->
        <el-button 
          v-if="vm.computed.previewItems.value.some(item => item.isMatched)"
          size="small"
          @click="vm.actions.exportMatchedData"
          :disabled="vm.state.isLoading.value"
        >
          <el-icon><Download /></el-icon>
          导出匹配数据
        </el-button>
      </div>
      
      <div class="space-x-3">
        <el-button 
          @click="vm.actions.handlePrevStep"
          :disabled="vm.state.isLoading.value"
        >
          上一步
        </el-button>
        
        <!-- 如果有错误，显示确认导入按钮 -->
        <el-button 
          v-if="vm.state.hasErrors.value"
          type="warning" 
          @click="vm.actions.handleConfirmWithErrors"
          :disabled="vm.state.isLoading.value"
        >
          跳过错误继续导入
        </el-button>
        
        <!-- 正常下一步按钮 -->
        <el-button 
          type="primary" 
          @click="vm.actions.handleNextStep"
          :disabled="vm.state.isLoading.value || !vm.state.canProceed.value"
        >
          下一步
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { 
  WarningFilled, 
  Warning, 
  CircleCloseFilled, 
  CircleCheckFilled, 
  Search,
  Loading,
  Download
} from '@element-plus/icons-vue'
import { useInventoryValidationPresenter } from './presenter'
import type { IInventoryValidationViewModel } from './viewmodel'
import type { InventoryValidationProps, InventoryValidationEmits } from './type'

// Props 和 Emits 定义
const props = withDefaults(defineProps<InventoryValidationProps>(), {
  isProcessing: false,
  currentStep: 0
})

const emit = defineEmits<InventoryValidationEmits>()

// 获取 ViewModel 实例 - View层唯一依赖
const vm: IInventoryValidationViewModel = useInventoryValidationPresenter()

// 设置Props和Emits处理 - 完全委托给Presenter层
vm.actions.setupProps(props)
vm.actions.setupEmits(emit)

// View 层职责：纯粹的模板绑定，所有逻辑都在Presenter层处理
</script>

<style scoped>
.inventory-validation-panel {
  max-width: 100%;
}

.validation-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

.inventory-stats {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}
</style> 