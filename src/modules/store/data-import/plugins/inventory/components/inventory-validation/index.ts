/**
 * 库存验证组件导出
 * VIPER-VueCS架构实现
 */

// 主组件
export { default as InventoryValidation } from './index.vue'

// 类型定义
export type {
  InventoryValidationProps,
  InventoryValidationEmits,
  ValidationError,
  ValidationResult,
  ParsedData,
  StepData
} from './type'

// ViewModel接口
export type {
  IInventoryValidationViewModel,
  IInventoryValidationState,
  IInventoryValidationComputed,
  IInventoryValidationActions
} from './viewmodel'

// Presenter工厂函数
export { useInventoryValidationPresenter } from './presenter'

// State管理
export { useInventoryValidationPageState } from './state'

// 业务逻辑
export { useInventoryValidationInteractor } from './interactor'

// 数据转换器
export { InventoryValidationConverter } from './converter' 