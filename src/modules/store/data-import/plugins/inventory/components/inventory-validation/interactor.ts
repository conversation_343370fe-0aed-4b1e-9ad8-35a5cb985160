import { businessInventoryApi } from '@/api/business/inventory'
import { businessProductApi } from '@/api/business/product'
import type { 
  StockImportPreviewReqDto, 
  StockImportPreviewVO,
  VenueStockItem,
  VenueStockListReqDto
} from '@/entities/business/inventory'
import type { 
  ProductQueryReqDto, 
  ProductQueryVO,
  ProductItem
} from '@/entities/business/product'

import { InventoryValidationConverter } from './converter'
import type { StockImportPreviewItem, StockImportPreviewData } from './type'

/**
 * 库存验证业务交互器
 * 负责执行业务逻辑和数据访问（API调用）
 */
export class InventoryValidationInteractor {
  /**
   * 获取门店商品列表 (ProductItem格式)
   * @param venueId - 门店ID
   * @returns 门店商品列表
   */
  async getVenueProductItems(venueId: string): Promise<ProductItem[]> {
    try {
      console.log('Interactor: Fetching venue product items for', venueId)
      
      // 验证 venueId 参数
      if (!venueId || typeof venueId !== 'string' || venueId.trim() === '') {
        throw new Error('门店ID不能为空')
      }
      
      // 调用产品查询API获取门店商品
      const productQuery: ProductQueryReqDto = {
        venueId: venueId.trim(),
        pageNum: 1,
        pageSize: 10000,
        isDisplayed: true
      }
      
      console.log('Interactor: 调用产品查询API，参数:', JSON.stringify(productQuery))
      const productResponse = await businessProductApi.queryProducts(productQuery)
      console.log('Interactor: 产品查询API调用成功，响应类型:', Array.isArray(productResponse) ? '数组' : '对象')
      
      // 处理两种可能的响应格式
      let productList: ProductItem[] = []
      
      if (Array.isArray(productResponse)) {
        productList = productResponse as ProductItem[]
        console.log(`Interactor: 获取到 ${productList.length} 个门店商品 (数组格式)`)
      } else if (productResponse && typeof productResponse === 'object') {
        const apiResponse = productResponse as ProductQueryVO
        if (apiResponse.code !== 200 || !apiResponse.data || apiResponse.data.length === 0) {
          const errorMsg = apiResponse.message || '未获取到门店商品数据'
          throw new Error(`产品查询失败: ${errorMsg}`)
        }
        productList = apiResponse.data
        console.log(`Interactor: 获取到 ${productList.length} 个门店商品 (标准格式)`)
      } else {
        throw new Error('产品查询失败: 响应格式无效')
      }
      
      // 检查是否有数据
      if (productList.length === 0) {
        throw new Error('产品查询失败: 该门店没有商品数据')
      }
      
      return productList
    } catch (error) {
      console.error('Interactor: Failed to fetch venue product items', error)
      throw new Error(`获取门店商品列表失败: ${(error as Error).message}`)
    }
  }

  /**
   * 生成库存导入预览数据 (仅合并商品信息和Excel数据，不调用API)
   * @param venueId - 门店ID
   * @param uploadData - Excel上传数据
   * @param syncMode - 同步模式
   * @param effectiveTime - 有效时间
   * @returns 完整的预览数据
   */
  async generateStockImportPreview(
    venueId: string,
    uploadData: any[],
    syncMode: 'incremental' | 'full_sync',
    effectiveTime?: string
  ): Promise<StockImportPreviewData> {
    try {
      console.log('Interactor: 生成库存导入预览数据 (本地验证)')
      
      // 1. 获取门店商品列表
      const productList = await this.getVenueProductItems(venueId)
      
      // 2. 合并商品信息和Excel数据 (本地匹配)
      const previewItems = InventoryValidationConverter.mergeProductAndStockData(
        uploadData,
        productList,
        syncMode
      )
      
      // 3. 生成完整的预览数据 (不调用API)
      const previewData = InventoryValidationConverter.generateStockImportPreviewData(
        venueId,
        previewItems,
        undefined, // 不传入API响应
        syncMode,
        effectiveTime
      )
      
      console.log('Interactor: 库存导入预览数据生成完成 (本地验证)')
      return previewData
    } catch (error) {
      console.error('Interactor: 生成库存导入预览数据失败', error)
      throw new Error(`生成库存导入预览失败: ${(error as Error).message}`)
    }
  }

  /**
   * 获取门店商品列表
   * @param venueId - 门店ID
   * @returns 门店商品列表
   */
  async getVenueProducts(venueId: string): Promise<VenueStockItem[]> {
    try {
      console.log('Interactor: Fetching venue products for', venueId)
      
      // 验证 venueId 参数
      if (!venueId || typeof venueId !== 'string' || venueId.trim() === '') {
        throw new Error('门店ID不能为空')
      }
      
      // 调用产品查询API获取门店商品
      const productQuery: ProductQueryReqDto = {
        venueId: venueId.trim(), // 门店ID参数
        pageNum: 1,
        pageSize: 10000, // 获取所有商品
        isDisplayed: true // 只查询上架展示的商品
      }
      
      console.log('Interactor: 调用产品查询API，参数:', JSON.stringify(productQuery))
      const productResponse = await businessProductApi.queryProducts(productQuery)
      console.log('Interactor: 产品查询API调用成功，响应类型:', Array.isArray(productResponse) ? '数组' : '对象')
      
      // 处理两种可能的响应格式
      let productList: ProductItem[] = []
      
      if (Array.isArray(productResponse)) {
        // 直接返回数组的情况
        productList = productResponse as ProductItem[]
        console.log(`Interactor: 获取到 ${productList.length} 个门店商品 (数组格式)`)
      } else if (productResponse && typeof productResponse === 'object') {
        // 标准API响应格式的情况
        const apiResponse = productResponse as ProductQueryVO
        if (apiResponse.code !== 200 || !apiResponse.data || apiResponse.data.length === 0) {
          const errorMsg = apiResponse.message || '未获取到门店商品数据'
          throw new Error(`产品查询失败: ${errorMsg}`)
        }
        productList = apiResponse.data
        console.log(`Interactor: 获取到 ${productList.length} 个门店商品 (标准格式)`)
      } else {
        throw new Error('产品查询失败: 响应格式无效')
      }
      
      // 检查是否有数据
      if (productList.length === 0) {
        throw new Error('产品查询失败: 该门店没有商品数据')
      }
      
      // 将 ProductItem 转换为 VenueStockItem 格式
      console.log('Interactor: 转换产品数据为库存格式')
      const stockItems: VenueStockItem[] = productList.map(product => ({
        id: product.id || '',
        venueId: product.venueId || venueId,
        productId: product.id || '',
        productName: product.name || '',
        productCode: product.barcode || '',
        category: product.category || '',
        unit: product.unit || '',
        currentStock: product.stock || 0,
        minStock: product.lowStockThreshold || 0,
        maxStock: 999999, // 默认最大库存
        price: product.currentPrice || 0,
        costPrice: 0, // 成本价默认为0
        status: product.isDisplayed ? 'ACTIVE' : 'INACTIVE',
        lastUpdated: new Date(product.utime ? product.utime * 1000 : Date.now()).toISOString(),
        remark: product.description || ''
      }))
      
      console.log(`Interactor: 成功转换 ${stockItems.length} 个商品为库存格式`)
      return stockItems
    } catch (error) {
      console.error('Interactor: Failed to fetch venue products', error)
      throw new Error(`获取门店商品列表失败: ${(error as Error).message}`)
    }
  }



  /**
   * 验证文件格式和基础数据
   * @param fileData - 文件数据
   * @returns 验证结果
   */
  validateFileFormat(fileData: any[]): { isValid: boolean; errors: string[] } {
    console.log('Interactor: Validating file format')
    
    const errors: string[] = []
    
    // 检查文件是否为空
    if (!fileData || fileData.length === 0) {
      errors.push('文件内容为空')
      return { isValid: false, errors }
    }
    
    // 检查必要的列是否存在
    const requiredColumns = ['商品名称', '库存数量']
    const firstRow = fileData[0]
    
    if (!firstRow) {
      errors.push('文件格式错误：无法读取数据')
      return { isValid: false, errors }
    }
    
    for (const column of requiredColumns) {
      if (!(column in firstRow)) {
        errors.push(`缺少必要列: ${column}`)
      }
    }
    
    // 检查数据行数限制
    if (fileData.length > 10000) {
      errors.push('文件数据超过10000行限制')
    }
    
    console.log('Interactor: File format validation completed')
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 检查门店权限
   * @param venueId - 门店ID
   * @returns 是否有权限
   */
  async checkVenuePermission(venueId: string): Promise<boolean> {
    try {
      console.log('Interactor: Checking venue permission for', venueId)
      
      // 这里可以调用权限检查API
      // 暂时返回true，实际项目中需要实现权限检查逻辑
      
      console.log('Interactor: Venue permission check passed')
      return true
    } catch (error) {
      console.error('Interactor: Venue permission check failed', error)
      return false
    }
  }

  /**
   * 生成导入报告
   * @param validationData - 验证数据
   * @returns 报告内容
   */
  generateValidationReport(validationData: {
    totalRows: number
    validRows: number
    errorRows: number
    warningRows: number
    errors: any[]
    warnings: any[]
  }): string {
    console.log('Interactor: Generating validation report')
    
    const { totalRows, validRows, errorRows, warningRows, errors, warnings } = validationData
    
    let report = `库存验证报告\n`
    report += `================\n`
    report += `总记录数: ${totalRows}\n`
    report += `有效记录: ${validRows}\n`
    report += `错误记录: ${errorRows}\n`
    report += `警告记录: ${warningRows}\n\n`
    
    if (errors.length > 0) {
      report += `错误详情:\n`
      errors.forEach((error, index) => {
        report += `${index + 1}. 第${error.row + 1}行 - ${error.field}: ${error.message}\n`
      })
      report += `\n`
    }
    
    if (warnings.length > 0) {
      report += `警告详情:\n`
      warnings.forEach((warning, index) => {
        report += `${index + 1}. 第${warning.row + 1}行 - ${warning.field}: ${warning.message}\n`
      })
    }
    
    console.log('Interactor: Validation report generated')
    return report
  }

  /**
   * 下载错误报告
   * @param errors - 错误列表
   * @param fileName - 文件名
   */
  downloadErrorReport(errors: any[], fileName: string = 'validation_errors.txt'): void {
    console.log('Interactor: Downloading error report')
    
    let content = '库存验证错误报告\n'
    content += '==================\n\n'
    
    errors.forEach((error, index) => {
      content += `错误 ${index + 1}:\n`
      content += `行号: ${error.row + 1}\n`
      content += `字段: ${error.field}\n`
      content += `值: ${error.value}\n`
      content += `错误信息: ${error.message}\n`
      content += `\n`
    })
    
    // 创建下载链接
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = fileName
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
    
    console.log('Interactor: Error report downloaded')
  }
}

/**
 * 创建库存验证业务交互器实例
 */
export function useInventoryValidationInteractor(): InventoryValidationInteractor {
  return new InventoryValidationInteractor()
} 