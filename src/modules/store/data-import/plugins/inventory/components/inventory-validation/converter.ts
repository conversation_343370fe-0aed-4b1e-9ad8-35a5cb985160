import type { InventoryValidationPageState } from './state'
import type { 
  ValidationError, 
  ValidationResult, 
  StepData,
  StockImportPreviewItem,
  StockImportPreviewData,
  StockImportPreviewSummary
} from './type'
import type { 
  StockImportPreviewReqDto, 
  StockImportPreviewVO,
  VenueStockItem,
  StockImportItemDto,
  StockImportPreviewItemVO
} from '@/entities/business/inventory'
import type { ProductItem } from '@/entities/business/product'

/**
 * 商品匹配结果接口
 */
export interface ProductMatchResult {
  productId?: string
  productName: string
  currentStock?: number
  newStock: number
  isMatched: boolean
  errorMessage?: string
  warningMessage?: string
}



/**
 * 库存验证数据转换器
 * 负责State层与外部Entity/DTO之间的数据结构转换
 */
export class InventoryValidationConverter {
  /**
   * 将页面状态转换为API预览请求格式
   * @param pageState - 页面状态
   * @param items - 库存导入项
   * @returns API请求DTO
   */
  static toPreviewRequestDto(
    pageState: Readonly<InventoryValidationPageState>,
    items: StockImportItemDto[]
  ): StockImportPreviewReqDto {
    console.log("Converter: PageState -> StockImportPreviewReqDto")
    
    const currentTime = Date.now()
    return {
      venueId: pageState.stepData.selectedStoreId || '',
      items,
      ctime: Math.floor(currentTime / 1000),
      utime: Math.floor(currentTime / 1000)
    }
  }

  /**
   * 将API预览响应转换为验证结果格式
   * @param apiResponse - API响应数据
   * @returns 验证结果
   */
  static toValidationResult(apiResponse: StockImportPreviewVO): ValidationResult {
    console.log("Converter: StockImportPreviewVO -> ValidationResult")
    
    // 从预览项中提取错误信息
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    
    if (apiResponse.previewItems) {
      apiResponse.previewItems.forEach((item, index) => {
        if (!item.valid && item.reason) {
          errors.push({
            row: index,
            field: 'productId',
            value: item.productId || '',
            message: item.reason,
            type: 'error' as const
          })
        }
      })
    }

    return {
      isValid: (apiResponse.validItems || 0) > 0 && errors.length === 0,
      errors,
      warnings,
      validRows: apiResponse.validItems || 0,
      totalRows: apiResponse.totalItems || 0
    }
  }



  /**
   * 将步骤数据转换为更新格式
   * @param stepData - 步骤数据
   * @returns 更新后的步骤数据
   */
  static toStepDataUpdateFormat(stepData: StepData): Partial<StepData> {
    console.log("Converter: StepData -> UpdateFormat")
    
    return {
      ...stepData,
      // 可以在这里添加额外的处理逻辑
    }
  }

  /**
   * 生成本地验证结果（用于前端验证）
   * @param uploadData - 上传的数据
   * @returns 验证结果
   */
  static generateLocalValidationResult(uploadData: any[]): ValidationResult {
    console.log("Converter: Generating local validation result")
    
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    let validRows = 0

    uploadData.forEach((row, index) => {
      const productName = row['商品名称']
      const stockQuantity = row['库存数量']

      // 基础验证
      if (!productName || productName.toString().trim() === '') {
        errors.push({
          row: index,
          field: '商品名称',
          value: productName || '',
          message: '商品名称不能为空',
          type: 'error'
        })
      }

      if (stockQuantity === undefined || stockQuantity === null || stockQuantity === '') {
        errors.push({
          row: index,
          field: '库存数量',
          value: stockQuantity || '',
          message: '库存数量不能为空',
          type: 'error'
        })
      } else {
        const qty = parseInt(stockQuantity)
        if (isNaN(qty) || qty < 0) {
          errors.push({
            row: index,
            field: '库存数量',
            value: stockQuantity.toString(),
            message: '库存数量必须是非负整数',
            type: 'error'
          })
        } else if (qty > 999999) {
          warnings.push({
            row: index,
            field: '库存数量',
            value: stockQuantity.toString(),
            message: '库存数量异常大，请确认',
            type: 'warning'
          })
        }
      }

      // 如果该行没有错误，则为有效行
      const hasRowErrors = errors.some(error => error.row === index)
      if (!hasRowErrors) {
        validRows++
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      validRows,
      totalRows: uploadData.length
    }
  }

  /**
   * 合并商品信息和Excel数据，生成预览项列表
   * @param uploadData - Excel上传的数据
   * @param productList - 门店商品列表
   * @param syncMode - 同步模式
   * @returns 预览项列表
   */
  static mergeProductAndStockData(
    uploadData: any[],
    productList: ProductItem[],
    syncMode: 'incremental' | 'full_sync'
  ): StockImportPreviewItem[] {
    console.log("Converter: 合并商品信息和库存数据")
    
    const previewItems: StockImportPreviewItem[] = []
    
    // 处理Excel上传的数据
    uploadData.forEach((row, index) => {
      const productName = row['商品名称']?.toString().trim() || ''
      const newStock = parseInt(row['库存数量']) || 0
      
      // 查找匹配的商品
      const matchedProduct = productList.find(product => 
        product.name === productName
      )
      
      let previewItem: StockImportPreviewItem
      
      if (matchedProduct) {
        // 匹配成功
        const currentStock = matchedProduct.stock || 0
        const stockChange = newStock - currentStock
        
        previewItem = {
          rowIndex: index + 2, // Excel行号从2开始(第1行是表头)
          productId: matchedProduct.id,
          productName: matchedProduct.name || '',
          productCode: matchedProduct.barcode || '',
          category: matchedProduct.category || '',
          unit: matchedProduct.unit || '',
          currentStock,
          newStock,
          stockChange,
          currentPrice: matchedProduct.currentPrice || 0,
          isMatched: true,
          operation: this.determineOperation(currentStock, newStock),
          validationStatus: 'pending',
          warningMessage: this.generateWarningMessage(currentStock, newStock),
          // 新增: 包含商品创建时间用于日期过滤
          ...(matchedProduct.ctime && { ctime: matchedProduct.ctime })
        } as StockImportPreviewItem & { ctime?: number }
      } else {
        // 匹配失败
        previewItem = {
          rowIndex: index + 2,
          productName,
          newStock,
          isMatched: false,
          operation: 'UPDATE',
          validationStatus: 'error',
          errorMessage: '商品不存在于门店商品列表中'
        }
      }
      
      previewItems.push(previewItem)
    })
    
    // 如果是全量同步模式，添加需要重置为0的商品
    if (syncMode === 'full_sync') {
      const uploadProductNames = new Set(uploadData.map(row => row['商品名称']?.toString().trim()))
      
      productList.forEach(product => {
        const productName = product.name || ''
        if (!uploadProductNames.has(productName) && (product.stock || 0) > 0) {
          previewItems.push({
            rowIndex: -1, // 标记为系统生成的重置项
            productId: product.id,
            productName,
            productCode: product.barcode || '',
            category: product.category || '',
            unit: product.unit || '',
            currentStock: product.stock || 0,
            newStock: 0,
            stockChange: -(product.stock || 0),
            currentPrice: product.currentPrice || 0,
            isMatched: true,
            operation: 'RESET',
            validationStatus: 'warning',
            warningMessage: '全量同步模式 - 将重置为0',
            // 新增: 包含商品创建时间用于日期过滤
            ...(product.ctime && { ctime: product.ctime })
          } as StockImportPreviewItem & { ctime?: number })
        }
      })
    }
    
    return previewItems
  }
  

  
  /**
   * 生成库存导入预览数据
   * @param venueId - 门店ID
   * @param previewItems - 预览项列表
   * @param apiResponse - API响应
   * @param syncMode - 同步模式
   * @param effectiveTime - 有效时间
   * @returns 完整的预览数据
   */
  static generateStockImportPreviewData(
    venueId: string,
    previewItems: StockImportPreviewItem[],
    apiResponse?: StockImportPreviewVO,
    syncMode: 'incremental' | 'full_sync' = 'incremental',
    effectiveTime?: string
  ): StockImportPreviewData {
    console.log("Converter: 生成库存导入预览数据")
    
    const matchedItems = previewItems.filter(item => item.isMatched).length
    const unmatchedItems = previewItems.filter(item => !item.isMatched).length
    
    const summary: StockImportPreviewSummary = {
      venueId,
      totalItems: previewItems.length,
      matchedItems,
      unmatchedItems,
      validItems: apiResponse?.validItems,
      invalidItems: apiResponse?.invalidItems,
      importToken: apiResponse?.importToken,
      tokenExpireTime: apiResponse?.tokenExpireTime,
      previewTime: Date.now()
    }
    
    return {
      summary,
      items: previewItems,
      syncMode,
      effectiveTime
    }
  }
  
  /**
   * 确定操作类型
   */
  private static determineOperation(currentStock: number, newStock: number): 'UPDATE' | 'RESET' | 'NO_CHANGE' {
    if (newStock === 0 && currentStock > 0) {
      return 'RESET'
    } else if (newStock === currentStock) {
      return 'NO_CHANGE'
    } else {
      return 'UPDATE'
    }
  }
  
  /**
   * 生成警告信息
   */
  private static generateWarningMessage(currentStock: number, newStock: number): string | undefined {
    if (newStock === 0 && currentStock > 0) {
      return '库存将被清零'
    } else if (newStock > currentStock * 3 && currentStock > 0) {
      return '库存大幅增长 (超过3倍)'
    } else if (newStock < currentStock * 0.3 && currentStock > 0) {
      return '库存大幅减少 (少于30%)'
    } else if (newStock > 10000) {
      return '库存数量异常高'
    }
    return undefined
  }
  
  /**
   * 将预览数据转换为传统的验证结果格式 (兼容现有逻辑)
   * @param previewData - 预览数据
   * @returns 验证结果
   */
  static previewDataToValidationResult(previewData: StockImportPreviewData): ValidationResult {
    console.log("Converter: StockImportPreviewData -> ValidationResult")
    
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    
    previewData.items.forEach((item, index) => {
      if (!item.isMatched) {
        errors.push({
          row: item.rowIndex >= 0 ? item.rowIndex - 2 : index, // 转换为基于0的索引
          field: '商品名称',
          value: item.productName,
          message: item.errorMessage || '商品不存在',
          type: 'error'
        })
      } else if (item.warningMessage) {
        warnings.push({
          row: item.rowIndex >= 0 ? item.rowIndex - 2 : index,
          field: '库存数量',
          value: item.newStock.toString(),
          message: item.warningMessage,
          type: 'warning'
        })
      }
    })
    
    const validRows = previewData.items.filter(item => item.isMatched).length
    const totalRows = previewData.items.filter(item => item.rowIndex >= 0).length // 排除系统生成的重置项
    
    return {
      isValid: errors.length === 0 && validRows > 0,
      errors,
      warnings,
      validRows,
      totalRows
    }
  }
} 