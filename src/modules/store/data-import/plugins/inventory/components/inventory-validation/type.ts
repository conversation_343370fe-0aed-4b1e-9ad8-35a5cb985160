/**
 * 库存验证组件的Props和Emits接口定义
 */

// 验证错误项接口
export interface ValidationError {
  row: number
  field: string
  value: string
  message: string
  type: 'error' | 'warning'
}

// 验证结果接口
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
  validRows: number
  totalRows: number
}

// 解析后的数据接口
export interface ParsedData {
  data: any[]
  headers: string[]
}

/**
 * 库存导入预览项 - 合并商品信息和库存数据
 */
export interface StockImportPreviewItem {
  /** 行号 (Excel中的行号) */
  rowIndex: number
  /** 产品ID */
  productId?: string
  /** 产品名称 */
  productName: string
  /** 产品编码/条形码 */
  productCode?: string
  /** 产品分类 */
  category?: string
  /** 产品单位 */
  unit?: string
  /** 当前库存数量 */
  currentStock?: number
  /** 新库存数量 (用户输入) */
  newStock: number
  /** 库存变化量 */
  stockChange?: number
  /** 当前价格 */
  currentPrice?: number
  /** 是否匹配成功 */
  isMatched: boolean
  /** 是否有效 (通过后端验证) */
  isValid?: boolean
  /** 操作类型 */
  operation?: 'UPDATE' | 'RESET' | 'NO_CHANGE'
  /** 错误信息 */
  errorMessage?: string
  /** 警告信息 */
  warningMessage?: string
  /** 验证状态 */
  validationStatus: 'pending' | 'validating' | 'success' | 'error' | 'warning'
}

/**
 * 库存导入预览汇总数据
 */
export interface StockImportPreviewSummary {
  /** 门店ID */
  venueId: string
  /** 门店名称 */
  venueName?: string
  /** 总记录数 */
  totalItems: number
  /** 匹配成功数 */
  matchedItems: number
  /** 匹配失败数 */
  unmatchedItems: number
  /** 有效记录数 (后端验证通过) */
  validItems?: number
  /** 无效记录数 (后端验证失败) */
  invalidItems?: number
  /** 导入令牌 */
  importToken?: string
  /** 令牌过期时间 */
  tokenExpireTime?: number
  /** 预览时间 */
  previewTime: number
}

/**
 * 完整的库存导入预览数据
 */
export interface StockImportPreviewData {
  /** 汇总信息 */
  summary: StockImportPreviewSummary
  /** 预览项目列表 */
  items: StockImportPreviewItem[]
  /** 同步模式 */
  syncMode: 'incremental' | 'full_sync'
  /** 库存有效时间 */
  effectiveTime?: string
}

// 步骤数据接口
export interface StepData {
  parsedData?: ParsedData
  validationResult?: ValidationResult
  validationErrors?: ValidationError[]
  selectedStoreId?: string
  selectedStoreName?: string
  effectiveTime?: string
  syncMode?: 'incremental' | 'full_sync'
  /** 新增: 库存导入预览数据 */
  stockImportPreview?: StockImportPreviewData
  /** 新增: 是否跳过错误 */
  skipErrors?: boolean
  /** 新增: API预览数据 */
  apiPreviewData?: any
  /** 新增: 导入令牌 */
  importToken?: string
  /** 新增: 令牌过期时间 */
  tokenExpireTime?: number
  /** 新增: 确认导入标志 */
  confirmImport?: boolean
  /** 新增: 导入完成标志 */
  importCompleted?: boolean
  /** 新增: 完整的门店商品列表 */
  allVenueProducts?: any[]
}

// 组件Props接口
export interface InventoryValidationProps {
  stepData: StepData
  plugin?: any
  currentStep?: number
  isProcessing?: boolean
}

// 组件Emits接口
export interface InventoryValidationEmits {
  (e: 'prev'): void
  (e: 'next'): void
  (e: 'update:step-data', data: Partial<StepData>): void
} 