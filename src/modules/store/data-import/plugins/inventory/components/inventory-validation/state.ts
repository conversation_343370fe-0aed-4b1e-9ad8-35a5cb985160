import { reactive, readonly } from 'vue'
import type { ValidationError, ValidationResult, StepData } from './type'

/**
 * 库存验证页面状态结构
 */
export interface InventoryValidationPageState {
  // 基础状态
  isLoading: boolean
  
  // UI控制状态
  activeTab: string
  searchKeyword: string
  filterStatus: string
  dateFilter: string
  currentPage: number
  pageSize: number
  
  // 数据状态
  stepData: StepData
  
  // 验证状态
  hasErrors: boolean
  hasWarnings: boolean
  canProceed: boolean
  
  // 错误状态
  error: string | null
}

/**
 * 创建库存验证页面状态管理
 */
export function useInventoryValidationPageState() {
  const state = reactive<InventoryValidationPageState>({
    // 基础状态
    isLoading: false,
    
    // UI控制状态
    activeTab: 'product-not-exist',
    searchKeyword: '',
    filterStatus: 'all',
    dateFilter: '',
    currentPage: 1,
    pageSize: 20,
    
    // 数据状态
    stepData: {},
    
    // 验证状态
    hasErrors: false,
    hasWarnings: false,
    canProceed: false,
    
    // 错误状态
    error: null
  })

  // --- 状态更新方法 (唯一入口) ---
  
  /**
   * 设置加载状态
   */
  const setLoading = (loading: boolean): void => {
    state.isLoading = loading
  }

  /**
   * 设置活动标签页
   */
  const setActiveTab = (tab: string): void => {
    if (typeof tab === 'string') {
      state.activeTab = tab
    }
  }

  /**
   * 设置搜索关键词
   */
  const setSearchKeyword = (keyword: string): void => {
    state.searchKeyword = keyword
    // 搜索时重置到第一页
    state.currentPage = 1
  }

  /**
   * 设置过滤状态
   */
  const setFilterStatus = (status: string): void => {
    state.filterStatus = status
    // 过滤时重置到第一页
    state.currentPage = 1
  }

  /**
   * 设置日期过滤器
   */
  const setDateFilter = (date: string): void => {
    state.dateFilter = date
    // 过滤时重置到第一页
    state.currentPage = 1
  }

  /**
   * 设置当前页码
   */
  const setCurrentPage = (page: number): void => {
    if (page > 0) {
      state.currentPage = page
    }
  }

  /**
   * 设置每页大小
   */
  const setPageSize = (size: number): void => {
    if (size > 0) {
      state.pageSize = size
      // 重置到第一页
      state.currentPage = 1
    }
  }

  /**
   * 更新步骤数据
   */
  const updateStepData = (data: Partial<StepData>): void => {
    state.stepData = { ...state.stepData, ...data }
    
    // 更新验证状态
    updateValidationStatus()
  }

  /**
   * 设置步骤数据
   */
  const setStepData = (data: StepData): void => {
    state.stepData = data
    
    // 更新验证状态
    updateValidationStatus()
  }

  /**
   * 更新验证状态
   */
  const updateValidationStatus = (): void => {
    const validationResult = state.stepData.validationResult
    const stockImportPreview = state.stepData.stockImportPreview
    
    // 优先使用stockImportPreview数据来判断错误状态
    if (stockImportPreview?.items) {
      const unmatchedItems = stockImportPreview.items.filter(item => !item.isMatched)
      const matchedItems = stockImportPreview.items.filter(item => item.isMatched)
      
      // 检查是否有门店商品未在Excel中找到（通过allVenueProducts判断）
      const stepData = state.stepData
      const hasStockNotExistWarnings = (stepData as any).allVenueProducts?.length > 0
      
      state.hasErrors = unmatchedItems.length > 0 // 只有商品不匹配才算错误
      state.hasWarnings = hasStockNotExistWarnings // 有门店商品未在Excel中找到
      state.canProceed = matchedItems.length > 0 // 只要有匹配的商品就可以继续
      
      // 动态设置默认的activeTab
      if (unmatchedItems.length > 0) {
        state.activeTab = 'product-not-exist'
      } else if (hasStockNotExistWarnings) {
        state.activeTab = 'stock-not-exist'
      }
    } else {
      // 回退到传统的验证结果判断
      const errors = validationResult?.errors || []
      const warnings = validationResult?.warnings || []
      
      state.hasErrors = errors.length > 0
      state.hasWarnings = warnings.length > 0
      state.canProceed = !state.hasErrors
    }
  }

  /**
   * 设置错误信息
   */
  const setError = (error: string | null): void => {
    state.error = error
  }

  /**
   * 清除错误信息
   */
  const clearError = (): void => {
    state.error = null
  }

  /**
   * 重置页面状态
   */
  const reset = (): void => {
    state.isLoading = false
    state.activeTab = 'product-not-exist'
    state.searchKeyword = ''
    state.filterStatus = 'all'
    state.dateFilter = ''
    state.currentPage = 1
    state.pageSize = 20
    state.stepData = {}
    state.hasErrors = false
    state.hasWarnings = false
    state.canProceed = false
    state.error = null
  }

  return {
    state: readonly(state), // 只读状态暴露
    // 更新方法暴露
    setLoading,
    setActiveTab,
    setSearchKeyword,
    setFilterStatus,
    setDateFilter,
    setCurrentPage,
    setPageSize,
    updateStepData,
    setStepData,
    updateValidationStatus,
    setError,
    clearError,
    reset
  }
}

export type InventoryValidationPageStateAPI = ReturnType<typeof useInventoryValidationPageState> 