// 存酒数据导入 - 数据验证器

import type { DataValidator, FieldConfig, ValidationResult, ValidationError } from '../../core/types'

/**
 * 存酒数据验证器
 */
export class WineStorageDataValidator implements DataValidator {
  getFieldConfig(): FieldConfig[] {
    return [
      {
        key: '会员编号',
        label: '会员编号',
        type: 'string',
        required: true,
        maxLength: 20,
        description: '存酒会员的唯一标识',
        example: 'M001'
      },
      {
        key: '酒品名称',
        label: '酒品名称',
        type: 'string',
        required: true,
        maxLength: 100,
        description: '存储酒品的名称',
        example: '茅台酒'
      },
      {
        key: '存储数量',
        label: '存储数量',
        type: 'number',
        required: false,
        description: '存储的酒品数量（瓶）',
        example: '2'
      }
    ]
  }

  validate(data: any[]): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationError[] = []
    const fieldConfigs = this.getFieldConfig()
    const memberWinePairs = new Set<string>()

    data.forEach((row, index) => {
      // 验证每个字段
      fieldConfigs.forEach(config => {
        const error = this.validateField(row[config.key], config, index)
        if (error) {
          errors.push(error)
        }
      })

      // 业务规则验证
      this.validateBusinessRules(row, index, errors, warnings, memberWinePairs)
    })

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      validRows: data.length - errors.filter((e, i, arr) => 
        arr.findIndex(err => err.row === e.row) === i
      ).length,
      totalRows: data.length
    }
  }

  validateRow(row: any, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const fieldConfigs = this.getFieldConfig()

    fieldConfigs.forEach(config => {
      const error = this.validateField(row[config.key], config, index)
      if (error) {
        errors.push(error)
      }
    })

    return errors
  }

  validateField(value: any, config: FieldConfig, rowIndex: number): ValidationError | null {
    // 必填字段验证
    if (config.required && (!value || value.toString().trim() === '')) {
      return {
        row: rowIndex,
        field: config.key,
        value,
        message: `${config.label}不能为空`,
        type: 'error'
      }
    }

    // 如果值为空且非必填，跳过其他验证
    if (!value || value.toString().trim() === '') {
      return null
    }

    const stringValue = value.toString().trim()

    // 字符串类型验证
    if (config.type === 'string') {
      if (config.maxLength && stringValue.length > config.maxLength) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}长度不能超过${config.maxLength}个字符`,
          type: 'error'
        }
      }

      if (config.minLength && stringValue.length < config.minLength) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}长度不能少于${config.minLength}个字符`,
          type: 'error'
        }
      }

      if (config.pattern) {
        const regex = new RegExp(config.pattern)
        if (!regex.test(stringValue)) {
          return {
            row: rowIndex,
            field: config.key,
            value,
            message: `${config.label}格式不正确`,
            type: 'error'
          }
        }
      }
    }

    // 数字类型验证
    if (config.type === 'number') {
      const numValue = Number(stringValue)
      if (isNaN(numValue)) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}必须为数字`,
          type: 'error'
        }
      }

      if (numValue < 0) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}不能为负数`,
          type: 'error'
        }
      }

      if (!Number.isInteger(numValue)) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}必须为整数`,
          type: 'error'
        }
      }

      if (numValue > 9999) {
        return {
          row: rowIndex,
          field: config.key,
          value,
          message: `${config.label}不能超过9999`,
          type: 'error'
        }
      }
    }

    return null
  }

  private validateBusinessRules(
    row: any, 
    index: number, 
    errors: ValidationError[], 
    warnings: ValidationError[],
    memberWinePairs: Set<string>
  ): void {
    const memberId = row['会员编号']?.toString().trim()
    const wineName = row['酒品名称']?.toString().trim()
    const quantity = row['存储数量']

    // 检查会员编号格式
    if (memberId && !/^[A-Z0-9]+$/.test(memberId)) {
      warnings.push({
        row: index,
        field: '会员编号',
        value: memberId,
        message: '建议会员编号使用大写字母和数字组合',
        type: 'warning'
      })
    }

    // 检查重复的会员-酒品组合
    if (memberId && wineName) {
      const pairKey = `${memberId}-${wineName}`
      if (memberWinePairs.has(pairKey)) {
        errors.push({
          row: index,
          field: '会员编号',
          value: memberId,
          message: `会员 ${memberId} 的 ${wineName} 存酒记录重复`,
          type: 'error'
        })
      } else {
        memberWinePairs.add(pairKey)
      }
    }

    // 检查存储数量异常情况
    if (quantity) {
      const qty = Number(quantity)
      if (!isNaN(qty)) {
        if (qty === 0) {
          warnings.push({
            row: index,
            field: '存储数量',
            value: quantity,
            message: '存储数量为0，请确认是否需要导入此记录',
            type: 'warning'
          })
        } else if (qty >= 50) {
          warnings.push({
            row: index,
            field: '存储数量',
            value: quantity,
            message: '存储数量较大，请确认数据准确性',
            type: 'warning'
          })
        }
      }
    }

    // 检查酒品名称合理性
    if (wineName) {
      const commonWines = ['茅台酒', '五粮液', '剑南春', '国窖1573', '水井坊', '郎酒', '汾酒', '洋河', '泸州老窖']
      if (!commonWines.some(wine => wineName.includes(wine)) && wineName.length < 3) {
        warnings.push({
          row: index,
          field: '酒品名称',
          value: wineName,
          message: '酒品名称可能不完整，请确认',
          type: 'warning'
        })
      }
    }
  }
}