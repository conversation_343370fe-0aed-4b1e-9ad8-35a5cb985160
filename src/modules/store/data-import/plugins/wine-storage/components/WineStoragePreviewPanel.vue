<template>
  <div class="wine-storage-preview-panel">
    <!-- 预览摘要 -->
    <div class="preview-summary mb-6">
      <h3 class="text-xl font-semibold mb-4 flex items-center gap-2">
        存酒数据预览
      </h3>
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
          <div class="text-sm text-purple-600 font-medium">待导入记录</div>
          <div class="text-2xl font-bold text-purple-700">{{ totalRecords }}</div>
        </div>
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
          <div class="text-sm text-indigo-600 font-medium">存酒会员</div>
          <div class="text-2xl font-bold text-indigo-700">{{ uniqueMemberCount }}</div>
        </div>
        <div class="bg-amber-50 border border-amber-200 rounded-lg p-4">
          <div class="text-sm text-amber-600 font-medium">酒品种类</div>
          <div class="text-2xl font-bold text-amber-700">{{ uniqueWineCount }}</div>
        </div>
        <div class="bg-emerald-50 border border-emerald-200 rounded-lg p-4">
          <div class="text-sm text-emerald-600 font-medium">总存储量</div>
          <div class="text-2xl font-bold text-emerald-700">{{ totalStorageQuantity.toLocaleString() }} 瓶</div>
        </div>
      </div>
    </div>

    <!-- 存酒分析图表 -->
    <div class="wine-storage-analysis mb-6">
      <h4 class="text-lg font-semibold mb-4">存酒分析</h4>
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        
        <!-- 存储量分布 -->
        <div class="bg-white border rounded-lg p-4">
          <h5 class="font-medium mb-3 flex items-center gap-2">
            <el-icon class="text-blue-500"><DataAnalysis /></el-icon>
            存储量分布
          </h5>
          <div class="space-y-3">
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">大量存储 (≥10瓶)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-red-500 h-2 rounded-full" 
                    :style="{ width: `${highStoragePercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ highStorageCount }}</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">中等存储 (5-9瓶)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-yellow-500 h-2 rounded-full" 
                    :style="{ width: `${mediumStoragePercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ mediumStorageCount }}</span>
              </div>
            </div>
            <div class="flex items-center justify-between">
              <span class="text-sm text-gray-600">少量存储 (1-4瓶)</span>
              <div class="flex items-center gap-2">
                <div class="w-16 bg-gray-200 rounded-full h-2">
                  <div 
                    class="bg-green-500 h-2 rounded-full" 
                    :style="{ width: `${lowStoragePercentage}%` }"
                  ></div>
                </div>
                <span class="text-sm font-medium">{{ lowStorageCount }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 热门酒品TOP5 -->
        <div class="bg-white border rounded-lg p-4">
          <h5 class="font-medium mb-3 flex items-center gap-2">
            <el-icon class="text-amber-500"><Trophy /></el-icon>
            热门存酒品种 TOP5
          </h5>
          <div class="space-y-3">
            <div 
              v-for="(wine, index) in topWines"
              :key="wine.name"
              class="flex items-center gap-3"
            >
              <div 
                class="w-6 h-6 rounded-full flex items-center justify-center text-white text-xs font-bold"
                :class="getRankingColor(index)"
              >
                {{ index + 1 }}
              </div>
              <div class="flex-1">
                <div class="text-sm font-medium">{{ wine.name }}</div>
                <div class="text-xs text-gray-500">{{ wine.count }} 条记录</div>
              </div>
              <div class="text-right">
                <div class="text-sm font-semibold">{{ wine.quantity }} 瓶</div>
                <div class="text-xs text-gray-500">{{ wine.percentage }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 会员存酒情况 -->
    <div class="member-analysis mb-6">
      <h4 class="text-lg font-semibold mb-4">会员存酒情况</h4>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
          <div class="text-sm text-blue-600">活跃会员</div>
          <div class="text-xl font-bold text-blue-700">{{ activeMembers }}</div>
          <div class="text-xs text-blue-500">存酒≥5瓶的会员</div>
        </div>
        <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
          <div class="text-sm text-green-600">平均存储</div>
          <div class="text-xl font-bold text-green-700">{{ averageStoragePerMember }}</div>
          <div class="text-xs text-green-500">每会员平均存酒量</div>
        </div>
        <div class="bg-gradient-to-r from-amber-50 to-yellow-50 border border-amber-200 rounded-lg p-4">
          <div class="text-sm text-amber-600">存酒价值</div>
          <div class="text-xl font-bold text-amber-700">¥{{ estimatedValue.toLocaleString() }}</div>
          <div class="text-xs text-amber-500">估算总价值</div>
        </div>
      </div>
    </div>

    <!-- 存酒数据表格 -->
    <div class="wine-storage-table mb-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold">存酒明细</h4>
        <div class="flex items-center gap-3">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索会员编号或酒品名称..."
            size="small"
            style="width: 250px;"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-select v-model="filterType" placeholder="筛选类型" size="small" style="width: 120px;">
            <el-option label="全部" value="all" />
            <el-option label="大量存储" value="high" />
            <el-option label="中等存储" value="medium" />
            <el-option label="少量存储" value="low" />
          </el-select>
        </div>
      </div>

      <el-table
        :data="filteredData"
        style="width: 100%"
        max-height="500"
        stripe
        border
        :row-class-name="getRowClassName"
      >
        <el-table-column prop="会员编号" label="会员编号" width="130" fixed="left">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <el-icon class="text-blue-500"><User /></el-icon>
              <span class="font-mono">{{ row['会员编号'] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="酒品名称" label="酒品名称" min-width="150">
          <template #default="{ row }">
            <div class="flex items-center gap-2">
              <span class="font-medium">{{ row['酒品名称'] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="存储数量" label="存储数量" width="120" align="right" sortable>
          <template #default="{ row }">
            <span :class="getQuantityClass(row['存储数量'])">
              {{ formatNumber(row['存储数量']) }}
            </span>
            <span class="text-xs text-gray-500 ml-1">瓶</span>
          </template>
        </el-table-column>
        <el-table-column label="存储等级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStorageTagType(row['存储数量'])" size="small">
              {{ getStorageLevel(row['存储数量']) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="存储价值" width="120" align="right">
          <template #default="{ row }">
            <span class="text-green-600 font-semibold">
              ¥{{ getStorageValue(row).toLocaleString() }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="VIP等级" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getVipTagType(row)" size="small">
              {{ getVipLevel(row) }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="mt-4 flex justify-center">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="filteredData.length"
        />
      </div>
    </div>

    <!-- 底部操作 -->
    <div class="flex justify-between items-center">
      <div class="text-sm text-gray-500">
        确认数据无误后，点击"确认导入"开始导入存酒记录
      </div>
      <div class="space-x-3">
        <el-button @click="$emit('prev')">
          上一步
        </el-button>
        <el-button type="primary" @click="$emit('next')">
          确认导入
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import { DataAnalysis, Trophy, User, Search } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits } from '../../../core/types'

// Props 和 Emits
const props = defineProps<ImportStepProps>()
const emit = defineEmits<ImportStepEmits>()

// 响应式数据
const searchKeyword = ref('')
const filterType = ref('all')
const currentPage = ref(1)
const pageSize = ref(20)

// 计算属性
const previewData = computed(() => props.stepData.parsedData?.data || [])

const totalRecords = computed(() => previewData.value.length)

const uniqueMemberCount = computed(() => {
  const members = new Set(previewData.value.map((row: any) => row['会员编号']))
  return members.size
})

const uniqueWineCount = computed(() => {
  const wines = new Set(previewData.value.map((row: any) => row['酒品名称']))
  return wines.size
})

const totalStorageQuantity = computed(() => {
  return previewData.value.reduce((total: number, row: any) => {
    return total + (parseInt(row['存储数量']) || 0)
  }, 0)
})

// 存储量分布统计
const storageDistribution = computed(() => {
  const high = previewData.value.filter((row: any) => (parseInt(row['存储数量']) || 0) >= 10).length
  const medium = previewData.value.filter((row: any) => {
    const qty = parseInt(row['存储数量']) || 0
    return qty >= 5 && qty < 10
  }).length
  const low = previewData.value.filter((row: any) => {
    const qty = parseInt(row['存储数量']) || 0
    return qty > 0 && qty < 5
  }).length
  
  return { high, medium, low }
})

const highStorageCount = computed(() => storageDistribution.value.high)
const mediumStorageCount = computed(() => storageDistribution.value.medium)
const lowStorageCount = computed(() => storageDistribution.value.low)

const highStoragePercentage = computed(() => 
  totalRecords.value > 0 ? (highStorageCount.value / totalRecords.value) * 100 : 0
)
const mediumStoragePercentage = computed(() => 
  totalRecords.value > 0 ? (mediumStorageCount.value / totalRecords.value) * 100 : 0
)
const lowStoragePercentage = computed(() => 
  totalRecords.value > 0 ? (lowStorageCount.value / totalRecords.value) * 100 : 0
)

// 热门酒品统计
const topWines = computed(() => {
  const wineStats = new Map<string, { count: number; quantity: number }>()
  
  previewData.value.forEach((row: any) => {
    const wineName = row['酒品名称']
    const quantity = parseInt(row['存储数量']) || 0
    
    if (wineName) {
      if (!wineStats.has(wineName)) {
        wineStats.set(wineName, { count: 0, quantity: 0 })
      }
      const stats = wineStats.get(wineName)!
      stats.count++
      stats.quantity += quantity
    }
  })
  
  const totalQuantity = totalStorageQuantity.value
  
  return Array.from(wineStats.entries())
    .map(([name, stats]) => ({
      name,
      count: stats.count,
      quantity: stats.quantity,
      percentage: totalQuantity > 0 ? Math.round((stats.quantity / totalQuantity) * 100) : 0
    }))
    .sort((a, b) => b.quantity - a.quantity)
    .slice(0, 5)
})

// 会员分析
const activeMembers = computed(() => {
  const memberStorage = new Map<string, number>()
  
  previewData.value.forEach((row: any) => {
    const member = row['会员编号']
    const quantity = parseInt(row['存储数量']) || 0
    
    if (member) {
      memberStorage.set(member, (memberStorage.get(member) || 0) + quantity)
    }
  })
  
  return Array.from(memberStorage.values()).filter(storage => storage >= 5).length
})

const averageStoragePerMember = computed(() => {
  if (uniqueMemberCount.value === 0) return 0
  return Math.round(totalStorageQuantity.value / uniqueMemberCount.value * 10) / 10
})

const estimatedValue = computed(() => {
  // 假设平均每瓶价值800元
  return totalStorageQuantity.value * 800
})

// 过滤后的数据
const filteredData = computed(() => {
  let filtered = previewData.value

  // 搜索过滤
  if (searchKeyword.value) {
    filtered = filtered.filter((row: any) => 
      row['会员编号']?.toString().toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      row['酒品名称']?.toString().toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
  }

  // 类型过滤
  if (filterType.value !== 'all') {
    filtered = filtered.filter((row: any) => {
      const qty = parseInt(row['存储数量']) || 0
      
      switch (filterType.value) {
        case 'high':
          return qty >= 10
        case 'medium':
          return qty >= 5 && qty < 10
        case 'low':
          return qty > 0 && qty < 5
        default:
          return true
      }
    })
  }

  return filtered
})

// 方法
const formatNumber = (value: any): string => {
  const num = parseInt(value) || 0
  return num.toLocaleString()
}

const getQuantityClass = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty >= 10) return 'text-red-600 font-bold'
  if (qty >= 5) return 'text-yellow-600 font-semibold'
  return 'text-gray-900'
}

const getStorageTagType = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty >= 10) return 'danger'
  if (qty >= 5) return 'warning'
  return 'success'
}

const getStorageLevel = (quantity: any): string => {
  const qty = parseInt(quantity) || 0
  if (qty >= 10) return '大量'
  if (qty >= 5) return '中等'
  return '少量'
}

const getStorageValue = (row: any): number => {
  const qty = parseInt(row['存储数量']) || 0
  return qty * 800 // 假设每瓶800元
}

const getVipLevel = (row: any): string => {
  const value = getStorageValue(row)
  if (value >= 50000) return '钻石'
  if (value >= 20000) return '白金'
  if (value >= 10000) return '黄金'
  if (value >= 5000) return '白银'
  return '青铜'
}

const getVipTagType = (row: any): string => {
  const level = getVipLevel(row)
  switch (level) {
    case '钻石': return 'danger'
    case '白金': return 'info'
    case '黄金': return 'warning'
    case '白银': return 'success'
    default: return ''
  }
}

const getRankingColor = (index: number): string => {
  const colors = [
    'bg-yellow-500',   // 金色第1名
    'bg-gray-400',     // 银色第2名
    'bg-amber-600',    // 铜色第3名
    'bg-blue-500',     // 蓝色第4名
    'bg-green-500'     // 绿色第5名
  ]
  return colors[index] || 'bg-gray-400'
}

const getRowClassName = ({ row }: { row: any }): string => {
  const qty = parseInt(row['存储数量']) || 0
  if (qty >= 10) return 'bg-red-50'
  if (qty >= 5) return 'bg-yellow-50'
  return ''
}
</script>

<style scoped>
.wine-storage-preview-panel {
  max-width: 100%;
}

.preview-summary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-radius: 12px;
  padding: 20px;
}

.wine-storage-analysis, .member-analysis {
  background: #ffffff;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #f8fafc;
  font-weight: 600;
}

:deep(.el-table .bg-red-50) {
  background-color: #fef2f2;
}

:deep(.el-table .bg-yellow-50) {
  background-color: #fffbeb;
}
</style> 