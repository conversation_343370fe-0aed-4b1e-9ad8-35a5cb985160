// 存酒数据导入 - 数据预览器

import type { DataPreviewer } from '../../core/types'

/**
 * 存酒数据预览器
 */
export class WineStorageDataPreviewer implements DataPreviewer {
  getPreviewColumns(): Array<{ key: string; label: string; width?: number }> {
    return [
      { key: '会员编号', label: '会员编号', width: 130 },
      { key: '酒品名称', label: '酒品名称', width: 150 },
      { key: '存储数量', label: '存储数量', width: 120 }
    ]
  }

  formatPreviewData(data: any[]): any[] {
    return data.map(row => ({
      '会员编号': row['会员编号'] || '',
      '酒品名称': row['酒品名称'] || '',
      '存储数量': this.formatNumber(row['存储数量']),
      // 添加计算字段
      '存储等级': this.getStorageLevel(row),
      '会员类型': this.getMemberType(row['会员编号']),
      '存储价值': this.getStorageValue(row)
    }))
  }

  getPreviewSummary(data: any[]): Record<string, any> {
    const totalRows = data.length
    const uniqueMembers = new Set(data.map(row => row['会员编号'])).size
    const uniqueWines = new Set(data.map(row => row['酒品名称'])).size
    
    const totalQuantity = data.reduce((sum, row) => {
      return sum + (parseInt(row['存储数量']) || 0)
    }, 0)

    const averagePerMember = uniqueMembers > 0 ? 
      Math.round(totalQuantity / uniqueMembers * 10) / 10 : 0

    const estimatedValue = totalQuantity * 800 // 假设每瓶800元

    // 存储等级分布
    const highStorage = data.filter(row => (parseInt(row['存储数量']) || 0) >= 10).length
    const mediumStorage = data.filter(row => {
      const qty = parseInt(row['存储数量']) || 0
      return qty >= 5 && qty < 10
    }).length
    const lowStorage = data.filter(row => {
      const qty = parseInt(row['存储数量']) || 0
      return qty > 0 && qty < 5
    }).length

    // 热门酒品TOP3
    const wineStats = new Map<string, number>()
    data.forEach(row => {
      const wine = row['酒品名称']
      const qty = parseInt(row['存储数量']) || 0
      if (wine) {
        wineStats.set(wine, (wineStats.get(wine) || 0) + qty)
      }
    })

    const topWines = Array.from(wineStats.entries())
      .sort(([,a], [,b]) => b - a)
      .slice(0, 3)
      .map(([name, quantity]) => `${name}(${quantity}瓶)`)

    return {
      总记录数: totalRows,
      存酒会员: uniqueMembers,
      酒品种类: uniqueWines,
      总存储量: `${totalQuantity.toLocaleString()}瓶`,
      平均存储: `${averagePerMember}瓶/会员`,
      估算价值: `¥${estimatedValue.toLocaleString()}`,
      大量存储: `${highStorage}条记录`,
      中等存储: `${mediumStorage}条记录`,
      少量存储: `${lowStorage}条记录`,
      热门酒品: topWines.join('、') || '无'
    }
  }

  private formatNumber(value: any): string {
    const num = parseInt(value) || 0
    return num.toLocaleString()
  }

  private getStorageLevel(row: any): string {
    const quantity = parseInt(row['存储数量']) || 0
    if (quantity >= 10) return '大量存储'
    if (quantity >= 5) return '中等存储'
    if (quantity > 0) return '少量存储'
    return '无存储'
  }

  private getMemberType(memberId: any): string {
    const id = memberId?.toString() || ''
    if (id.startsWith('VIP')) return 'VIP会员'
    if (id.startsWith('P')) return '白金会员'
    if (id.startsWith('G')) return '黄金会员'
    return '普通会员'
  }

  private getStorageValue(row: any): string {
    const quantity = parseInt(row['存储数量']) || 0
    const value = quantity * 800 // 假设每瓶800元
    return `¥${value.toLocaleString()}`
  }
}