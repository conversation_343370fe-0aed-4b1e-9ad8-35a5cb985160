// 存酒数据导入 - 数据导入器

import type { DataImporter, ImportResult } from '../../core/types'

/**
 * 存酒数据导入器
 */
export class WineStorageDataImporter implements DataImporter {
  private progressCallback?: (progress: number) => void
  private isCancelled = false
  private currentProgress = 0

  async import(data: any[], storeId: string): Promise<ImportResult> {
    this.resetImportState()
    
    const startTime = Date.now()
    let successCount = 0
    let errorCount = 0
    const errors: any[] = []

    try {
      for (let i = 0; i < data.length; i++) {
        if (this.checkCancellation()) {
          break
        }

        const row = data[i]
        
        try {
          // 模拟导入过程
          await this.simulateImportRow(row, storeId)
          successCount++
        } catch (error) {
          errorCount++
          errors.push({
            row: i + 1,
            field: '',
            value: '',
            message: (error as Error).message,
            type: 'error'
          })
        }

        // 更新进度
        this.updateProgress((i + 1) / data.length * 100)
      }

      const executionTime = Date.now() - startTime

      return {
        success: errorCount === 0,
        totalRows: data.length,
        successRows: successCount,
        errorRows: errorCount,
        warnings: [],
        errors,
        executionTime,
        timestamp: Date.now(),
        summary: errorCount === 0 
          ? `成功导入 ${successCount} 条存酒记录`
          : `导入完成，成功 ${successCount} 条，失败 ${errorCount} 条`
      }
    } catch (error) {
      return {
        success: false,
        totalRows: data.length,
        successRows: successCount,
        errorRows: data.length - successCount,
        warnings: [],
        errors: [{
          row: 0,
          field: '',
          value: '',
          message: `导入过程中发生错误：${(error as Error).message}`,
          type: 'error'
        }],
        executionTime: Date.now() - startTime,
        timestamp: Date.now(),
        summary: '导入失败'
      }
    }
  }

  cancel(): void {
    this.isCancelled = true
  }

  getProgress(): number {
    return this.currentProgress
  }

  onProgress(callback: (progress: number) => void): void {
    this.progressCallback = callback
  }

  private resetImportState(): void {
    this.isCancelled = false
    this.currentProgress = 0
  }

  private checkCancellation(): boolean {
    return this.isCancelled
  }

  private updateProgress(progress: number): void {
    this.currentProgress = Math.min(100, Math.max(0, progress))
    this.progressCallback?.(this.currentProgress)
  }

  private async simulateImportRow(row: any, storeId: string): Promise<void> {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 80))
    
    // 模拟验证
    if (!row['会员编号']) {
      throw new Error('会员编号不能为空')
    }
    
    if (!row['酒品名称']) {
      throw new Error('酒品名称不能为空')
    }

    // 模拟随机失败（8%概率）
    if (Math.random() < 0.08) {
      throw new Error(`会员 ${row['会员编号']} 的存酒记录导入失败：系统异常`)
    }

    // 模拟存酒业务逻辑
    const memberId = row['会员编号']
    const wineName = row['酒品名称']
    const quantity = parseInt(row['存储数量']) || 0

    console.log(`导入存酒记录: 门店${storeId}, 会员${memberId}, 酒品${wineName}, 数量${quantity}瓶`)
  }
}