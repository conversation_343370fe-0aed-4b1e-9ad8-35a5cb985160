// 存酒数据导入 - 模板生成器

import type { TemplateGenerator, TemplateData } from '../../core/types'

/**
 * 存酒模板生成器
 */
export class WineStorageTemplateGenerator implements TemplateGenerator {
  getTemplateData(): TemplateData {
    return {
      headers: ['会员编号', '酒品名称', '存储数量'],
      fields: [
        {
          key: '会员编号',
          label: '会员编号',
          type: 'string',
          required: true,
          maxLength: 20,
          description: '存酒会员的唯一标识',
          example: 'M001'
        },
        {
          key: '酒品名称',
          label: '酒品名称',
          type: 'string',
          required: true,
          maxLength: 100,
          description: '存储酒品的名称',
          example: '茅台酒'
        },
        {
          key: '存储数量',
          label: '存储数量',
          type: 'number',
          required: false,
          description: '存储的酒品数量（瓶）',
          example: '2'
        }
      ],
      validationRules: [
        {
          field: '会员编号',
          type: 'required',
          message: '会员编号不能为空'
        },
        {
          field: '酒品名称',
          type: 'required',
          message: '酒品名称不能为空'
        },
        {
          field: '存储数量',
          type: 'type',
          message: '存储数量必须为数字'
        }
      ],
      exampleData: [
        {
          '会员编号': 'M001',
          '酒品名称': '茅台酒',
          '存储数量': '2'
        },
        {
          '会员编号': 'M002',
          '酒品名称': '五粮液',
          '存储数量': '1'
        }
      ],
      instructions: [
        '请按照以下要求填写存酒信息：',
        '• 会员编号：存酒会员的唯一标识，如：M001、VIP001',
        '• 酒品名称：存储酒品的名称，如：茅台酒、五粮液',
        '• 存储数量：存储的酒品数量，单位为瓶',
        '',
        '注意事项：',
        '• 会员编号和酒品名称为必填项',
        '• 存储数量必须为非负整数',
        '• 单次存储数量不能超过9999瓶',
        '• 保存为CSV格式文件进行导入'
      ]
    }
  }

  generateCSV(): string {
    const csvRows: string[] = []
    
    // 添加标题行
    csvRows.push(['会员编号', '酒品名称', '存储数量'].join(','))
    
    // 添加示例数据
    csvRows.push('M001,茅台酒,2')
    csvRows.push('M002,五粮液,1')
    csvRows.push('M003,剑南春,3')
    csvRows.push('M004,国窖1573,1')
    csvRows.push('M005,水井坊,2')
    
    return csvRows.join('\n')
  }

  getExampleData(): Record<string, any>[] {
    return [
      {
        '会员编号': 'M001',
        '酒品名称': '茅台酒',
        '存储数量': '2'
      },
      {
        '会员编号': 'M002',
        '酒品名称': '五粮液',
        '存储数量': '1'
      },
      {
        '会员编号': 'M003',
        '酒品名称': '剑南春',
        '存储数量': '3'
      },
      {
        '会员编号': 'M004',
        '酒品名称': '国窖1573',
        '存储数量': '1'
      },
      {
        '会员编号': 'M005',
        '酒品名称': '水井坊',
        '存储数量': '2'
      }
    ]
  }
} 