// 数据导入插件核心类型定义

import type { Component } from 'vue'

// 基础类型定义
export interface ValidationError {
  row: number
  field: string
  value: any
  message: string
  type: 'error' | 'warning'
}

export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationError[]
  validRows: number
  totalRows: number
}

export interface ImportResult {
  success: boolean
  totalRows: number
  successRows: number
  errorRows: number
  warnings: ValidationError[]
  errors: ValidationError[]
  executionTime: number
  timestamp: number
  summary?: string
}

// 模板数据结构
export interface TemplateField {
  key: string
  label: string
  type: 'string' | 'number' | 'date' | 'boolean' | 'email' | 'phone'
  required: boolean
  maxLength?: number
  minLength?: number
  pattern?: string
  description?: string
  example?: string
}

export interface ValidationRule {
  field: string
  type: 'required' | 'type' | 'length' | 'pattern' | 'custom'
  message: string
  value?: any
  validator?: (value: any, row: any) => boolean | Promise<boolean>
}

export interface TemplateData {
  headers: string[]
  fields: TemplateField[]
  validationRules: ValidationRule[]
  exampleData: Record<string, any>[]
  instructions: string[]
  description?: string
}

// 字段配置
export interface FieldConfig {
  key: string
  label: string
  type: 'string' | 'number' | 'date' | 'boolean' | 'email' | 'phone'
  required: boolean
  maxLength?: number
  minLength?: number
  pattern?: string
  description?: string
  example?: string
}

// 插件基础信息
export interface PluginInfo {
  id: string
  name: string
  displayName: string
  description: string
  version: string
  author?: string
  icon: string
  enabled: boolean
  order?: number
}

// 插件生命周期钩子
export interface PluginLifecycleHooks {
  onInstall?(): Promise<void>
  onUninstall?(): Promise<void>
  onEnable?(): Promise<void>
  onDisable?(): Promise<void>
  beforeImport?(data: any[]): Promise<void>
  afterImport?(result: ImportResult): Promise<void>
}

// 模板生成器接口
export interface TemplateGenerator {
  getTemplateData(): TemplateData
  generateCSV(): string
  getExampleData(): Record<string, any>[]
}

// 数据验证器接口
export interface DataValidator {
  getFieldConfig(): FieldConfig[]
  validate(data: any[]): ValidationResult
  validateRow(row: any, index: number): ValidationError[]
  validateField(value: any, config: FieldConfig, rowIndex: number): ValidationError | null
}

// 数据预览器接口
export interface DataPreviewer {
  getPreviewColumns(): Array<{ key: string; label: string; width?: number }>
  formatPreviewData(data: any[]): any[]
  getPreviewSummary(data: any[]): Record<string, any>
}

// 数据导入器接口
export interface DataImporter {
  import(data: any[], storeId: string): Promise<ImportResult>
  cancel(): void
  getProgress(): number
  onProgress(callback: (progress: number) => void): void
}

// 插件自定义组件接口
export interface PluginCustomComponents {
  // 自定义验证面板组件
  validationPanel?: any
  // 自定义预览面板组件
  previewPanel?: any
  // 自定义结果面板组件
  resultPanel?: any
  // 自定义进度面板组件
  progressPanel?: any
}

// 插件组件配置
export interface PluginComponentConfig {
  // 是否使用自定义验证预览页面
  useCustomValidationPreview?: boolean
  // 是否使用自定义结果页面
  useCustomResultPage?: boolean
  // 自定义步骤配置
  customSteps?: ImportStep[]
}

// 插件主接口
export interface DataImportPlugin extends PluginInfo, PluginLifecycleHooks {
  templateGenerator: TemplateGenerator
  dataValidator: DataValidator
  dataPreviewer: DataPreviewer
  dataImporter: DataImporter
  
  // 自定义组件
  customComponents?: PluginCustomComponents
  
  // 组件配置
  componentConfig?: PluginComponentConfig
}

export interface ImportStepProps {
  plugin: DataImportPlugin | null
  stepData: Record<string, any>
  currentStep: number
  isProcessing: boolean
}

export interface ImportStepEmits {
  'update:step-data': [data: Record<string, any>]
  'next': []
  'prev': []
  'complete': []
}

export interface ImportStep {
  key: string
  title: string
  component: any
  canProceed?: (stepData: Record<string, any>) => boolean
}

// 插件注册配置
export interface PluginRegistryConfig {
  autoDiscover: boolean
  pluginPaths: string[]
  enabledPlugins: string[]
  disabledPlugins: string[]
}

// 插件状态
export interface PluginState {
  id: string
  status: 'installed' | 'enabled' | 'disabled' | 'error'
  error?: string
  lastUpdated: number
}

// 插件管理器接口
export interface PluginManager {
  register(plugin: DataImportPlugin): void
  unregister(pluginId: string): void
  enable(pluginId: string): void
  disable(pluginId: string): void
  getPlugin(pluginId: string): DataImportPlugin | null
  getAllPlugins(): DataImportPlugin[]
  getEnabledPlugins(): DataImportPlugin[]
  getPluginState(pluginId: string): PluginState | null
}

// 插件工厂接口
export interface PluginFactory {
  createPlugin(config: any): DataImportPlugin
  validateConfig(config: any): boolean
} 