import type { ComputedRef, WritableComputedRef, Ref } from 'vue';
import type { VenueVO } from '@/entities/store/venue';
import type { StoreSelectorProps, StoreSelectorEmits } from './type';

// 视图直接绑定的状态接口
export interface IStoreSelectorState {
    readonly selectedStoreId: WritableComputedRef<string>;
    readonly stores: ComputedRef<VenueVO[]>;
    readonly isLoading: ComputedRef<boolean>;
    readonly loadingMore: ComputedRef<boolean>; // 加载更多数据的状态
    readonly error: ComputedRef<string>;
    searchKeyword: WritableComputedRef<string>; // 搜索关键词
    readonly currentPage: ComputedRef<number>; // 当前页码
    readonly pageSize: ComputedRef<number>; // 每页数量
    readonly total: ComputedRef<number>; // 总记录数
}

// 视图使用的计算属性接口
export interface IStoreSelectorComputed {
    readonly selectedStore: ComputedRef<VenueVO | null>;
    readonly hasStores: ComputedRef<boolean>;
    readonly displayStores: ComputedRef<VenueVO[]>;
    readonly totalPages: ComputedRef<number>; // 总页数
    readonly hasMore: ComputedRef<boolean>; // 是否有更多数据
    readonly showPagination: ComputedRef<boolean>; // 是否显示分页
    readonly formattedSelectedStoreAddress: ComputedRef<string>; // 格式化的选中门店地址
}

// 视图可触发的操作接口
export interface IStoreSelectorActions {
    loadStores(): Promise<void>;
    loadMoreStores(): Promise<void>; // 加载更多（无限滚动）
    selectStore(storeId: string): void;
    clearSelection(): void;
    updateSearchKeyword(keyword: string): void; // 更新搜索关键词
    search(): Promise<void>; // 执行搜索
    changePage(page: number): Promise<void>; // 切换页码
    changePageSize(size: number): Promise<void>; // 切换每页数量
    handleDropdownVisibleChange(visible: boolean): void; // 下拉框显示状态变化处理
    handleScroll(event: Event): void; // 滚动处理函数
    handleRemoteSearch(query: string): void; // 远程搜索处理函数
    // View层专用的actions
    initScrollListener(): void; // 初始化滚动监听器
    destroyScrollListener(): void; // 销毁滚动监听器
    handlePropsChange(newValue: string): void; // 处理外部Props变化
    handleSelectionChange(newStoreId: string): void; // 处理选择变化
}

// 视图层的引用和状态接口
export interface IStoreSelectorViewRefs {
    readonly selectRef: Ref<any>;
    readonly scrollElement: Ref<HTMLElement | null>;
}

// 组合的视图模型接口
export interface IStoreSelectorViewModel {
    state: IStoreSelectorState;
    computed: IStoreSelectorComputed;
    actions: IStoreSelectorActions;
    refs: IStoreSelectorViewRefs;
    // Props和Emits设置方法
    setupProps(props: StoreSelectorProps): void;
    setupEmits(emit: StoreSelectorEmits): void;
} 