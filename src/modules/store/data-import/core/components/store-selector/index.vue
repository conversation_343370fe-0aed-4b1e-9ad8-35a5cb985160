<template>
  <div class="store-selector">
    <h3 class="text-lg font-semibold text-gray-900 mb-4">选择目标门店</h3>
    
    <!-- 错误提示 -->
    <div v-if="vm.state.error.value" class="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
      <div class="flex items-center gap-2">
        <el-icon class="text-red-600" :size="16">
          <Warning />
        </el-icon>
        <span class="text-sm text-red-700">{{ vm.state.error.value }}</span>
      </div>
    </div>

    <div class="space-y-4">
      <!-- 搜索选择下拉框 -->
      <el-select 
        v-model="vm.state.selectedStoreId.value" 
        placeholder="搜索并选择门店"
        class="w-full max-w-md"
        size="large"
        :loading="vm.state.isLoading.value && !vm.computed.hasStores.value"
        filterable
        remote
        reserve-keyword
        :remote-method="vm.actions.handleRemoteSearch"
        clearable
        @clear="vm.actions.clearSelection"
        @visible-change="vm.actions.handleDropdownVisibleChange"
        ref="vm.refs.selectRef"
      >
        <el-option
          v-for="store in vm.computed.displayStores.value"
          :key="store.id"
          :label="store.name"
          :value="store.id"
        >
          <div class="flex justify-between items-center">
            <span>{{ store.name }}</span>
            <span class="text-xs text-gray-500">{{ store.city }}{{ store.district }}</span>
          </div>
        </el-option>
        
        <!-- 加载更多提示 -->
        <el-option 
          v-if="vm.state.loadingMore.value" 
          :value="'loading'" 
          disabled
        >
          <div class="flex items-center justify-center gap-2 text-sm text-gray-500 py-2">
            <el-icon class="animate-spin" :size="14">
              <Loading />
            </el-icon>
            <span>正在加载更多...</span>
          </div>
        </el-option>
        
        <!-- 无更多数据提示 -->
        <el-option 
          v-else-if="!vm.computed.hasMore.value && vm.computed.hasStores.value" 
          :value="'no-more'" 
          disabled
        >
          <div class="text-center text-sm text-gray-400 py-2">
            已加载全部数据
          </div>
        </el-option>
        
        <!-- 无数据提示 -->
        <el-option 
          v-else-if="!vm.computed.hasStores.value && !vm.state.isLoading.value" 
          :value="'no-data'" 
          disabled
        >
          <div class="text-center text-sm text-gray-400 py-2">
            暂无门店数据
          </div>
        </el-option>
      </el-select>
      
      <!-- 初始加载状态 -->
      <div v-if="vm.state.isLoading.value && !vm.computed.hasStores.value" class="flex items-center gap-2 text-sm text-gray-500">
        <el-icon class="animate-spin" :size="14">
          <Loading />
        </el-icon>
        <span>正在加载门店列表...</span>
      </div>

      <!-- 无门店提示 -->
      <div v-else-if="!vm.computed.hasStores.value && !vm.state.error.value && !vm.state.isLoading.value" class="text-sm text-gray-500">
        暂无可用门店
      </div>
      
      <!-- 选中门店的详细信息 -->
      <div v-if="vm.computed.selectedStore.value" class="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <div class="flex items-center gap-3">
          <el-icon class="text-blue-600" :size="20">
            <Shop />
          </el-icon>
          <div>
            <h4 class="font-medium text-blue-900">{{ vm.computed.selectedStore.value.name }}</h4>
            <p class="text-sm text-blue-700">
              门店ID：{{ vm.computed.selectedStore.value.id }} | 
              地址：{{ vm.computed.formattedSelectedStoreAddress.value }}
            </p>
            <p class="text-xs text-blue-600 mt-1">
              联系人：{{ vm.computed.selectedStore.value.contact }} | 
              电话：{{ vm.computed.selectedStore.value.contactPhone }}
            </p>
          </div>
        </div>
      </div>

      <!-- 重新加载按钮 -->
      <div v-if="vm.state.error.value" class="mt-4">
        <el-button 
          @click="vm.actions.loadStores"
          :loading="vm.state.isLoading.value"
          type="primary"
          size="small"
        >
          重新加载
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Shop, Warning, Loading } from '@element-plus/icons-vue'
import { useStoreSelectorPresenter } from './presenter'
import type { IStoreSelectorViewModel } from './viewmodel'
import type { StoreSelectorProps, StoreSelectorEmits } from './type'

// 使用type.ts中定义的接口
const props = withDefaults(defineProps<StoreSelectorProps>(), {
  modelValue: ''
})

const emit = defineEmits<StoreSelectorEmits>()

// 获取 ViewModel 实例 - View层唯一依赖
const vm: IStoreSelectorViewModel = useStoreSelectorPresenter()

// 设置Props和Emits处理 - 完全委托给Presenter层
vm.setupProps(props)
vm.setupEmits(emit)

// View 层职责：纯粹的模板绑定，所有逻辑都在Presenter层处理
</script> 