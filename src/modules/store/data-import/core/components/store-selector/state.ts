import { reactive, readonly } from 'vue';
import type { VenueVO } from '@/entities/store/venue';

// 定义权威页面状态的结构
export interface StoreSelectorPageState {
    selectedStoreId: string;
    stores: VenueVO[];
    isLoading: boolean;
    loadingMore: boolean; // 加载更多数据的状态，独立于主加载状态
    error: string;
    searchKeyword: string; // 搜索关键词
    currentPage: number; // 当前页码
    pageSize: number; // 每页数量
    total: number; // 总记录数
}

// 创建/提供页面状态实例的组合函数
export function useStoreSelectorPageState() {
    const state = reactive<StoreSelectorPageState>({
        selectedStoreId: '',
        stores: [],
        isLoading: false,
        loadingMore: false,
        error: '',
        searchKeyword: '',
        currentPage: 1,
        pageSize: 10,
        total: 0,
    });

    // --- 状态更新方法 (唯一入口) ---
    const setSelectedStoreId = (storeId: string): void => {
        state.selectedStoreId = storeId;
    };

    const setStores = (stores: VenueVO[]): void => {
        state.stores = stores;
    };

    const appendStores = (newStores: VenueVO[]): void => {
        // 去重并追加新门店，避免重复
        const existingIds = new Set(state.stores.map(store => store.id));
        const uniqueNewStores = newStores.filter(store => !existingIds.has(store.id));
        state.stores.push(...uniqueNewStores);
    };

    const setLoading = (loading: boolean): void => {
        state.isLoading = loading;
    };

    const setLoadingMore = (loading: boolean): void => {
        state.loadingMore = loading;
    };

    const setError = (error: string): void => {
        state.error = error;
    };

    const clearError = (): void => {
        state.error = '';
    };

    const clearSelection = (): void => {
        state.selectedStoreId = '';
    };

    const setSearchKeyword = (keyword: string): void => {
        state.searchKeyword = keyword;
    };

    const setCurrentPage = (page: number): void => {
        state.currentPage = page;
    };

    const setPageSize = (size: number): void => {
        state.pageSize = size;
    };

    const setTotal = (total: number): void => {
        state.total = total;
    };

    const reset = (): void => {
        state.selectedStoreId = '';
        state.stores = [];
        state.isLoading = false;
        state.loadingMore = false;
        state.error = '';
        state.searchKeyword = '';
        state.currentPage = 1;
        state.pageSize = 10;
        state.total = 0;
    };

    return {
        state: readonly(state), // 只读状态暴露
        // 更新方法暴露
        setSelectedStoreId,
        setStores,
        appendStores,
        setLoading,
        setLoadingMore,
        setError,
        clearError,
        clearSelection,
        setSearchKeyword,
        setCurrentPage,
        setPageSize,
        setTotal,
        reset,
    };
}

export type StoreSelectorPageStateAPI = ReturnType<typeof useStoreSelectorPageState>; 