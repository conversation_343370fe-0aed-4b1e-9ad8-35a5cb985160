import type { VenueVO } from '@/entities/store/venue';

// 组件Props接口
export interface StoreSelectorProps {
  modelValue?: string;
}

// 组件Emits接口
export interface StoreSelectorEmits {
  (e: 'update:modelValue', value: string): void;
  (e: 'change', store: VenueVO | null): void;
}

// 组件对外暴露的数据结构
export interface StoreSelectorChangeEvent {
  storeId: string;
  store: VenueVO | null;
}

// 组件实例接口（如果需要通过ref调用）
export interface StoreSelectorInstance {
  loadStores(): Promise<void>;
  clearSelection(): void;
  getSelectedStore(): VenueVO | null;
}

// 重新导出相关实体类型，方便外部使用
export type { VenueVO } from '@/entities/store/venue'; 