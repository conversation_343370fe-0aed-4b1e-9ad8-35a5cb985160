import { computed, onMounted, ref, nextTick, watch } from 'vue';
import type { 
    IStoreSelectorViewModel, 
    IStoreSelectorState, 
    IStoreSelectorComputed, 
    IStoreSelectorActions,
    IStoreSelectorViewRefs
} from './viewmodel';
import type { StoreSelectorProps, StoreSelectorEmits } from './type';
import { useStoreSelectorPageState, type StoreSelectorPageStateAPI } from './state';
import { useStoreSelectorInteractor, StoreSelectorInteractor } from './interactor';
import { StoreSelectorConverter, type StoreListUpdateFormat } from './converter';
import type { VenueVO, VenuePageQueryParams } from '@/entities/store/venue';
import { useDataImportState } from '../../../stores/dataImportState';

/**
 * Presenter是核心协调者，实现IViewModel接口。
 * 它从State层派生ViewModel，处理Actions，并调用其他层。
 * 同时管理Props和Emits的处理逻辑。
 */
export class StoreSelectorPresenter implements IStoreSelectorViewModel {
    private pageState: StoreSelectorPageStateAPI = useStoreSelectorPageState();
    private interactor: StoreSelectorInteractor = useStoreSelectorInteractor();
    private dataImportState = useDataImportState();

    // Props和Emits处理
    private props: StoreSelectorProps | null = null;
    private emit: StoreSelectorEmits | null = null;

    // --- IStoreSelectorViewRefs 实现 (View层引用管理) ---
    public refs: IStoreSelectorViewRefs = {
        selectRef: ref(),
        scrollElement: ref<HTMLElement | null>(null),
    };

    // --- IStoreSelectorState 实现 (通过 computed 连接 State 层) ---
    public state: IStoreSelectorState = {
        selectedStoreId: computed({
            get: (): string => this.pageState.state.selectedStoreId,
            set: (value: string): void => { this.actions.selectStore(value); },
        }),
        stores: computed(() => [...this.pageState.state.stores]),
        isLoading: computed(() => this.pageState.state.isLoading),
        loadingMore: computed(() => this.pageState.state.loadingMore),
        error: computed(() => this.pageState.state.error),
        searchKeyword: computed({
            get: (): string => this.pageState.state.searchKeyword,
            set: (value: string): void => { this.actions.updateSearchKeyword(value); },
        }),
        currentPage: computed(() => this.pageState.state.currentPage),
        pageSize: computed(() => this.pageState.state.pageSize),
        total: computed(() => this.pageState.state.total),
    };

    // --- IStoreSelectorComputed 实现 (基于响应式 state 计算) ---
    public computed: IStoreSelectorComputed = {
        selectedStore: computed((): VenueVO | null => {
            const storeId = this.state.selectedStoreId.value;
            if (!storeId) return null;
            return this.state.stores.value.find(store => store.id === storeId) || null;
        }),
        hasStores: computed((): boolean => this.state.stores.value.length > 0),
        displayStores: computed((): VenueVO[] => {
            return this.state.stores.value.filter(store => store.auditStatus === 1);
        }),
        totalPages: computed((): number => {
            return Math.ceil(this.state.total.value / this.state.pageSize.value);
        }),
        hasMore: computed((): boolean => {
            return this.state.currentPage.value < this.computed.totalPages.value;
        }),
        showPagination: computed((): boolean => {
            return this.state.total.value > this.state.pageSize.value;
        }),
        formattedSelectedStoreAddress: computed((): string => {
            const store = this.computed.selectedStore.value;
            return store ? StoreSelectorConverter.formatStoreAddress(store) : '';
        }),
    };

    // --- IStoreSelectorActions 实现 (业务流程编排) ---
    public actions: IStoreSelectorActions = {
        loadStores: async (): Promise<void> => {
            console.log("Presenter: loadStores 开始执行, isLoading:", this.state.isLoading.value);
            if (this.state.isLoading.value) {
                console.log("Presenter: 已在加载中，跳过重复加载");
                return; // 防止重复加载
            }
            
            // 首先尝试使用缓存的门店数据
            const cachedStores = this.dataImportState.getCachedStores();
            console.log("Presenter: 检查缓存数据, 缓存数量:", cachedStores.length, "搜索关键词:", this.pageState.state.searchKeyword);
            if (cachedStores.length > 0 && !this.pageState.state.searchKeyword) {
                console.log("Presenter: 使用缓存的门店数据");
                this.pageState.setStores(cachedStores);
                this.pageState.setTotal(cachedStores.length);
                return;
            }
            
            console.log("Presenter: 开始从API加载门店数据");
            this.pageState.setLoading(true);
            this.pageState.clearError();
            
            try {
                // 准备查询参数: 通过 Converter
                const queryParams: VenuePageQueryParams = StoreSelectorConverter.toQueryParams(
                    this.pageState.state.searchKeyword,
                    this.pageState.state.currentPage,
                    this.pageState.state.pageSize
                );
                
                // 执行业务: 调用 Interactor
                const result = await this.interactor.loadStores(queryParams);
                
                // 准备数据: API数据 -> StateUpdateFormat (通过 Converter)
                const storeData: StoreListUpdateFormat = StoreSelectorConverter.toStateFormat(result.stores);
                
                // 更新状态: 调用 State 更新方法
                this.pageState.setStores(storeData.stores);
                this.pageState.setTotal(result.total);
                
                // 如果是无搜索条件的完整加载，缓存结果
                if (!this.pageState.state.searchKeyword && this.pageState.state.currentPage === 1) {
                    this.dataImportState.cacheStores(storeData.stores, 30); // 缓存30分钟
                }
                
                console.log("Presenter: Stores loaded successfully, count:", storeData.stores.length, "total:", result.total);
            } catch (error) {
                console.error("Presenter: Load stores failed.", error);
                const errorMessage = error instanceof Error ? error.message : '加载门店列表失败';
                this.pageState.setError(errorMessage);
            } finally {
                this.pageState.setLoading(false);
            }
        },

        updateSearchKeyword: (keyword: string): void => {
            this.pageState.setSearchKeyword(keyword);
        },

        search: async (): Promise<void> => {
            // 搜索时重置到第一页并清空现有数据
            this.pageState.setCurrentPage(1);
            this.pageState.setStores([]); // 清空现有数据
            await this.actions.loadStores();
        },

        changePage: async (page: number): Promise<void> => {
            if (page < 1 || page > this.computed.totalPages.value) return;
            this.pageState.setCurrentPage(page);
            await this.actions.loadMoreStores();
        },

        loadMoreStores: async (): Promise<void> => {
            if (this.state.isLoading.value || this.pageState.state.loadingMore) return; // 防止重复加载
            
            // 使用独立的loadingMore状态，避免影响主加载状态
            this.pageState.setLoadingMore(true);
            this.pageState.clearError();
            
            try {
                // 准备查询参数: 通过 Converter
                const queryParams: VenuePageQueryParams = StoreSelectorConverter.toQueryParams(
                    this.pageState.state.searchKeyword,
                    this.pageState.state.currentPage,
                    this.pageState.state.pageSize
                );
                
                // 执行业务: 调用 Interactor
                const result = await this.interactor.loadStores(queryParams);
                
                // 准备数据: API数据 -> StateUpdateFormat (通过 Converter)
                const storeData: StoreListUpdateFormat = StoreSelectorConverter.toStateFormat(result.stores);
                
                // 使用新的追加方法，避免替换整个数组
                this.pageState.appendStores(storeData.stores);
                this.pageState.setTotal(result.total);
                
                console.log("Presenter: More stores loaded successfully, new count:", storeData.stores.length, "total:", result.total);
            } catch (error) {
                console.error("Presenter: Load more stores failed.", error);
                const errorMessage = error instanceof Error ? error.message : '加载更多门店失败';
                this.pageState.setError(errorMessage);
            } finally {
                this.pageState.setLoadingMore(false);
            }
        },

        changePageSize: async (size: number): Promise<void> => {
            this.pageState.setPageSize(size);
            this.pageState.setCurrentPage(1); // 重置到第一页
            await this.actions.loadStores();
        },

        selectStore: (storeId: string): void => {
            console.log("StoreSelectorPresenter: selectStore 被调用，storeId:", storeId);
            
            // 验证选择的有效性
            if (storeId && !this.interactor.validateStoreSelection(storeId, this.state.stores.value)) {
                console.warn("StoreSelectorPresenter: Invalid store selection:", storeId);
                this.pageState.setError('选择的门店无效');
                return;
            }
            
            // 清除错误并更新选择
            this.pageState.clearError();
            this.pageState.setSelectedStoreId(storeId);
            console.log("StoreSelectorPresenter: 本地状态更新完成，storeId:", storeId);
            
            // 同步到全局Store
            const selectedStore = this.computed.selectedStore.value;
            console.log("StoreSelectorPresenter: 准备同步到全局状态，selectedStore:", selectedStore);
            this.dataImportState.setStoreInfo(storeId, selectedStore);
            
            console.log("StoreSelectorPresenter: 门店选择完成:", storeId, selectedStore?.name);
        },

        clearSelection: (): void => {
            this.pageState.clearSelection();
            this.pageState.clearError();
            console.log("Presenter: Selection cleared");
        },

        // 下拉框显示状态变化处理
        handleDropdownVisibleChange: (visible: boolean): void => {
            if (visible) {
                if (!this.computed.hasStores.value) {
                    // 首次打开时加载数据
                    this.actions.loadStores();
                }
                // 延迟初始化滚动监听器
                nextTick(() => {
                    this.actions.initScrollListener();
                });
            } else {
                // 销毁滚动监听器
                this.actions.destroyScrollListener();
            }
        },

        // 滚动处理函数（无限滚动）
        handleScroll: (event: Event): void => {
            const target = event.target as HTMLElement;
            if (target.scrollTop + target.clientHeight >= target.scrollHeight - 10) {
                // 接近底部时加载更多
                if (this.computed.hasMore.value && !this.state.isLoading.value && !this.state.loadingMore.value) {
                    this.actions.changePage(this.state.currentPage.value + 1);
                }
            }
        },

        // 远程搜索处理函数
        handleRemoteSearch: (query: string): void => {
            this.actions.updateSearchKeyword(query);
            this.actions.search();
        },

        // View层专用的actions
        initScrollListener: (): void => {
            setTimeout(() => {
                const dropdown = document.querySelector('.el-select-dropdown__wrap') as HTMLElement;
                if (dropdown && dropdown !== this.refs.scrollElement.value) {
                    // 先清理之前的监听器
                    this.actions.destroyScrollListener();
                    // 添加新的监听器
                    dropdown.addEventListener('scroll', this.actions.handleScroll);
                    this.refs.scrollElement.value = dropdown;
                    console.log('Presenter: Scroll listener initialized');
                }
            }, 100);
        },

        destroyScrollListener: (): void => {
            if (this.refs.scrollElement.value) {
                this.refs.scrollElement.value.removeEventListener('scroll', this.actions.handleScroll);
                this.refs.scrollElement.value = null;
                console.log('Presenter: Scroll listener destroyed');
            }
        },

        handlePropsChange: (newValue: string): void => {
            if (newValue !== this.state.selectedStoreId.value) {
                this.actions.selectStore(newValue || '');
            }
        },

        handleSelectionChange: (newStoreId: string): void => {
            // 当选择发生变化时，检查是否需要重新设置滚动监听
            nextTick(() => {
                if (this.refs.selectRef.value?.visible) {
                    this.actions.initScrollListener();
                }
            });
        },
    };

    // --- Props和Emits处理方法 ---
    public setupProps(props: StoreSelectorProps): void {
        this.props = props;
        // 监听外部prop变化
        watch(() => props.modelValue, (newValue) => {
            this.actions.handlePropsChange(newValue || '');
        }, { immediate: true });
    }

    public setupEmits(emit: StoreSelectorEmits): void {
        this.emit = emit;
        // 监听选择变化并向父组件发出事件
        watch(() => this.state.selectedStoreId.value, (newStoreId) => {
            emit('update:modelValue', newStoreId);
            emit('change', this.computed.selectedStore.value);
            // 处理选择变化的副作用
            this.actions.handleSelectionChange(newStoreId);
        });
    }

    // --- 生命周期钩子 ---
    constructor() {
        onMounted(() => {
            console.log("StoreSelectorPresenter: 组件挂载，开始加载门店列表");
            this.actions.loadStores();
        });
    }
}

// Presenter 工厂函数
export function useStoreSelectorPresenter(): IStoreSelectorViewModel {
    return new StoreSelectorPresenter();
} 