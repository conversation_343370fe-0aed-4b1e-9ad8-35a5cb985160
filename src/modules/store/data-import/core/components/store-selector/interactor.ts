import { queryVenueList } from '@/api/store/venue';
import type { VenueVO, VenuePageQueryParams, VenuePageVO } from '@/entities/store/venue';

/**
 * Interactor负责执行业务逻辑和数据访问 (如API调用)。
 * 它操作 Entity 或 DTOs。
 */
export class StoreSelectorInteractor {
    /**
     * 获取门店列表
     * @param params - 查询参数
     * @returns 包含门店列表和分页信息的结果
     */
    async loadStores(params: VenuePageQueryParams): Promise<{stores: VenueVO[], total: number}> {
        try {
            console.log('Interactor: Loading stores with params:', params);
            const response = await queryVenueList(params);
            console.log('Interactor: API response:', response);
            
            // 分页API返回的数据格式：{pageNum, pageSize, total, data}
            if (response && typeof response === 'object' && 'data' in response && Array.isArray(response.data)) {
                console.log('Interactor: Found page response with data array, count:', response.data.length);
                return {
                    stores: response.data,
                    total: response.total || 0
                };
            }
            else {
                console.error('Interactor: Invalid page response structure:', response);
                console.error('Response type:', typeof response);
                console.error('Response keys:', response ? Object.keys(response) : 'null');
                throw new Error('分页查询API返回的数据格式不正确，无法找到门店记录');
            }
        } catch (error) {
            console.error('Interactor: Failed to load stores', error);
            const errorMessage = error instanceof Error ? error.message : '获取门店列表失败，请稍后重试';
            throw new Error(errorMessage);
        }
    }

    /**
     * 验证门店是否存在且可用
     * @param storeId - 门店ID
     * @param stores - 当前门店列表
     * @returns 是否有效
     */
    validateStoreSelection(storeId: string, stores: VenueVO[]): boolean {
        if (!storeId) return false;
        return stores.some(store => store.id === storeId && store.auditStatus === 1);
    }
}

// 工厂函数获取 Interactor 实例
export function useStoreSelectorInteractor(): StoreSelectorInteractor {
    return new StoreSelectorInteractor();
} 