import type { VenueVO, VenuePageQueryParams } from '@/entities/store/venue';
import type { StoreSelectorPageState } from './state';

// API查询参数格式
export interface StoreQueryFormat {
    auditStatus: number; // 只查询已通过审核的门店
    pageNum: number; // 页码
    pageSize: number; // 每页数量
    venueName?: string; // 门店名称搜索关键词
}

// State更新所需的数据格式
export interface StoreListUpdateFormat {
    stores: VenueVO[];
}

/**
 * Converter负责不同层级/边界之间的数据结构转换。
 * 主要用于 State <-> Entity/DTO (外部) 之间。
 */
export class StoreSelectorConverter {
    /**
     * 将查询需求转换为API查询参数格式
     * @param searchKeyword - 搜索关键词
     * @param pageNum - 页码
     * @param pageSize - 每页数量
     * @returns API查询参数
     */
    static toQueryParams(searchKeyword?: string, pageNum: number = 1, pageSize: number = 10): VenuePageQueryParams {
        const params: VenuePageQueryParams = {
            auditStatus: 1, // 只查询已通过审核的门店
            pageNum,
            pageSize,
        };
        
        // 如果有搜索关键词，添加到参数中
        if (searchKeyword && searchKeyword.trim()) {
            params.name = searchKeyword.trim();
        }
        
        return params;
    }

    /**
     * 将API返回的门店数据转换为State层更新方法所需的格式
     * @param venues - 从API获取的门店列表
     * @returns 供State更新方法使用的格式化数据
     */
    static toStateFormat(venues: VenueVO[]): StoreListUpdateFormat {
        // 确保venues是数组
        if (!Array.isArray(venues)) {
            console.warn('Converter: venues is not an array, returning empty array');
            return { stores: [] };
        }

        // 过滤并排序门店列表
        const filteredStores = venues
            .filter(store => store.auditStatus === 1) // 确保只显示已审核通过的门店
            .sort((a, b) => a.name.localeCompare(b.name)); // 按名称排序

        return {
            stores: filteredStores,
        };
    }

    /**
     * 格式化门店显示信息
     * @param store - 门店信息
     * @returns 格式化的显示文本
     */
    static formatStoreDisplay(store: VenueVO): { label: string; subtitle: string } {
        return {
            label: store.name,
            subtitle: `${store.city}${store.district} | ID: ${store.id}`,
        };
    }

    /**
     * 格式化门店地址
     * @param store - 门店信息
     * @returns 完整地址字符串
     */
    static formatStoreAddress(store: VenueVO): string {
        return `${store.province}${store.city}${store.district}${store.address}`;
    }
} 