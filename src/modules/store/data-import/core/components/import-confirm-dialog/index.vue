<template>
  <el-dialog
    v-model="visible"
    title="确认导入"
    width="480px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    @close="handleCancel"
  >
    <div class="import-confirm-content">
      <!-- 警告图标和消息 -->
      <div class="flex items-start gap-4 mb-6">
        <el-icon class="text-orange-500 text-2xl mt-1" :size="24">
          <WarningFilled />
        </el-icon>
        <div class="flex-1">
          <h3 class="text-lg font-semibold text-gray-900 mb-2">请确认导入操作</h3>
          <div class="text-gray-600 space-y-2">
            <p>{{ confirmMessage || '此操作将执行数据导入，请确认无误后继续。' }}</p>
            <div v-if="importStats" class="bg-gray-50 rounded-lg p-3 text-sm">
              <div class="grid grid-cols-2 gap-2">
                <div>总计：<span class="font-semibold">{{ importStats.total }}</span> 项</div>
                <div>有效：<span class="font-semibold text-green-600">{{ importStats.valid }}</span> 项</div>
                <div v-if="importStats.invalid > 0">
                  无效：<span class="font-semibold text-red-600">{{ importStats.invalid }}</span> 项
                </div>
                <div v-if="importStats.reset && importStats.reset > 0">
                  重置：<span class="font-semibold text-orange-600">{{ importStats.reset }}</span> 项
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 门店名称确认输入 -->
      <div v-if="requireStoreConfirm" class="mb-6">
        <div class="mb-3">
          <label class="block text-sm font-medium text-gray-700 mb-2">
            请输入门店名称确认：<span class="text-red-500">*</span>
          </label>
          <div class="text-xs text-gray-500 mb-2">
            目标门店：<span class="font-semibold">{{ storeName }}</span>
          </div>
        </div>
        <el-input
          v-model="confirmInput"
          placeholder="请输入门店名称"
          :class="{ 'border-red-300': showValidationError }"
          @input="handleInputChange"
          @keyup.enter="handleConfirm"
        />
        <div v-if="showValidationError" class="text-red-500 text-xs mt-1">
          门店名称不匹配，请重新输入
        </div>
      </div>

      <!-- 自定义确认内容 -->
      <div v-if="$slots.content" class="mb-6">
        <slot name="content"></slot>
      </div>

      <!-- 操作按钮 -->
      <div class="flex justify-end gap-3">
        <el-button @click="handleCancel" :disabled="loading">
          取消
        </el-button>
        <el-button 
          type="primary" 
          @click="handleConfirm"
          :loading="loading"
          :disabled="!canConfirm"
        >
          {{ loading ? '导入中...' : '确认导入' }}
        </el-button>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { WarningFilled } from '@element-plus/icons-vue'

// 定义属性
interface Props {
  modelValue: boolean
  storeName?: string
  confirmMessage?: string
  requireStoreConfirm?: boolean
  loading?: boolean
  importStats?: {
    total: number
    valid: number
    invalid: number
    reset?: number
  }
}

// 定义事件
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false,
  storeName: '',
  confirmMessage: '',
  requireStoreConfirm: true,
  loading: false,
  importStats: undefined
})

const emit = defineEmits<Emits>()

// 响应式数据
const confirmInput = ref('')
const showValidationError = ref(false)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

const canConfirm = computed(() => {
  if (props.loading) return false
  
  if (props.requireStoreConfirm) {
    return confirmInput.value.trim() === props.storeName.trim()
  }
  
  return true
})

// 方法
const handleInputChange = (): void => {
  showValidationError.value = false
}

const handleConfirm = (): void => {
  if (props.requireStoreConfirm) {
    if (confirmInput.value.trim() !== props.storeName.trim()) {
      showValidationError.value = true
      return
    }
  }
  
  emit('confirm')
}

const handleCancel = (): void => {
  if (props.loading) return
  
  confirmInput.value = ''
  showValidationError.value = false
  emit('cancel')
}

// 监听弹窗显示状态，重置输入
watch(visible, (newVisible) => {
  if (newVisible) {
    confirmInput.value = ''
    showValidationError.value = false
  }
})
</script>

<style scoped>
.import-confirm-content {
  padding: 0;
}

.border-red-300 {
  border-color: #fca5a5 !important;
}
</style> 