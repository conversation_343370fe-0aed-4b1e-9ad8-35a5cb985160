// 数据导入插件注册系统

import { pluginManager } from './PluginManager'
import type { DataImportPlugin } from './types'

// 导入所有插件
import { inventoryImportPlugin } from '../plugins/inventory'
import { wineStorageImportPlugin } from '../plugins/wine-storage'

/**
 * 插件注册表
 */
export class PluginRegistry {
  private static instance: PluginRegistry
  private registered = false

  private constructor() {}

  static getInstance(): PluginRegistry {
    if (!PluginRegistry.instance) {
      PluginRegistry.instance = new PluginRegistry()
    }
    return PluginRegistry.instance
  }

  /**
   * 注册所有插件
   */
  registerAllPlugins(): void {
    if (this.registered) {
      console.warn('Plugins already registered')
      return
    }

    const plugins: DataImportPlugin[] = [
      inventoryImportPlugin,
      wineStorageImportPlugin
    ]

    console.log('开始注册数据导入插件...')
    
    plugins.forEach(plugin => {
      try {
        pluginManager.register(plugin)
        console.log(`✓ 插件 ${plugin.displayName} 注册成功`)
      } catch (error) {
        console.error(`✗ 插件 ${plugin.displayName} 注册失败:`, error)
      }
    })

    this.registered = true
    console.log(`数据导入插件注册完成，共注册 ${plugins.length} 个插件`)
  }

  /**
   * 获取所有已注册的插件
   */
  getRegisteredPlugins(): DataImportPlugin[] {
    return pluginManager.getAllPlugins()
  }

  /**
   * 获取所有启用的插件
   */
  getEnabledPlugins(): DataImportPlugin[] {
    return pluginManager.getEnabledPlugins()
  }

  /**
   * 根据ID获取插件
   */
  getPlugin(pluginId: string): DataImportPlugin | null {
    return pluginManager.getPlugin(pluginId)
  }

  /**
   * 启用插件
   */
  enablePlugin(pluginId: string): void {
    pluginManager.enable(pluginId)
  }

  /**
   * 禁用插件
   */
  disablePlugin(pluginId: string): void {
    pluginManager.disable(pluginId)
  }

  /**
   * 检查插件是否已注册
   */
  isRegistered(): boolean {
    return this.registered
  }

  /**
   * 重新注册所有插件
   */
  reregisterAllPlugins(): void {
    this.cleanup()
    this.registerAllPlugins()
  }

  /**
   * 清理所有插件
   */
  cleanup(): void {
    pluginManager.cleanup()
    this.registered = false
  }
}

// 导出全局实例
export const pluginRegistry = PluginRegistry.getInstance()

// 自动注册插件
pluginRegistry.registerAllPlugins() 