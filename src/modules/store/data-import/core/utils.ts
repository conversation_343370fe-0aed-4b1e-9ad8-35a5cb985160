// 数据导入插件工具函数

import type { TemplateData, FieldConfig } from './types'
import { generateCSVContent, downloadCSV } from '@/shared/utils/csvUtils'

/**
 * 根据模板数据生成CSV模板文件
 */
export function generateTemplateCSV(templateData: TemplateData, pluginName: string): void {
  const headers = templateData.headers
  const exampleData = templateData.exampleData || []
  
  const csvContent = generateCSVContent(headers, exampleData)
  const filename = `${pluginName}_导入模板_${new Date().toISOString().split('T')[0]}.csv`
  
  downloadCSV(csvContent, filename)
}

/**
 * 解析CSV文件
 */
export function parseCSVFile(file: File): Promise<{ headers: string[]; data: Record<string, any>[] }> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const text = e.target?.result as string
        const result = parseCSVText(text)
        resolve(result)
      } catch (error) {
        reject(error)
      }
    }
    
    reader.onerror = () => {
      reject(new Error('文件读取失败'))
    }
    
    reader.readAsText(file, 'utf-8')
  })
}

/**
 * 解析CSV文本内容
 */
export function parseCSVText(text: string): { headers: string[]; data: Record<string, any>[] } {
  const lines = text.split('\n').filter(line => line.trim())
  
  if (lines.length === 0) {
    throw new Error('CSV文件为空')
  }
  
  // 解析表头
  const headers = parseCSVLine(lines[0])
  
  // 解析数据行
  const data: Record<string, any>[] = []
  for (let i = 1; i < lines.length; i++) {
    const values = parseCSVLine(lines[i])
    if (values.length === headers.length) {
      const row: Record<string, any> = {}
      headers.forEach((header, index) => {
        row[header] = values[index]
      })
      data.push(row)
    }
  }
  
  return { headers, data }
}

/**
 * 解析CSV行
 */
function parseCSVLine(line: string): string[] {
  const result: string[] = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < line.length; i++) {
    const char = line[i]
    const nextChar = line[i + 1]
    
    if (char === '"') {
      if (inQuotes && nextChar === '"') {
        // 转义的引号
        current += '"'
        i++ // 跳过下一个引号
      } else {
        // 切换引号状态
        inQuotes = !inQuotes
      }
    } else if (char === ',' && !inQuotes) {
      // 字段分隔符
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }
  
  // 添加最后一个字段
  result.push(current.trim())
  
  return result
}

/**
 * 验证文件类型
 */
export function validateFileType(file: File, allowedTypes: string[] = ['.csv']): boolean {
  const fileName = file.name.toLowerCase()
  return allowedTypes.some(type => fileName.endsWith(type))
}

/**
 * 验证文件大小
 */
export function validateFileSize(file: File, maxSizeInMB: number = 10): boolean {
  const maxSizeInBytes = maxSizeInMB * 1024 * 1024
  return file.size <= maxSizeInBytes
}

/**
 * 格式化文件大小
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 根据字段配置生成模板数据
 */
export function generateTemplateFromFields(fieldConfigs: FieldConfig[]): TemplateData {
  const headers = fieldConfigs.map(field => field.label)
  const exampleData: Record<string, any>[] = [{}]
  
  // 生成示例数据
  fieldConfigs.forEach(field => {
    exampleData[0][field.label] = field.example || ''
  })
  
  // 生成验证规则描述
  const validationRules = fieldConfigs.map(field => ({
    field: field.label,
    type: field.required ? 'required' as const : 'custom' as const,
    message: field.description || `${field.label}字段`
  }))
  
  // 生成字段配置
  const fields = fieldConfigs.map(field => ({
    key: field.key,
    label: field.label,
    type: field.type,
    required: field.required,
    maxLength: field.maxLength,
    minLength: field.minLength,
    pattern: field.pattern,
    description: field.description,
    example: field.example
  }))
  
  // 生成填写说明
  const instructions = [
    '请按照以下要求填写数据：',
    ...fieldConfigs.map(field => {
      let instruction = `• ${field.label}: ${field.description || '请填写相关信息'}`
      if (field.required) {
        instruction += ' (必填)'
      }
      if (field.example) {
        instruction += ` 例如: ${field.example}`
      }
      return instruction
    }),
    '',
    '注意事项：',
    '• 请勿修改表头行',
    '• 必填字段不能为空',
    '• 日期格式请使用 YYYY-MM-DD',
    '• 数字字段请填写有效数值',
    '• 保存为CSV格式文件'
  ]
  
  return {
    headers,
    fields,
    validationRules,
    exampleData,
    description: '数据导入模板',
    instructions
  }
} 