// 数据导入插件基类

import type { 
  DataImportPlugin, 
  TemplateGenerator, 
  DataValidator, 
  DataPreviewer, 
  DataImporter,
  TemplateData,
  ValidationResult,
  ValidationError,
  ImportResult,
  FieldConfig
} from './types'

/**
 * 抽象基类 - 模板生成器
 */
export abstract class BaseTemplateGenerator implements TemplateGenerator {
  abstract getTemplateData(): TemplateData
  
  async generateTemplate(): Promise<Blob> {
    const templateData = this.getTemplateData()
    const csvContent = this.generateCSVContent(templateData)
    return new Blob([csvContent], { type: 'text/csv;charset=utf-8' })
  }

  validateTemplate(data: any[]): ValidationResult {
    const templateData = this.getTemplateData()
    const errors: ValidationError[] = []
    let validRows = 0

    // 验证表头
    if (data.length === 0) {
      errors.push({
        row: 0,
        field: 'headers',
        value: null,
        message: '文件为空或格式不正确',
        type: 'error'
      })
    }

    // 验证数据行
    data.forEach((row, index) => {
      const rowErrors = this.validateRow(row, index, templateData.validationRules)
      if (rowErrors.length === 0) {
        validRows++
      }
      errors.push(...rowErrors)
    })

    return {
      isValid: errors.filter(e => e.type === 'error').length === 0,
      errors: errors.filter(e => e.type === 'error'),
      warnings: errors.filter(e => e.type === 'warning'),
      validRows,
      totalRows: data.length
    }
  }

  private generateCSVContent(templateData: TemplateData): string {
    const lines: string[] = []
    
    // 添加表头
    lines.push(templateData.headers.join(','))
    
    // 添加示例数据
    templateData.sampleData.forEach(row => {
      const values = templateData.headers.map(header => {
        const value = row[header]
        return this.formatCSVValue(value)
      })
      lines.push(values.join(','))
    })
    
    return lines.join('\n')
  }

  private formatCSVValue(value: any): string {
    if (value === null || value === undefined) {
      return ''
    }
    
    const str = String(value)
    if (str.includes(',') || str.includes('"') || str.includes('\n')) {
      return `"${str.replace(/"/g, '""')}"`
    }
    
    return str
  }

  private validateRow(row: any, index: number, rules: any[]): ValidationError[] {
    const errors: ValidationError[] = []
    
    rules.forEach(rule => {
      const value = row[rule.field]
      const error = this.validateField(value, rule, index)
      if (error) {
        errors.push(error)
      }
    })
    
    return errors
  }

  private validateField(value: any, rule: any, rowIndex: number): ValidationError | null {
    // 基础验证逻辑
    if (rule.type === 'required' && (value === null || value === undefined || value === '')) {
      return {
        row: rowIndex + 1,
        field: rule.field,
        value,
        message: rule.message || `${rule.field} 是必填字段`,
        type: 'error'
      }
    }
    
    return null
  }
}

/**
 * 抽象基类 - 数据验证器
 */
export abstract class BaseDataValidator implements DataValidator {
  abstract getFieldConfig(): FieldConfig[]
  
  validate(data: any[]): ValidationResult {
    const errors: ValidationError[] = []
    let validRows = 0

    data.forEach((row, index) => {
      const rowErrors = this.validateRow(row, index)
      if (rowErrors.length === 0) {
        validRows++
      }
      errors.push(...rowErrors)
    })

    return {
      isValid: errors.filter(e => e.type === 'error').length === 0,
      errors: errors.filter(e => e.type === 'error'),
      warnings: errors.filter(e => e.type === 'warning'),
      validRows,
      totalRows: data.length
    }
  }

  validateRow(row: any, index: number): ValidationError[] {
    const errors: ValidationError[] = []
    const fieldConfigs = this.getFieldConfig()
    
    fieldConfigs.forEach(config => {
      const value = row[config.key]
      const error = this.validateField(value, config, index)
      if (error) {
        errors.push(error)
      }
    })
    
    return errors
  }

  private validateField(value: any, config: FieldConfig, rowIndex: number): ValidationError | null {
    // 必填验证
    if (config.required && (value === null || value === undefined || value === '')) {
      return {
        row: rowIndex + 1,
        field: config.key,
        value,
        message: `${config.label} 是必填字段`,
        type: 'error'
      }
    }

    // 如果值为空且不是必填，跳过其他验证
    if (value === null || value === undefined || value === '') {
      return null
    }

    // 类型验证
    switch (config.type) {
      case 'number':
        if (isNaN(Number(value))) {
          return {
            row: rowIndex + 1,
            field: config.key,
            value,
            message: `${config.label} 必须是数字`,
            type: 'error'
          }
        }
        break
      case 'email':
        if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return {
            row: rowIndex + 1,
            field: config.key,
            value,
            message: `${config.label} 格式不正确`,
            type: 'error'
          }
        }
        break
    }

    return null
  }
}

/**
 * 抽象基类 - 数据预览器
 */
export abstract class BaseDataPreviewer implements DataPreviewer {
  abstract getPreviewColumns(): Array<{ key: string; label: string; width?: number }>
  
  formatPreviewData(data: any[]): any[] {
    return data.slice(0, 100) // 默认只预览前100行
  }

  getPreviewSummary(data: any[]): Record<string, any> {
    return {
      totalRows: data.length,
      previewRows: Math.min(data.length, 100),
      hasMore: data.length > 100
    }
  }
}

/**
 * 抽象基类 - 数据导入器
 */
export abstract class BaseDataImporter implements DataImporter {
  private importProgress = 0
  private isImporting = false
  private shouldCancel = false

  abstract import(data: any[], storeId: string): Promise<ImportResult>

  getImportProgress(): number {
    return this.importProgress
  }

  cancelImport(): void {
    this.shouldCancel = true
  }

  protected updateProgress(progress: number): void {
    this.importProgress = Math.max(0, Math.min(100, progress))
  }

  protected checkCancellation(): boolean {
    return this.shouldCancel
  }

  protected resetImportState(): void {
    this.importProgress = 0
    this.isImporting = false
    this.shouldCancel = false
  }
}

/**
 * 抽象基类 - 数据导入插件
 */
export abstract class BaseDataImportPlugin implements DataImportPlugin {
  // 插件基础信息
  abstract id: string
  abstract name: string
  abstract displayName: string
  abstract description: string
  abstract version: string
  abstract icon: string
  abstract enabled: boolean
  
  // 可选信息
  author?: string
  order?: number

  // 插件组件
  abstract templateGenerator: TemplateGenerator
  abstract dataValidator: DataValidator
  abstract dataPreviewer: DataPreviewer
  abstract dataImporter: DataImporter

  // 生命周期钩子 - 默认实现
  async onInstall(): Promise<void> {
    console.log(`Plugin ${this.id} installed`)
  }

  async onUninstall(): Promise<void> {
    console.log(`Plugin ${this.id} uninstalled`)
  }

  async onEnable(): Promise<void> {
    console.log(`Plugin ${this.id} enabled`)
  }

  async onDisable(): Promise<void> {
    console.log(`Plugin ${this.id} disabled`)
  }

  async beforeImport(data: any[]): Promise<void> {
    console.log(`Plugin ${this.id} before import, ${data.length} rows`)
  }

  async afterImport(result: ImportResult): Promise<void> {
    console.log(`Plugin ${this.id} after import, ${result.successRows}/${result.totalRows} success`)
  }
} 