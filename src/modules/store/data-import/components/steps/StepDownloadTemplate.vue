<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">下载导入模板</h3>
      <div class="border border-gray-200 rounded-lg p-6 bg-gray-50">
        <div class="flex items-center gap-4 mb-4">
          <div class="flex items-center justify-center w-12 h-12 bg-blue-100 rounded-lg">
            <el-icon :size="24" class="text-blue-600">
              <Files />
            </el-icon>
          </div>
          <div>
            <h4 class="text-base font-medium text-gray-900">{{ plugin?.displayName }}导入模板</h4>
            <p class="text-sm text-gray-600">包含所有必需字段和示例数据</p>
          </div>
          <div class="ml-auto">
            <el-button type="primary" @click="downloadTemplate" :loading="downloading">
              <el-icon class="mr-2"><Download /></el-icon>
              下载模板
            </el-button>
          </div>
        </div>
        
        <!-- 模板说明 -->
        <div v-if="templateData" class="mt-4 p-4 bg-white rounded border">
          <h5 class="font-medium text-gray-900 mb-2">填写说明：</h5>
          <ul class="text-sm text-gray-600 space-y-1">
            <li v-for="instruction in templateData.instructions" :key="instruction">
              {{ instruction }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Files, Download } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
import { generateTemplateCSV } from '../../core/utils'
import type { ImportStepProps, ImportStepEmits, TemplateData } from '../../core/types'

interface Props extends ImportStepProps {}
interface Emits extends ImportStepEmits {}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const downloading = ref(false)
const templateData = ref<TemplateData | null>(null)

const downloadTemplate = () => {
  if (!props.plugin || !templateData.value) return
  
  downloading.value = true
  try {
    generateTemplateCSV(templateData.value, props.plugin.displayName)
    ElMessage.success('模板下载成功')
    emit('update:step-data', { templateDownloaded: true })
  } catch (error) {
    ElMessage.error('模板下载失败')
  } finally {
    downloading.value = false
  }
}

onMounted(() => {
  if (props.plugin) {
    templateData.value = props.plugin.templateGenerator.getTemplateData()
  }
})
</script> 