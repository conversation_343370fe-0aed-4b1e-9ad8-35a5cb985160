<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">上传CSV文件</h3>
      <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-blue-400 transition-colors"
           :class="{ 'border-blue-400 bg-blue-50': isDragOver }"
           @dragover.prevent="isDragOver = true"
           @dragleave.prevent="isDragOver = false"
           @drop.prevent="handleFileDrop">
        
        <div v-if="!uploadedFile">
          <el-icon :size="48" class="text-gray-400 mb-4">
            <Upload />
          </el-icon>
          <div class="mb-4">
            <p class="text-lg text-gray-600 mb-2">拖拽CSV文件到此处，或</p>
            <el-button type="primary" @click="triggerFileInput">
              选择文件
            </el-button>
          </div>
          <p class="text-sm text-gray-500">支持CSV格式，文件大小不超过10MB</p>
        </div>
        
        <div v-else class="space-y-4">
          <el-icon :size="48" class="text-green-500 mb-2">
            <Check />
          </el-icon>
          <div>
            <p class="text-lg font-medium text-gray-900">{{ uploadedFile.name }}</p>
            <p class="text-sm text-gray-500">{{ formatFileSize(uploadedFile.size) }}</p>
          </div>
          <el-button @click="removeFile" type="danger" plain>
            重新选择
          </el-button>
        </div>
        
        <input 
          ref="fileInput" 
          type="file" 
          accept=".csv" 
          style="display: none" 
          @change="handleFileSelect"
        />
      </div>
      
      <!-- 文件解析结果 -->
      <div v-if="uploadedFile && parsedData" class="mt-4 p-4 bg-green-50 border border-green-200 rounded">
        <div class="flex items-center gap-2 text-green-700">
          <el-icon><Check /></el-icon>
          <span class="font-medium">文件解析成功</span>
        </div>
        <p class="text-sm text-green-600 mt-1">
          共解析到 {{ parsedData.data.length }} 条数据记录
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { Upload, Check } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  parseCSVFile, 
  validateFileType, 
  validateFileSize, 
  formatFileSize 
} from '../../core/utils'
import type { ImportStepProps, ImportStepEmits } from '../../core/types'

interface Props extends ImportStepProps {}
interface Emits extends ImportStepEmits {}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const uploadedFile = ref<File | null>(props.stepData.uploadedFile || null)
const parsedData = ref<{ headers: string[]; data: Record<string, any>[] } | null>(props.stepData.parsedData || null)
const isDragOver = ref(false)
const fileInput = ref<HTMLInputElement>()

const triggerFileInput = () => {
  fileInput.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    processFile(file)
  }
  target.value = '' // 重置input
}

const handleFileDrop = (event: DragEvent) => {
  isDragOver.value = false
  const file = event.dataTransfer?.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = async (file: File) => {
  // 验证文件类型
  if (!validateFileType(file)) {
    ElMessage.error('请选择CSV格式的文件')
    return
  }
  
  // 验证文件大小
  if (!validateFileSize(file)) {
    ElMessage.error('文件大小不能超过10MB')
    return
  }
  
  uploadedFile.value = file
  
  try {
    // 解析CSV文件
    const result = await parseCSVFile(file)
    
    // 验证表头是否与模板一致
    if (!validateHeaders(result.headers)) {
      uploadedFile.value = null
      return
    }
    
    parsedData.value = result
    
    emit('update:step-data', {
      uploadedFile: file,
      parsedData: result
    })
    
    ElMessage.success('文件解析成功')
  } catch (error) {
    ElMessage.error('文件解析失败: ' + (error as Error).message)
    uploadedFile.value = null
    parsedData.value = null
  }
}

const validateHeaders = (uploadedHeaders: string[]): boolean => {
  if (!props.plugin) {
    ElMessage.error('插件数据未加载')
    return false
  }
  
  const templateData = props.plugin.templateGenerator.getTemplateData()
  const templateHeaders = templateData.headers
  
  // 检查表头数量是否一致
  if (uploadedHeaders.length !== templateHeaders.length) {
    ElMessage.error(`表头数量不匹配。期望 ${templateHeaders.length} 个字段，实际 ${uploadedHeaders.length} 个字段`)
    return false
  }
  
  // 检查表头名称是否一致
  const missingHeaders: string[] = []
  const extraHeaders: string[] = []
  
  templateHeaders.forEach(header => {
    if (!uploadedHeaders.includes(header)) {
      missingHeaders.push(header)
    }
  })
  
  uploadedHeaders.forEach(header => {
    if (!templateHeaders.includes(header)) {
      extraHeaders.push(header)
    }
  })
  
  if (missingHeaders.length > 0 || extraHeaders.length > 0) {
    let errorMessage = '表头不匹配！'
    if (missingHeaders.length > 0) {
      errorMessage += `\n缺少字段: ${missingHeaders.join(', ')}`
    }
    if (extraHeaders.length > 0) {
      errorMessage += `\n多余字段: ${extraHeaders.join(', ')}`
    }
    
    ElMessageBox.alert(
      `${errorMessage}\n\n正确的表头应该是：\n${templateHeaders.join('、')}`,
      '文件格式错误',
      {
        type: 'error',
        confirmButtonText: '重新上传'
      }
    )
    return false
  }
  
  return true
}

const removeFile = () => {
  uploadedFile.value = null
  parsedData.value = null
  emit('update:step-data', {
    uploadedFile: null,
    parsedData: null
  })
}

// 监听外部数据变化
watch(() => props.stepData.uploadedFile, (newValue) => {
  if (newValue !== uploadedFile.value) {
    uploadedFile.value = newValue
  }
})

watch(() => props.stepData.parsedData, (newValue) => {
  if (newValue !== parsedData.value) {
    parsedData.value = newValue
  }
})
</script> 