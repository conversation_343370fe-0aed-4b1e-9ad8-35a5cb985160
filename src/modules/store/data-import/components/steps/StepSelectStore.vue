<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">选择门店</h3>
      <p class="text-gray-600 mb-6">请选择要导入数据的门店</p>
      
      <StoreSelector 
        v-model="selectedStoreId"
        @change="handleStoreChange"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { StoreSelector } from '../../core/components'
import type { ImportStepProps, ImportStepEmits } from '../../core/types'

interface Props extends ImportStepProps {}
interface Emits extends ImportStepEmits {}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const selectedStoreId = ref(props.stepData.selectedStoreId || '')

const handleStoreChange = (store: any) => {
  emit('update:step-data', {
    selectedStoreId: selectedStoreId.value,
    selectedStore: store
  })
}

// 监听外部数据变化
watch(() => props.stepData.selectedStoreId, (newValue) => {
  if (newValue !== selectedStoreId.value) {
    selectedStoreId.value = newValue
  }
})
</script> 