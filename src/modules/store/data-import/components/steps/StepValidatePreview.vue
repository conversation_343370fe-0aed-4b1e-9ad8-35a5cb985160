<template>
  <div class="space-y-6">
    <div>
      <h3 class="text-lg font-semibold text-gray-900 mb-4">数据验证与预览</h3>
      
      <!-- 验证结果摘要 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div class="p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <div class="text-2xl font-bold text-blue-600">{{ totalRecords }}</div>
          <div class="text-sm text-blue-600">总记录数</div>
        </div>
        <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
          <div class="text-2xl font-bold text-green-600">{{ validRecords }}</div>
          <div class="text-sm text-green-600">有效记录</div>
        </div>
        <div class="p-4 bg-red-50 border border-red-200 rounded-lg">
          <div class="text-2xl font-bold text-red-600">{{ errorRecords }}</div>
          <div class="text-sm text-red-600">错误记录</div>
        </div>
      </div>
      
      <!-- 验证错误列表 -->
      <div v-if="validationErrors && validationErrors.length > 0" class="mb-6">
        <h4 class="font-medium text-gray-900 mb-3">验证错误</h4>
        <div class="border border-red-200 rounded-lg max-h-60 overflow-y-auto">
          <div v-for="error in validationErrors" :key="`${error.row}-${error.field}`"
               class="p-3 border-b border-red-100 last:border-b-0">
            <div class="flex items-start gap-3">
              <el-icon class="text-red-500 mt-0.5"><Warning /></el-icon>
              <div>
                <p class="text-sm font-medium text-red-700">
                  第 {{ error.row + 1 }} 行，字段 "{{ error.field }}"
                </p>
                <p class="text-sm text-red-600">{{ error.message }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 数据预览表格 -->
      <div>
        <h4 class="font-medium text-gray-900 mb-3">数据预览（前20条）</h4>
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <el-table 
            :data="previewData" 
            style="width: 100%" 
            max-height="400"
            stripe
          >
            <el-table-column 
              v-for="header in headers"
              :key="header"
              :prop="header"
              :label="header"
              min-width="120"
              show-overflow-tooltip
            />
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Warning } from '@element-plus/icons-vue'
import type { ImportStepProps, ImportStepEmits, ValidationError } from '../../core/types'

interface Props extends ImportStepProps {}
interface Emits extends ImportStepEmits {}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const parsedData = computed(() => props.stepData.parsedData)
const validationErrors = computed(() => props.stepData.validationErrors as ValidationError[] || [])

const totalRecords = computed(() => parsedData.value?.data.length || 0)
const errorRecords = computed(() => validationErrors.value.length || 0)
const validRecords = computed(() => totalRecords.value - errorRecords.value)

const headers = computed(() => parsedData.value?.headers || [])
const previewData = computed(() => {
  if (!parsedData.value) return []
  return parsedData.value.data.slice(0, 20)
})
</script> 