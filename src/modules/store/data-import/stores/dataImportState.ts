import { defineStore } from 'pinia'
import type { VenueVO } from '@/entities/store/venue'
import type { DataImportPlugin, ImportResult, ValidationResult } from '../core/types'

/**
 * 导入步骤枚举
 */
export enum ImportStep {
  SELECT_STORE = 'select-store',
  DOWNLOAD_TEMPLATE = 'download-template', 
  UPLOAD_FILE = 'upload-file',
  VALIDATE_PREVIEW = 'validate-preview',
  IMPORT_RESULT = 'import-result'
}

/**
 * 步骤配置接口
 */
export interface StepConfig {
  key: string
  title: string
  component: any
  canProceed?: (stepData: Record<string, any>) => boolean
}

/**
 * 导入会话状态（整合了原pageState的功能）
 */
export interface ImportSession {
  // 会话标识
  sessionId: string
  pluginId: string
  plugin: DataImportPlugin | null
  
  // 当前步骤（原pageState功能）
  currentStep: number
  stepKey: ImportStep
  
  // 步骤配置（原pageState功能）
  steps: StepConfig[]
  
  // 核心共享数据
  selectedStoreId: string
  selectedStore: VenueVO | null
  
  // 文件相关
  uploadedFile: File | null
  fileName: string
  fileSize: number
  parsedData: {
    headers: string[]
    data: Record<string, any>[]
  } | null
  
  // 验证相关
  validationResult: ValidationResult | null
  validationErrors: any[]
  validationWarnings: any[]
  
  // 导入相关
  importResult: ImportResult | null
  
  // 插件特定配置
  pluginConfig: Record<string, any>
  
  // 状态标识（原pageState功能）
  isProcessing: boolean
  canProceed: boolean
  error: string | null
  isInitialized: boolean
  
  // 时间戳
  createdAt: number
  updatedAt: number
}

/**
 * Store状态接口
 */
interface DataImportStoreState {
  // 当前活动会话（整合了原pageState的所有功能）
  currentSession: ImportSession | null
  
  // 历史会话（可选，用于恢复）
  historySessions: ImportSession[]
  
  // 全局状态
  isInitialized: boolean
  
  // 缓存的门店列表（跨插件共享）
  cachedStores: VenueVO[]
  cacheExpiry: number
}

/**
 * 数据导入状态管理State（整合了原pageState功能）
 * 
 * 职责：
 * 1. 管理import-wizard页面的完整状态
 * 2. 管理跨步骤的共享数据（门店信息、文件数据等）
 * 3. 提供会话级别的状态管理
 * 4. 缓存常用数据（门店列表等）
 * 5. 支持导入会话的恢复和持久化
 */
export const useDataImportState = defineStore('dataImportState', {
  state: (): DataImportStoreState => ({
    currentSession: null,
    historySessions: [],
    isInitialized: false,
    cachedStores: [],
    cacheExpiry: 0
  }),

  getters: {
    /**
     * 是否有活动会话
     */
    hasActiveSession: (state): boolean => !!state.currentSession,

    /**
     * 获取当前会话（如果存在）
     */
    getCurrentSession: (state) => state.currentSession,

    /**
     * 当前步骤信息
     */
    currentStepInfo: (state) => {
      if (!state.currentSession) return null
      return {
        step: state.currentSession.currentStep,
        stepKey: state.currentSession.stepKey,
        canProceed: state.currentSession.canProceed,
        isProcessing: state.currentSession.isProcessing
      }
    },

    /**
     * 页面基础状态（原pageState的state部分）
     */
    pageState: (state) => {
      if (!state.currentSession) return null
      return {
        currentStep: state.currentSession.currentStep,
        isProcessing: state.currentSession.isProcessing,
        currentPlugin: state.currentSession.plugin,
        pluginId: state.currentSession.pluginId,
        steps: state.currentSession.steps,
        stepData: {
          selectedStoreId: state.currentSession.selectedStoreId,
          selectedStore: state.currentSession.selectedStore,
          selectedStoreName: state.currentSession.selectedStore?.name || '',
          uploadedFile: state.currentSession.uploadedFile,
          parsedData: state.currentSession.parsedData,
          validationResult: state.currentSession.validationResult,
          validationErrors: state.currentSession.validationErrors,
          validationWarnings: state.currentSession.validationWarnings,
          importResult: state.currentSession.importResult,
          ...state.currentSession.pluginConfig
        },
        error: state.currentSession.error,
        sessionId: state.currentSession.sessionId,
        isInitialized: state.currentSession.isInitialized
      }
    },

    /**
     * 选中的门店信息
     */
    selectedStoreInfo: (state) => {
      if (!state.currentSession) return null
      return {
        storeId: state.currentSession.selectedStoreId,
        store: state.currentSession.selectedStore
      }
    },

    /**
     * 文件信息
     */
    fileInfo: (state) => {
      if (!state.currentSession) return null
      return {
        file: state.currentSession.uploadedFile,
        fileName: state.currentSession.fileName,
        fileSize: state.currentSession.fileSize,
        parsedData: state.currentSession.parsedData
      }
    },

    /**
     * 验证信息
     */
    validationInfo: (state) => {
      if (!state.currentSession) return null
      return {
        result: state.currentSession.validationResult,
        errors: state.currentSession.validationErrors,
        warnings: state.currentSession.validationWarnings,
        hasErrors: state.currentSession.validationErrors.length > 0,
        hasWarnings: state.currentSession.validationWarnings.length > 0
      }
    },

    /**
     * 导入结果信息
     */
    importInfo: (state) => {
      if (!state.currentSession) return null
      return state.currentSession.importResult
    },

    /**
     * 门店缓存是否有效
     */
    isStoreCacheValid: (state): boolean => {
      return state.cachedStores.length > 0 && Date.now() < state.cacheExpiry
    }
  },

  actions: {
    /**
     * 初始化Store
     */
    initialize() {
      if (this.isInitialized) return
      
      console.log('DataImportState: 初始化数据导入状态管理')
      this.isInitialized = true
    },

    /**
     * 创建新的导入会话（整合了原pageState的初始化）
     */
    createSession(pluginId: string, plugin: DataImportPlugin): string {
      const sessionId = `import_${pluginId}_${Date.now()}`
      
      const newSession: ImportSession = {
        sessionId,
        pluginId,
        plugin,
        currentStep: 0,
        stepKey: ImportStep.SELECT_STORE,
        steps: [], // 将在设置插件后初始化
        selectedStoreId: '',
        selectedStore: null,
        uploadedFile: null,
        fileName: '',
        fileSize: 0,
        parsedData: null,
        validationResult: null,
        validationErrors: [],
        validationWarnings: [],
        importResult: null,
        pluginConfig: {},
        isProcessing: false,
        canProceed: false,
        error: null,
        isInitialized: false,
        createdAt: Date.now(),
        updatedAt: Date.now()
      }

      // 如果有当前会话，保存到历史
      if (this.$state.currentSession) {
        this.historySessions.push({ ...this.$state.currentSession })
        // 保持历史会话数量在合理范围内
        if (this.historySessions.length > 10) {
          this.historySessions = this.historySessions.slice(-10)
        }
      }

      this.$state.currentSession = newSession
      
      // 初始化canProceed状态
      this.updateCanProceedStatus()
      
      console.log(`DataImportState: 创建新会话 ${sessionId} for plugin ${pluginId}`)
      
      return sessionId
    },

    // --- 原pageState的状态更新方法 ---

    /**
     * 设置当前步骤
     */
    setCurrentStep(step: number): void {
      if (!this.$state.currentSession) return
      if (step >= 0 && step < this.$state.currentSession.steps.length) {
        this.$state.currentSession.currentStep = step
        this.$state.currentSession.updatedAt = Date.now()
      }
    },

    /**
     * 设置处理状态
     */
    setProcessing(processing: boolean): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.isProcessing = processing
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 设置当前插件
     */
    setCurrentPlugin(plugin: DataImportPlugin | null): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.plugin = plugin
      if (plugin) {
        this.$state.currentSession.pluginId = plugin.id
      }
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 设置插件ID
     */
    setPluginId(pluginId: string): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.pluginId = pluginId
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 设置步骤配置
     */
    setSteps(steps: StepConfig[]): void {
      if (!this.$state.currentSession) {
        console.log('DataImportState: setSteps 失败 - 没有当前会话')
        return
      }
      console.log(`DataImportState: 设置步骤，数量: ${steps.length}`, steps.map(s => s.key))
      this.$state.currentSession.steps = [...steps]
      this.$state.currentSession.updatedAt = Date.now()
      console.log(`DataImportState: 步骤设置完成，当前会话步骤数量: ${this.$state.currentSession.steps.length}`)
    },

    /**
     * 设置错误信息
     */
    setError(error: string | null): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.error = error
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 清除错误信息
     */
    clearError(): void {
      this.setError(null)
    },

    /**
     * 设置会话ID
     */
    setSessionId(sessionId: string): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.sessionId = sessionId
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 设置初始化状态
     */
    setInitialized(initialized: boolean): void {
      if (!this.$state.currentSession) return
      this.$state.currentSession.isInitialized = initialized
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 下一步
     */
    nextStep(): boolean {
      if (!this.$state.currentSession) return false
      if (this.$state.currentSession.currentStep < this.$state.currentSession.steps.length - 1) {
        this.$state.currentSession.currentStep++
        this.$state.currentSession.updatedAt = Date.now()
        return true
      }
      return false
    },

    /**
     * 上一步
     */
    prevStep(): boolean {
      if (!this.$state.currentSession) return false
      if (this.$state.currentSession.currentStep > 0) {
        this.$state.currentSession.currentStep--
        this.$state.currentSession.updatedAt = Date.now()
        return true
      }
      return false
    },

    /**
     * 跳转到指定步骤
     */
    jumpToStep(step: number): boolean {
      if (!this.$state.currentSession) return false
      if (step >= 0 && step < this.$state.currentSession.steps.length) {
        this.$state.currentSession.currentStep = step
        this.$state.currentSession.updatedAt = Date.now()
        return true
      }
      return false
    },

    /**
     * 更新当前步骤（包含stepKey更新）
     */
    updateCurrentStep(step: number, stepKey: ImportStep) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.currentStep = step
      this.$state.currentSession.stepKey = stepKey
      this.$state.currentSession.updatedAt = Date.now()
      
      // 切换步骤时重新计算canProceed状态
      this.updateCanProceedStatus()
      
      console.log(`DataImportState: 更新步骤到 ${step} (${stepKey})`)
    },

    /**
     * 设置门店信息
     */
    setStoreInfo(storeId: string, store: VenueVO | null) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.selectedStoreId = storeId
      this.$state.currentSession.selectedStore = store
      this.$state.currentSession.updatedAt = Date.now()
      
      console.log(`DataImportState: 设置门店信息 ${storeId}`, store?.name)
    },

    /**
     * 设置文件信息
     */
    setFileInfo(file: File | null, parsedData: any = null) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.uploadedFile = file
      this.$state.currentSession.fileName = file?.name || ''
      this.$state.currentSession.fileSize = file?.size || 0
      this.$state.currentSession.parsedData = parsedData
      this.$state.currentSession.updatedAt = Date.now()
      
      console.log(`DataImportState: 设置文件信息`, file?.name, `${file?.size} bytes`)
    },

    /**
     * 设置验证结果
     */
    setValidationResult(result: ValidationResult | null, errors: any[] = [], warnings: any[] = []) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.validationResult = result
      this.$state.currentSession.validationErrors = errors
      this.$state.currentSession.validationWarnings = warnings
      this.$state.currentSession.updatedAt = Date.now()
      
      console.log(`DataImportState: 设置验证结果`, `${errors.length} errors, ${warnings.length} warnings`)
    },

    /**
     * 设置导入结果
     */
    setImportResult(result: ImportResult | null) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.importResult = result
      this.$state.currentSession.updatedAt = Date.now()
      
      console.log(`DataImportState: 设置导入结果`, result?.success ? '成功' : '失败')
    },

    /**
     * 设置插件配置
     */
    setPluginConfig(config: Record<string, any>) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.pluginConfig = { ...this.$state.currentSession.pluginConfig, ...config }
      this.$state.currentSession.updatedAt = Date.now()
      
      console.log(`DataImportState: 更新插件配置`, config)
    },

    /**
     * 设置是否可以继续
     */
    setCanProceed(canProceed: boolean) {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.canProceed = canProceed
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 更新步骤数据（通用方法）
     */
    updateStepData(data: Record<string, any>) {
      if (!this.$state.currentSession) return
      
      // 根据数据类型智能更新
      if (data.selectedStoreId !== undefined || data.selectedStore !== undefined) {
        this.setStoreInfo(data.selectedStoreId || '', data.selectedStore || null)
      }
      
      if (data.uploadedFile !== undefined || data.parsedData !== undefined) {
        this.setFileInfo(data.uploadedFile || null, data.parsedData || null)
      }
      
      if (data.validationResult !== undefined || data.validationErrors !== undefined) {
        this.setValidationResult(
          data.validationResult || null,
          data.validationErrors || [],
          data.validationWarnings || []
        )
      }
      
      if (data.importResult !== undefined) {
        this.setImportResult(data.importResult || null)
      }
      
      // 更新插件特定配置
      const pluginConfigKeys = Object.keys(data).filter(key => 
        !['selectedStoreId', 'selectedStore', 'uploadedFile', 'parsedData', 
          'validationResult', 'validationErrors', 'validationWarnings', 'importResult'].includes(key)
      )
      
      if (pluginConfigKeys.length > 0) {
        const pluginConfig = pluginConfigKeys.reduce((config, key) => {
          config[key] = data[key]
          return config
        }, {} as Record<string, any>)
        
        this.setPluginConfig(pluginConfig)
      }
      
      // 自动更新canProceed状态
      this.updateCanProceedStatus()
    },

    /**
     * 根据当前步骤和数据自动更新canProceed状态
     */
    updateCanProceedStatus() {
      if (!this.$state.currentSession) return
      
      let canProceed = false
      
      // 根据当前步骤判断是否可以继续
      switch (this.$state.currentSession.stepKey) {
        case ImportStep.SELECT_STORE:
          canProceed = !!this.$state.currentSession.selectedStoreId
          break
        case ImportStep.DOWNLOAD_TEMPLATE:
          canProceed = true
          break
        case ImportStep.UPLOAD_FILE:
          canProceed = !!this.$state.currentSession.uploadedFile && !!this.$state.currentSession.parsedData
          break
        case ImportStep.VALIDATE_PREVIEW:
          canProceed = !this.$state.currentSession.validationErrors?.length
          break
        case ImportStep.IMPORT_RESULT:
          canProceed = true
          break
        default:
          canProceed = true
      }
      
      this.setCanProceed(canProceed)
      console.log(`DataImportState: 更新canProceed状态 - ${this.$state.currentSession.stepKey}: ${canProceed}`)
    },

    /**
     * 获取步骤数据（用于向下兼容）
     */
    getStepData(): Record<string, any> {
      if (!this.$state.currentSession) return {}
      
      return {
        selectedStoreId: this.$state.currentSession.selectedStoreId,
        selectedStore: this.$state.currentSession.selectedStore,
        selectedStoreName: this.$state.currentSession.selectedStore?.name || '',
        uploadedFile: this.$state.currentSession.uploadedFile,
        parsedData: this.$state.currentSession.parsedData,
        validationResult: this.$state.currentSession.validationResult,
        validationErrors: this.$state.currentSession.validationErrors,
        validationWarnings: this.$state.currentSession.validationWarnings,
        importResult: this.$state.currentSession.importResult,
        ...this.$state.currentSession.pluginConfig
      }
    },

    /**
     * 重置状态（原pageState的reset方法）
     */
    reset(): void {
      if (!this.$state.currentSession) return
      
      this.$state.currentSession.currentStep = 0
      this.$state.currentSession.isProcessing = false
      this.$state.currentSession.plugin = null
      this.$state.currentSession.pluginId = ''
      this.$state.currentSession.steps = []
      this.$state.currentSession.selectedStoreId = ''
      this.$state.currentSession.selectedStore = null
      this.$state.currentSession.uploadedFile = null
      this.$state.currentSession.fileName = ''
      this.$state.currentSession.fileSize = 0
      this.$state.currentSession.parsedData = null
      this.$state.currentSession.validationResult = null
      this.$state.currentSession.validationErrors = []
      this.$state.currentSession.validationWarnings = []
      this.$state.currentSession.importResult = null
      this.$state.currentSession.pluginConfig = {}
      this.$state.currentSession.error = null
      this.$state.currentSession.sessionId = ''
      this.$state.currentSession.isInitialized = false
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 从Store恢复状态（原pageState的restoreFromStore方法）
     */
    restoreFromStore(storeData: {
      currentStep?: number
      stepData?: Record<string, any>
      sessionId?: string
      pluginId?: string
    }): void {
      if (!this.$state.currentSession) return
      
      if (storeData.currentStep !== undefined) {
        this.$state.currentSession.currentStep = storeData.currentStep
      }
      if (storeData.stepData) {
        // 更新各种数据
        this.updateStepData(storeData.stepData)
      }
      if (storeData.sessionId) {
        this.$state.currentSession.sessionId = storeData.sessionId
      }
      if (storeData.pluginId) {
        this.$state.currentSession.pluginId = storeData.pluginId
      }
      this.$state.currentSession.updatedAt = Date.now()
    },

    /**
     * 缓存门店列表
     */
    cacheStores(stores: VenueVO[], expireMinutes: number = 30) {
      this.cachedStores = stores
      this.cacheExpiry = Date.now() + (expireMinutes * 60 * 1000)
      
      console.log(`DataImportState: 缓存 ${stores.length} 个门店，${expireMinutes} 分钟后过期`)
    },

    /**
     * 获取缓存的门店列表
     */
         getCachedStores(): VenueVO[] {
       if (this.cachedStores.length > 0 && Date.now() < this.cacheExpiry) {
         console.log(`DataImportState: 使用缓存的门店列表 (${this.cachedStores.length} 个)`)
         return this.cachedStores
       }
       
       console.log(`DataImportState: 门店缓存已过期或为空`)
       return []
     },

    /**
     * 清除门店缓存
     */
    clearStoreCache() {
      this.cachedStores = []
      this.cacheExpiry = 0
      console.log(`DataImportState: 清除门店缓存`)
    },

    /**
     * 重置当前会话
     */
    resetCurrentSession() {
      if (this.$state.currentSession) {
        console.log(`DataImportState: 重置会话 ${this.$state.currentSession.sessionId}`)
      }
      this.$state.currentSession = null
    },

    /**
     * 恢复会话（从历史中）
     */
    restoreSession(sessionId: string): boolean {
      const session = this.historySessions.find(s => s.sessionId === sessionId)
      if (session) {
        this.$state.currentSession = { ...session }
        console.log(`DataImportState: 恢复会话 ${sessionId}`)
        return true
      }
      
      console.warn(`DataImportState: 未找到会话 ${sessionId}`)
      return false
    },

    /**
     * 清理历史会话
     */
    clearHistorySessions() {
      this.historySessions = []
      console.log(`DataImportState: 清理历史会话`)
    },

    /**
     * 重置整个Store
     */
    resetStore() {
      this.$state.currentSession = null
      this.historySessions = []
      this.cachedStores = []
      this.cacheExpiry = 0
      console.log(`DataImportState: 重置所有状态`)
    }
  },

  // 明确禁用持久化，因为包含不可序列化的对象（File、Vue组件等）
  persist: false
}) 