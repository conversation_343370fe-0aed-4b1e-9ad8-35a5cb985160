import { reactive, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { IAuditListViewModel } from './viewmodel'
import { AuditListConverter } from './converter'
import { AuditListInteractor } from './interactor'

export class AuditListPresenter implements IAuditListViewModel {
  // 状态管理
  public state = reactive(AuditListConverter.createInitialState())

  // 计算属性
  public computes = {
    // 是否有选中项
    hasSelected: computed(() => this.state.selectedIds.length > 0),
    
    // 过滤后的列表
    filteredList: computed(() => {
      return AuditListConverter.filterList(this.state.list, this.state.filters)
    }),
    
    // 是否为空列表
    isEmpty: computed(() => this.computes.filteredList.value.length === 0),
    
    // 汇总数据
    summaryData: computed(() => {
      const list = this.computes.filteredList.value
      return {
        total: list.length,
        approved: list.filter(item => item.status === 'approved').length,
        pending: list.filter(item => item.status === 'pending').length,
        rejected: list.filter(item => item.status === 'rejected').length
      }
    }),
    
    // 获取状态类型
    getStatusType: (status: string) => AuditListConverter.getStatusType(status),
    
    // 获取状态文本
    getStatusText: (status: string) => AuditListConverter.getStatusText(status),
    
    // 格式化日期
    formatDate: (date: string) => AuditListConverter.formatDate(date)
  }

  // 依赖注入
  private router = useRouter()
  private interactor = new AuditListInteractor()

  constructor() {
    // 初始化加载数据
    this.loadInitialData()
  }

  // 动作处理
  public actions = {
    // 筛选相关 - 现在都是本地过滤，不需要重新加载数据
    handleDateRangeChange: () => {
      // 本地过滤，不需要重新加载
    },

    handleStatusChange: () => {
      // 本地过滤，不需要重新加载
    },

    handleSearchInput: () => {
      // 本地过滤，不需要重新加载
    },

    handleSearch: () => {
      // 本地过滤，不需要重新加载
    },

    handleReset: () => {
      AuditListConverter.resetFilters(this.state)
      // 本地过滤，不需要重新加载
    },

    // 表格选择
    handleSelectionChange: (selection: any[]) => {
      this.state.selectedIds = selection.map(item => item.id)
    },

    handleAudit: async (id: string, action: 'approve' | 'reject') => {
      try {
        await ElMessageBox.confirm(
          `确认${action === 'approve' ? '通过' : '拒绝'}该门店的审核申请？`,
          '确认操作',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: action === 'approve' ? 'success' : 'warning'
          }
        )

        await this.interactor.auditVenue({
          venue_id: id,
          status: action === 'approve' ? 1 : 2,
          remark: action === 'approve' ? '审核通过' : '审核拒绝'
        })

        ElMessage.success('操作成功')
        this.loadAuditList()
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('操作失败')
        }
      }
    },

    // 批量操作
    handleBatchAudit: async () => {
      await this.loadTemplates()
      AuditListConverter.updateBatchDialogState(this.state, true)
    },

    handleBatchConfirm: async () => {
      try {
        const { action, remark } = this.state.batchDialog.form
        await this.interactor.batchAudit({
          venueIds: this.state.selectedIds,
          status: action === 'approve' ? 1 : 2,
          remark
        })

        ElMessage.success('批量操作成功')
        AuditListConverter.updateBatchDialogState(this.state, false)
        AuditListConverter.updateSelectedState(this.state, [])
        this.loadAuditList()
      } catch (error) {
        ElMessage.error('批量操作失败')
      }
    }
  }

  /**
   * 初始化加载数据
   */
  private async loadInitialData() {
    await Promise.all([
      this.loadAuditList(),
      this.loadTemplates()
    ])
  }

  /**
   * 加载审核列表
   */
  private async loadAuditList() {
    try {
      AuditListConverter.updateLoadingState(this.state, true)
      const queryParams = AuditListConverter.toQueryParams(this.state)
      const response = await this.interactor.getAuditList(queryParams)
      AuditListConverter.toListState(this.state, response)
    } catch (error) {
      ElMessage.error('加载列表失败')
    } finally {
      AuditListConverter.updateLoadingState(this.state, false)
    }
  }

  /**
   * 加载审核模板
   */
  private async loadTemplates() {
    try {
      const templates = await this.interactor.getAuditTemplates()
      AuditListConverter.toTemplatesState(this.state, templates)
    } catch (error) {
      ElMessage.error('加载模板失败')
    }
  }
}

export function useAuditListPresenter(): IAuditListViewModel {
  return new AuditListPresenter()
} 