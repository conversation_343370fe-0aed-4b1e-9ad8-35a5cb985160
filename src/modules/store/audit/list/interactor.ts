import { auditVenue } from '@/api/store/license'
import { queryVenueList } from '@/api/store/venue'
import type { AuditVenueReq } from '@/entities/store/license'
import type { VenuePageQueryParams, VenuePageVO } from '@/entities/store/venue'

// 审核列表查询参数
export interface AuditListQuery {
  startTime?: string
  endTime?: string
  status?: number
  keyword?: string
  pageNum: number
  pageSize: number
}

// 审核列表响应
export type AuditListResponse = VenuePageVO

// 备注模板
export interface AuditTemplate {
  id: string
  name: string
  content: string
}

export class AuditListInteractor {
  /**
   * 获取审核列表
   */
  async getAuditList(query: AuditListQuery): Promise<AuditListResponse> {
    try {
      const params: VenuePageQueryParams = {
        pageNum: query.pageNum,
        pageSize: query.pageSize,
        auditStatus: query.status,
        name: query.keyword // 支持按门店名称搜索
      }
      return await queryVenueList(params)
    } catch (error) {
      console.error('获取审核列表失败:', error)
      throw new Error('获取审核列表失败')
    }
  }

  /**
   * 执行审核操作
   */
  async auditVenue(params: AuditVenueReq): Promise<void> {
    try {
      await auditVenue(params)
    } catch (error) {
      console.error('审核操作失败:', error)
      throw new Error('审核操作失败')
    }
  }

  /**
   * 批量审核操作
   */
  async batchAudit(params: {
    venueIds: string[]
    status: number
    remark: string
  }): Promise<void> {
    try {
      const { venueIds, status, remark } = params
      // 串行处理每个门店的审核
      for (const venueId of venueIds) {
        await this.auditVenue({
          venue_id: venueId,
          status,
          remark
        })
      }
    } catch (error) {
      console.error('批量审核操作失败:', error)
      throw new Error('批量审核操作失败')
    }
  }

  /**
   * 获取审核备注模板
   */
  async getAuditTemplates(): Promise<AuditTemplate[]> {
    // TODO: 替换为实际的API调用
    return [
      {
        id: '1',
        name: '通过模板',
        content: '信息完整，审核通过'
      },
      {
        id: '2',
        name: '驳回模板',
        content: '信息不完整，请补充后重新提交'
      }
    ]
  }
}

export function useAuditListInteractor() {
  return new AuditListInteractor()
} 