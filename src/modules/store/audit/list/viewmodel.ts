import type { ComputedRef } from 'vue'

// 审核项数据结构
export interface AuditItem {
  id: string
  venue_id: string
  storeName: string
  applyType: 'new' | 'renew'
  status: string
  appliedAt: string
  location: {
    province: string
    city: string
    district: string
  }
  contactPhone: string
}

// 备注模板数据结构
export interface Template {
  id: string
  name: string
  content: string
}

// 汇总数据结构
export interface SummaryData {
  total: number        // 总门店数
  approved: number     // 审核通过
  pending: number      // 待审核
  rejected: number     // 审核失败
}

// UI状态接口
export interface IAuditListState {
  // 列表数据
  list: AuditItem[]
  loading: boolean
  selectedIds: string[]
  
  // 筛选条件
  filters: {
    dateRange: [Date, Date] | null
    status: string
    searchKey: string
  }
  
  // 分页信息（已移除，保留接口兼容性）
  pagination: {
    current: number
    pageSize: number
    total: number
  }

  // 备注模板
  templates: Template[]

  // 批量审核弹窗
  batchDialog: {
    visible: boolean
    form: {
      action: 'approve' | 'reject'
      templateId: string
      remark: string
    }
  }
}

// UI计算属性接口
export interface IAuditListComputed {
  // 是否有选中项
  hasSelected: ComputedRef<boolean>
  // 过滤后的列表
  filteredList: ComputedRef<AuditItem[]>
  // 是否为空列表
  isEmpty: ComputedRef<boolean>
  // 汇总数据
  summaryData: ComputedRef<SummaryData>
  // 获取状态类型
  getStatusType: (status: string) => string
  // 获取状态文本
  getStatusText: (status: string) => string
  // 格式化日期
  formatDate: (date: string) => string
}

// UI动作接口
export interface IAuditListActions {
  // 筛选相关
  handleDateRangeChange: () => void
  handleStatusChange: () => void
  handleSearchInput: () => void
  handleSearch: () => void
  handleReset: () => void
  
  // 表格选择
  handleSelectionChange: (selection: AuditItem[]) => void
  
  // 审核操作
  handleAudit: (id: string, action: 'approve' | 'reject') => void
  
  // 批量操作
  handleBatchAudit: () => void
  handleBatchConfirm: () => Promise<void>
}

// 组合接口
export interface IAuditListViewModel {
  state: IAuditListState
  computes: IAuditListComputed
  actions: IAuditListActions
} 