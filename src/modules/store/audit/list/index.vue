<template>
  <div class="audit-list-container h-full flex flex-col">
    <!-- 页面标题区 -->
    <div
      class="mb-4 sm:mb-6 flex flex-col space-y-3 sm:space-y-0 sm:flex-row justify-between sm:items-center flex-shrink-0">
      <div class="flex flex-row items-center h-full">
        <h2 class="text-xl sm:text-2xl font-bold">门店管理</h2>
        <div class="flex flex-row items-center h-full ml-[12px]">
          <el-form :inline="true" :model="vm.state.filters"
            class="flex flex-col sm:flex-row flex-wrap gap-3 sm:gap-4 items-center h-full ml-[12px]">
            <!-- 关键词搜索 -->
            <el-form-item label="关键词" class="w-full sm:w-auto">
              <el-input v-model="vm.state.filters.searchKey" placeholder="门店名称/联系人/手机号"
                @input="vm.actions.handleSearchInput" style="width: 100%">
                <template #suffix>
                  <el-icon>
                    <Search />
                  </el-icon>
                </template>
              </el-input>
            </el-form-item>

            <!-- 操作按钮 -->
            <el-form-item class="flex justify-center sm:justify-start w-full sm:w-auto">
              <el-button type="primary" @click="vm.actions.handleSearch">查询</el-button>
            </el-form-item>

            <!-- 时间范围 -->
            <el-form-item label="申请时间" class="w-full sm:w-auto ml-[12px]">
              <el-date-picker v-model="vm.state.filters.dateRange" type="daterange" range-separator="至"
                start-placeholder="开始日期" end-placeholder="结束日期" @change="vm.actions.handleDateRangeChange"
                style="width: 100%" />
            </el-form-item>

            <!-- 审核状态 -->
            <el-form-item label="审核状态" class="w-full sm:w-auto">
              <el-select v-model="vm.state.filters.status" placeholder="请选择状态" @change="vm.actions.handleStatusChange"
                style="width: 100%; min-width: 5.5rem;">
                <el-option label="全部" value="all" />
                <el-option label="待审核" value="pending" />
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
              </el-select>
            </el-form-item>
            <!-- 操作按钮 -->
            <el-form-item class="flex justify-center sm:justify-start w-full sm:w-auto">
              <el-button @click="vm.actions.handleReset">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
      <div class="flex flex-col sm:flex-row items-start sm:items-center space-y-3 sm:space-y-0 sm:space-x-4 h-full">

        <el-button type="primary" @click="vm.actions.handleBatchAudit" :disabled="!vm.computes.hasSelected.value">
          批量审核
        </el-button>
      </div>
    </div>

    <!-- 筛选条件区 -->
    <div class="filter-bar bg-white p-3 sm:p-4 rounded-lg shadow-sm mb-4 sm:mb-6 flex-shrink-0">
      <!-- 汇总数据展示 -->
      <div class="flex flex-wrap items-center gap-2 sm:gap-3 text-sm">
        <div class="bg-blue-50 px-3 py-1 rounded-lg flex items-center">
          <span class="text-gray-600">总数：</span>
          <span class="font-bold text-blue-600 ml-1">{{ vm.computes.summaryData.value.total }}</span>
        </div>
        <div class="bg-green-50 px-3 py-1 rounded-lg flex items-center">
          <span class="text-gray-600">通过：</span>
          <span class="font-bold text-green-600 ml-1">{{ vm.computes.summaryData.value.approved }}</span>
        </div>
        <div class="bg-orange-50 px-3 py-1 rounded-lg flex items-center">
          <span class="text-gray-600">待审核：</span>
          <span class="font-bold text-orange-600 ml-1">{{ vm.computes.summaryData.value.pending }}</span>
        </div>
        <div class="bg-red-50 px-3 py-1 rounded-lg flex items-center">
          <span class="text-gray-600">失败：</span>
          <span class="font-bold text-red-600 ml-1">{{ vm.computes.summaryData.value.rejected }}</span>
        </div>
      </div>

    </div>

    <!-- 列表内容区 -->
    <div class="audit-list flex-1 overflow-hidden">
      <!-- 加载状态 -->
      <div v-if="vm.state.loading" class="flex justify-center items-center py-12">
        <el-skeleton :rows="3" animated />
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="vm.computes.isEmpty.value" description="暂无数据" />

      <!-- 表格列表 -->
      <div v-else class="bg-white rounded-lg shadow-sm h-full overflow-hidden">
                 <el-table :data="vm.computes.filteredList.value" style="width: 100%" height="100%"
           @selection-change="vm.actions.handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column prop="storeName" label="门店名称" min-width="150" />
          <el-table-column prop="applyType" label="申请类型" width="100">
            <template #default="{ row }">
              <span>{{ row.applyType === 'new' ? '新店申请' : '续期申请' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="contactPhone" label="联系电话" width="130" />
          <el-table-column prop="location" label="所在地区" min-width="120">
            <template #default="{ row }">
              <span>{{ row.location.province }} {{ row.location.city }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="appliedAt" label="申请时间" width="150">
            <template #default="{ row }">
              <span>{{ vm.computes.formatDate(row.appliedAt) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="vm.computes.getStatusType(row.status)" size="small">
                {{ vm.computes.getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="150" fixed="right">
            <template #default="{ row }">
              <el-button v-if="row.status === 'pending'" type="success" size="small"
                @click="vm.actions.handleAudit(row.id, 'approve')">
                通过
              </el-button>
              <el-button v-if="row.status === 'pending'" type="danger" size="small"
                @click="vm.actions.handleAudit(row.id, 'reject')">
                拒绝
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 批量审核弹窗 -->
    <el-dialog v-model="vm.state.batchDialog.visible" title="批量审核" width="95%" max-width="500px">
      <el-form :model="vm.state.batchDialog.form" label-width="80px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="vm.state.batchDialog.form.action">
            <el-radio label="approve">通过</el-radio>
            <el-radio label="reject">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注模板">
          <el-select v-model="vm.state.batchDialog.form.templateId" placeholder="请选择备注模板" style="width: 100%">
            <el-option v-for="template in vm.state.templates" :key="template.id" :label="template.name"
              :value="template.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="vm.state.batchDialog.form.remark" type="textarea" rows="3" placeholder="请输入审核备注" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="flex justify-end">
          <el-button @click="vm.state.batchDialog.visible = false">取消</el-button>
          <el-button type="primary" @click="vm.actions.handleBatchConfirm">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { useAuditListPresenter } from './presenter'
import type { IAuditListViewModel } from './viewmodel'

const vm: IAuditListViewModel = useAuditListPresenter()
</script>

<style scoped>
/* 容器样式 */
.audit-list-container {
  height: 100%;
  min-height: 600px;
  padding: 20px;
  background-color: #f5f7fa;
}

:deep(.el-form--inline .el-form-item) {
  margin-bottom: 0 !important;
  margin-right: 0 !important;
}

/* 表格样式优化 */
.audit-list-container :deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

.audit-list-container :deep(.el-table__header-wrapper) {
  background-color: #f5f5f5;
}

.audit-list-container :deep(.el-table__row):hover {
  background-color: #f8f9fa;
}

/* 汇总数据样式 */
.audit-list-container .bg-blue-50 {
  background-color: #eff6ff;
}

.audit-list-container .bg-green-50 {
  background-color: #f0fdf4;
}

.audit-list-container .bg-orange-50 {
  background-color: #fff7ed;
}

.audit-list-container .bg-red-50 {
  background-color: #fef2f2;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .audit-list-container {
    padding: 15px;
  }

  .audit-list-container :deep(.el-table) {
    font-size: 12px;
  }

  .audit-list-container :deep(.el-table__cell) {
    padding: 8px 5px;
  }

  /* 汇总数据在移动端垂直排列 */
  .audit-list-container .flex-wrap {
    flex-direction: column;
    align-items: flex-start;
  }

  .audit-list-container .flex-wrap>div {
    width: 100%;
    justify-content: space-between;
  }
}

@media (max-width: 640px) {
  .audit-list-container {
    padding: 10px;
  }

  .audit-list-container h2 {
    font-size: 1.25rem;
  }

  .audit-list-container .text-sm {
    font-size: 0.75rem;
  }


}
</style>