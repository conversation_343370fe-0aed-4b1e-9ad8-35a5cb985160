import type { 
  AppVers<PERSON>, 
  H5Version, 
  VersionTree,
  Environment 
} from '@/entities/appversion/types'
import type {
  AppVersionView,
  H5VersionView,
  VersionTreeView,
  AppVersionFormData,
  H5VersionFormData,
  IVersionManageState,
  FilterState,
  PaginationState
} from './viewmodel'

/**
 * 版本管理数据转换器
 */
export class VersionManageConverter {
  
  /**
   * 创建初始状态
   */
  static createInitialState(): IVersionManageState {
    return {
      // 数据状态
      versionTreeList: [],
      currentApp: null,
      currentH5: null,
      
      // UI状态
      loading: false,
      submitting: false,
      
      // 筛选和分页
      filter: this.createDefaultFilter(),
      pagination: this.createDefaultPagination(),
      
      // 表单状态
      appFormData: this.createEmptyAppForm(),
      h5FormData: this.createEmptyH5Form(),
      
      // 弹窗状态
      showAppDialog: false,
      showH5Dialog: false,
      showConfirmDialog: false,
      
      // 操作标识
      editMode: 'create',
      operationType: 'app'
    }
  }

  /**
   * 创建默认筛选条件
   */
  static createDefaultFilter(): FilterState {
    return {
      clientType: '',
      environment: '',
      keyword: '',
      sortField: 'createTime',
      sortOrder: 'desc'
    }
  }

  /**
   * 创建默认分页状态
   */
  static createDefaultPagination(): PaginationState {
    return {
      pageNum: 1,
      pageSize: 10,
      total: 0
    }
  }

  /**
   * 创建空的APP表单数据
   */
  static createEmptyAppForm(): AppVersionFormData {
    return {
      clientType: 'CASHIER_ANDROID',
      environment: 'TEST',
      versionCode: null,
      versionName: '',
      upgradeTitle: '',
      upgradeContent: '',
      downloadUrl: '',
      fileSize: null,
      fileMd5: '',
      forceUpgrade: false
    }
  }

  /**
   * 创建空的H5表单数据
   */
  static createEmptyH5Form(): H5VersionFormData {
    return {
      appUpgradeId: '',
      domain: 'https://merp.dev.com/',
      h5Tag: '',
      environment: 'TEST',
      h5Url: '',
      description: '',
      isActive: false
    }
  }

  /**
   * 将版本树实体转换为视图数据
   */
  static toVersionTreeView(tree: VersionTree[]): VersionTreeView[] {
    const result: VersionTreeView[] = []
    
    tree.forEach(appItem => {
      // 添加APP版本
      const appView: VersionTreeView = {
        ...this.toAppVersionView(appItem),
        children: [],
        expanded: false
      }
      
      // 添加H5版本作为子节点
      if (appItem.children && appItem.children.length > 0) {
        appView.children = appItem.children.map(h5 => this.toH5VersionView(h5))
      }
      
      result.push(appView)
    })
    
    return result
  }

  /**
   * 将APP版本实体转换为视图数据
   */
  static toAppVersionView(app: AppVersion): AppVersionView {
    return {
      id: app.id || '',
      clientType: app.clientType,
      environment: app.environment,
      versionCode: app.versionCode,
      versionName: app.versionName,
      upgradeTitle: app.upgradeTitle,
      upgradeContent: app.upgradeContent,
      downloadUrl: app.downloadUrl,
      fileSize: app.fileSize,
      fileMd5: app.fileMd5,
      forceUpgrade: app.forceUpgrade,
      createTime: this.formatTimestamp(app.updateTime ?? app.createTime),
      updateTime: this.formatTimestamp(app.updateTime),
      
      // 视图层扩展属性
      statusText: '已发布',
      h5Count: (app as VersionTree)?.children?.length || 0,
      activeH5Count: (app as VersionTree)?.children?.filter(h5 => h5.isActive).length || 0
    }
  }

  /**
   * 将H5版本实体转换为视图数据
   */
  static toH5VersionView(h5: H5Version): H5VersionView {
    return {
      id: h5.id || '',
      appUpgradeId: h5.appUpgradeId,
      environment: h5.environment,
      h5Tag: h5.h5Tag,
      h5Url: h5.h5Url,
      description: h5.description,
      isActive: h5.isActive,
      createTime: this.formatTimestamp(h5.updateTime ?? h5.createTime),
      updateTime: this.formatTimestamp(h5.updateTime),
      
      // 视图层扩展属性
      statusText: h5.isActive ? '已激活' : '未激活'
    }
  }

  /**
   * 将APP视图数据转换为表单数据
   */
  static appViewToForm(app: AppVersionView): AppVersionFormData {
    return {
      id: app.id,
      clientType: app.clientType,
      environment: app.environment,
      versionCode: app.versionCode,
      versionName: app.versionName,
      upgradeTitle: app.upgradeTitle || '',
      upgradeContent: app.upgradeContent || '',
      downloadUrl: app.downloadUrl,
      fileSize: app.fileSize || null,
      fileMd5: app.fileMd5 || '',
      forceUpgrade: app.forceUpgrade
    }
  }

  /**
   * 将H5视图数据转换为表单数据
   */
  static h5ViewToForm(h5: H5VersionView): H5VersionFormData {
    let domain = ''
    if (h5.h5Url) {
      const idx = h5.h5Url.lastIndexOf('/')
      domain = idx >= 0 ? h5.h5Url.substring(0, idx + 1) : h5.h5Url
    }

    return {
      id: h5.id,
      appUpgradeId: h5.appUpgradeId,
      domain,
      environment: h5.environment,
      h5Tag: h5.h5Tag,
      h5Url: h5.h5Url,
      description: h5.description || '',
      isActive: h5.isActive
    }
  }

  /**
   * 将APP表单数据转换为请求数据
   */
  static appFormToRequest(form: AppVersionFormData) {
    const baseData = {
      clientType: form.clientType,
      environment: form.environment,
      versionCode: form.versionCode!,
      versionName: form.versionName,
      upgradeTitle: form.upgradeTitle || undefined,
      upgradeContent: form.upgradeContent || undefined,
      downloadUrl: form.downloadUrl,
      fileSize: form.fileSize || undefined,
      fileMd5: form.fileMd5 || undefined,
      forceUpgrade: form.forceUpgrade
    }

    // 如果有ID，返回更新请求数据；否则返回创建请求数据
    return form.id 
      ? { id: form.id, ...baseData }
      : baseData
  }

  /**
   * 将H5表单数据转换为请求数据
   */
  static h5FormToRequest(form: H5VersionFormData) {
    const domainFixed = form.domain.endsWith('/') ? form.domain : `${form.domain}/`
    const h5Url = `${domainFixed}${form.h5Tag}`

    const baseData = {
      appUpgradeId: form.appUpgradeId,
      environment: form.environment,
      h5Tag: form.h5Tag,
      h5Url,
      description: form.description || undefined,
      isActive: form.isActive
    }

    // 如果有ID，返回更新请求数据；否则返回创建请求数据
    return form.id 
      ? { id: form.id, ...baseData }
      : baseData
  }

  /**
   * 格式化时间戳
   */
  static formatTimestamp(timestamp?: number): string {
    if (!timestamp) return ''
    
    // 若为秒级时间戳（10位），转换为毫秒
    if (timestamp < 1e11) {
      timestamp = timestamp * 1000
    }
    const date = new Date(timestamp)
    const year = date.getFullYear()
    const month = String(date.getMonth() + 1).padStart(2, '0')
    const day = String(date.getDate()).padStart(2, '0')
    const hour = String(date.getHours()).padStart(2, '0')
    const minute = String(date.getMinutes()).padStart(2, '0')
    
    return `${year}-${month}-${day} ${hour}:${minute}`
  }

  /**
   * 格式化文件大小
   */
  static formatFileSize(size: number): string {
    if (!size) return ''
    
    const units = ['B', 'KB', 'MB', 'GB']
    let unitIndex = 0
    let fileSize = size
    
    while (fileSize >= 1024 && unitIndex < units.length - 1) {
      fileSize /= 1024
      unitIndex++
    }
    
    return `${fileSize.toFixed(unitIndex === 0 ? 0 : 1)} ${units[unitIndex]}`
  }

  /**
   * 获取客户端类型文本
   */
  static getClientTypeText(type: string): string {
    switch (type) {
      case 'CASHIER_ANDROID':
        return '收银端Android'
      case 'CASHIER_WINDOWS':
        return '收银端Windows'
      case 'MOBILE_ORDER':
        return '移动点单Android'
      default:
        return '未知'
    }
  }

  /**
   * 获取状态标签类型
   */
  static getStatusTagType(isActive: boolean): string {
    return isActive ? 'success' : 'info'
  }

  /**
   * 验证APP表单数据
   */
  static validateAppForm(form: AppVersionFormData): boolean {
    return !!(
      form.clientType &&
      form.environment &&
      form.versionCode &&
      form.versionCode > 0 &&
      form.versionName.trim() &&
      form.downloadUrl.trim()
    )
  }

  /**
   * 验证H5表单数据
   */
  static validateH5Form(form: H5VersionFormData): boolean {
    return !!(
      form.appUpgradeId &&
      form.domain.trim() &&
      form.environment &&
      form.h5Tag.trim()
    )
  }

  /**
   * 过滤版本树列表
   */
  static filterVersionTree(
    list: VersionTreeView[], 
    filter: FilterState
  ): VersionTreeView[] {
    let filtered = [...list]

    // 客户端类型筛选
    if (filter.clientType) {
      filtered = filtered.filter(item => item.clientType === filter.clientType)
    }

    // 环境筛选（H5级别）
    if (filter.environment) {
      filtered = filtered.filter(app => {
        return app.children.some(h5 => h5.environment === filter.environment)
      })
      // 同时过滤子节点
      filtered.forEach(app => {
        app.children = app.children.filter(h5 => h5.environment === filter.environment)
      })
    }

    // 关键词搜索
    if (filter.keyword) {
      const keyword = filter.keyword.toLowerCase()
      filtered = filtered.filter(item => 
        item.versionName.toLowerCase().includes(keyword) ||
        item.upgradeTitle?.toLowerCase().includes(keyword) ||
        item.children.some(h5 => 
          h5.h5Tag.toLowerCase().includes(keyword) ||
          h5.description?.toLowerCase().includes(keyword)
        )
      )
    }

    // 排序
    filtered.sort((a, b) => {
      const { sortField, sortOrder } = filter
      let aValue: any = a[sortField as keyof VersionTreeView]
      let bValue: any = b[sortField as keyof VersionTreeView]

      // 特殊处理数字类型字段
      if (sortField === 'versionCode') {
        aValue = Number(aValue)
        bValue = Number(bValue)
      }

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1
      return 0
    })

    return filtered
  }

  static getEnvironmentText(env: Environment): string {
    switch(env){
      case 'TEST': return '测试';
      case 'PREVIEW': return '预览';
      case 'PRODUCTION': return '线上';
      default: return '未知';
    }
  }
} 