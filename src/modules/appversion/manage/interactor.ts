import { ElMessage, ElMessageBox } from 'element-plus'
import {
  getVersionTree,
  createAppVersion,
  updateAppVersion,
  createH5Version,
  updateH5Version,
  deleteH5Version,
  deleteAppVersion
} from '@/api/appversion'
import type {
  CreateAppVersionReq,
  UpdateAppVersionReq,
  CreateH5VersionReq,
  UpdateH5VersionReq,
  GetVersionTreeReq,
  VersionTree,
  ClientType
} from '@/entities/appversion/types'
import { VersionManageConverter } from './converter'
import type {
  VersionTreeView,
  FilterState,
  PaginationState,
  AppVersionFormData,
  H5VersionFormData
} from './viewmodel'

/**
 * 版本管理业务逻辑层
 */
export class VersionManageInteractor {

  /**
   * 获取版本树数据
   */
  async fetchVersionTree(
    filter: FilterState,
    pagination: PaginationState
  ): Promise<{ list: VersionTreeView[], total: number }> {
    try {
      const req: GetVersionTreeReq = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize
      }

      // 只有选择了具体客户端类型才传递 clientType 参数
      if (filter.clientType) {
        req.clientType = filter.clientType as ClientType
      }

      const response = await getVersionTree(req)
      // 拦截器已保证 code === 0 才会进入此处，response 即为 PageResponse<VersionTree>
      const treeData = response.data || []
      const total = response.total || 0

      // 转换为视图数据
      const list = VersionManageConverter.toVersionTreeView(treeData)

      return { list, total }
    } catch (error) {
      console.error('获取版本树数据失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '获取版本列表失败')
      return { list: [], total: 0 }
    }
  }

  /**
   * 创建APP版本
   */
  async createApp(formData: AppVersionFormData): Promise<boolean> {
    try {
      const req = VersionManageConverter.appFormToRequest(formData) as CreateAppVersionReq
      await createAppVersion(req)
      // 能走到这里说明接口已成功
      ElMessage.success('创建APP版本成功')
      return true
    } catch (error) {
      console.error('创建APP版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '创建APP版本失败')
      return false
    }
  }

  /**
   * 更新APP版本
   */
  async updateApp(formData: AppVersionFormData): Promise<boolean> {
    try {
      const req = VersionManageConverter.appFormToRequest(formData) as UpdateAppVersionReq
      await updateAppVersion(req)
      ElMessage.success('更新APP版本成功')
      return true
    } catch (error) {
      console.error('更新APP版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '更新APP版本失败')
      return false
    }
  }

  /**
   * 删除APP版本（确认对话框）
   */
  async deleteApp(appId: string, versionName: string): Promise<boolean> {
    try {
      await ElMessageBox.confirm(
        `确定要删除APP版本 "${versionName}" 吗？删除后将无法恢复，且会同时删除该版本下的所有H5版本。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      )

      // 调用删除API
      await deleteAppVersion(appId)
      ElMessage.success('删除APP版本成功')
      return true
      
    } catch (error) {
      if (error === 'cancel') {
        return false
      }
      console.error('删除APP版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '删除APP版本失败')
      return false
    }
  }

  /**
   * 创建H5版本
   */
  async createH5(formData: H5VersionFormData): Promise<boolean> {
    try {
      const req = VersionManageConverter.h5FormToRequest(formData) as CreateH5VersionReq
      await createH5Version(req)
      ElMessage.success('创建H5版本成功')
      return true
    } catch (error) {
      console.error('创建H5版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '创建H5版本失败')
      return false
    }
  }

  /**
   * 更新H5版本
   */
  async updateH5(formData: H5VersionFormData): Promise<boolean> {
    try {
      const req = VersionManageConverter.h5FormToRequest(formData) as UpdateH5VersionReq
      await updateH5Version(req)
      ElMessage.success('更新H5版本成功')
      return true
    } catch (error) {
      console.error('更新H5版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '更新H5版本失败')
      return false
    }
  }

  /**
   * 删除H5版本（确认对话框）
   */
  async deleteH5(h5Id: string, h5Tag: string): Promise<boolean> {
    try {
      await ElMessageBox.confirm(
        `确定要删除H5版本 "${h5Tag}" 吗？删除后将无法恢复。`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning',
          confirmButtonClass: 'el-button--danger'
        }
      )

      // 调用删除API
      await deleteH5Version(h5Id)
      ElMessage.success('删除H5版本成功')
      return true
      
    } catch (error) {
      if (error === 'cancel') {
        return false
      }
      console.error('删除H5版本失败:', error)
      ElMessage.error(error instanceof Error ? error.message : '删除H5版本失败')
      return false
    }
  }

  /**
   * 切换H5版本激活状态
   */
  async toggleH5Active(h5: H5VersionFormData): Promise<boolean> {
    try {
      // 切换激活状态
      const updatedH5 = { ...h5, isActive: !h5.isActive }
      const result = await this.updateH5(updatedH5)
      
      if (result) {
        const statusText = updatedH5.isActive ? '激活' : '停用'
        ElMessage.success(`H5版本 ${statusText} 成功`)
      }
      
      return result
    } catch (error) {
      console.error('切换H5版本状态失败:', error)
      ElMessage.error('切换H5版本状态失败')
      return false
    }
  }

  /**
   * 验证APP表单数据
   */
  validateAppForm(formData: AppVersionFormData): { valid: boolean; message?: string } {
    if (!formData.clientType) {
      return { valid: false, message: '请选择客户端类型' }
    }
    
    if (!formData.versionCode || formData.versionCode <= 0) {
      return { valid: false, message: '请输入有效的版本号' }
    }
    
    if (!formData.versionName.trim()) {
      return { valid: false, message: '请输入版本名称' }
    }
    
    if (!formData.downloadUrl.trim()) {
      return { valid: false, message: '请输入下载地址' }
    }
    
    // 验证URL格式
    try {
      new URL(formData.downloadUrl)
    } catch {
      return { valid: false, message: '请输入有效的下载地址' }
    }
    
    return { valid: true }
  }

  /**
   * 验证H5表单数据
   */
  validateH5Form(formData: H5VersionFormData): { valid: boolean; message?: string } {
    if (!formData.appUpgradeId) {
      return { valid: false, message: '缺少关联的APP版本ID' }
    }
    
    if (!formData.domain.trim()) {
      return { valid: false, message: '请输入域名或前缀' }
    }

    if (!formData.h5Tag.trim()) {
      return { valid: false, message: '请输入H5版本标签' }
    }

    // 拼接生成完整 URL 后验证
    const domainFixed = formData.domain.endsWith('/') ? formData.domain : `${formData.domain}/`
    const fullUrl = `${domainFixed}${formData.h5Tag}`

    // 验证URL格式
    try {
      new URL(fullUrl)
    } catch {
      return { valid: false, message: '域名或标签格式不正确，无法生成合法链接' }
    }
    
    return { valid: true }
  }

  /**
   * 生成默认的H5版本标签
   */
  generateH5Tag(appVersionName: string): string {
    // 生成形如 v1.0.6-b1.20250703 的标签
    const now = new Date()
    const today = `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`

    // 去掉开头多余的 v
    let base = appVersionName.startsWith('v') ? appVersionName.slice(1) : appVersionName

    // 如果已有日期后缀与今天相同，则不再追加
    if (base.endsWith(today)) {
      return `v${base}`
    }

    return `v${base}.${today}`
  }

  /**
   * 生成默认的H5链接地址
   */
  generateH5Url(h5Tag: string): string {
    return `https://merp.dev.com/${h5Tag}/auth?deviceId=999`
  }
}

/**
 * 创建Interactor实例的组合函数
 */
export function useVersionManageInteractor() {
  return new VersionManageInteractor()
} 