import type { ComputedRef } from 'vue'
import type { AppVersion, H5Version, VersionTree, ClientType, Environment } from '@/entities/appversion/types'

// =============================================================================
// 视图层数据类型
// =============================================================================

// 视图层APP版本类型
export interface AppVersionView {
  id: string
  clientType: ClientType
  environment: Environment
  versionCode: number
  versionName: string
  upgradeTitle?: string
  upgradeContent?: string
  downloadUrl: string
  fileSize?: number
  fileMd5?: string
  forceUpgrade: boolean
  createTime: string      // 格式化后的创建时间
  updateTime: string      // 格式化后的更新时间
  
  // 视图层扩展属性
  statusText: string      // 状态文本：已发布、草稿等
  h5Count: number        // 关联的H5版本数量
  activeH5Count: number  // 激活的H5版本数量
}

// 视图层H5版本类型
export interface H5VersionView {
  id: string
  appUpgradeId: string
  environment: Environment
  h5Tag: string
  h5Url: string
  description?: string
  isActive: boolean
  createTime: string      // 格式化后的创建时间
  updateTime: string      // 格式化后的更新时间
  
  // 视图层扩展属性
  statusText: string      // 状态文本：已激活、未激活
}

// 视图层版本树形结构
export interface VersionTreeView extends AppVersionView {
  children: H5VersionView[]
  expanded?: boolean      // 是否展开
}

// =============================================================================
// 表单数据类型
// =============================================================================

// APP版本表单数据
export interface AppVersionFormData {
  id?: string
  clientType: ClientType
  environment: Environment
  versionCode: number | null
  versionName: string
  upgradeTitle: string
  upgradeContent: string
  downloadUrl: string
  fileSize: number | null
  fileMd5: string
  forceUpgrade: boolean
}

// H5版本表单数据
export interface H5VersionFormData {
  id?: string
  appUpgradeId: string
  domain: string          // 域名（前缀），UI 可编辑
  h5Tag: string           // 版本标签
  environment: Environment
  h5Url?: string          // 完整 URL，由 domain + tag 组装
  description: string
  isActive: boolean
}

// =============================================================================
// 筛选和分页数据类型
// =============================================================================

// 筛选条件
export interface FilterState {
  clientType: ClientType | ''  // 客户端类型筛选
  environment: Environment | '' // 环境筛选
  keyword: string                      // 关键词搜索
  sortField: string                    // 排序字段
  sortOrder: 'asc' | 'desc'           // 排序方向
}

// 分页状态
export interface PaginationState {
  pageNum: number
  pageSize: number
  total: number
}

// =============================================================================
// UI状态接口
// =============================================================================

export interface IVersionManageState {
  // 数据状态
  versionTreeList: VersionTreeView[]   // 版本树形列表
  currentApp: AppVersionView | null    // 当前选中的APP版本
  currentH5: H5VersionView | null      // 当前选中的H5版本
  
  // UI状态
  loading: boolean                     // 列表加载状态
  submitting: boolean                  // 表单提交状态
  
  // 筛选和分页
  filter: FilterState                  // 筛选条件
  pagination: PaginationState          // 分页状态
  
  // 表单状态
  appFormData: AppVersionFormData      // APP版本表单数据
  h5FormData: H5VersionFormData        // H5版本表单数据
  
  // 弹窗状态
  showAppDialog: boolean               // APP版本编辑弹窗
  showH5Dialog: boolean                // H5版本编辑弹窗
  showConfirmDialog: boolean           // 确认弹窗
  
  // 操作标识
  editMode: 'create' | 'edit'          // 编辑模式：创建或编辑
  operationType: 'app' | 'h5'          // 操作类型：APP版本或H5版本
}

// =============================================================================
// 计算属性接口
// =============================================================================

export interface IVersionManageComputed {
  // 过滤后的版本列表
  filteredVersionList: ComputedRef<VersionTreeView[]>
  
  // 统计数据
  totalAppVersions: ComputedRef<number>        // APP版本总数
  totalH5Versions: ComputedRef<number>         // H5版本总数
  activeH5Versions: ComputedRef<number>        // 激活的H5版本数
  
  // 表单验证
  appFormValid: ComputedRef<boolean>           // APP表单是否有效
  h5FormValid: ComputedRef<boolean>            // H5表单是否有效
  
  // 辅助方法
  getClientTypeText: ComputedRef<(type: string) => string>    // 获取客户端类型文本
  getStatusTagType: ComputedRef<(isActive: boolean) => string> // 获取状态标签类型
  formatFileSize: ComputedRef<(size: number) => string>       // 格式化文件大小
  getEnvironmentText: ComputedRef<(env: Environment) => string> // 环境文本
}

// =============================================================================
// 动作接口
// =============================================================================

export interface IVersionManageActions {
  // 数据加载
  loadVersionTree(): Promise<void>             // 加载版本树数据
  refreshData(): Promise<void>                 // 刷新数据
  
  // 筛选和分页
  updateFilter(filter: Partial<FilterState>): void    // 更新筛选条件
  changePage(page: number): void               // 切换页码
  changePageSize(size: number): void           // 切换每页大小
  
  // APP版本操作
  createAppVersion(): void                     // 创建APP版本
  editAppVersion(app: AppVersionView): void    // 编辑APP版本
  submitAppForm(): Promise<void>               // 提交APP表单
  deleteAppVersion(id: string): Promise<void>  // 删除APP版本
  
  // H5版本操作
  createH5Version(appId: string): void         // 创建H5版本
  editH5Version(h5: H5VersionView): void       // 编辑H5版本
  submitH5Form(): Promise<void>                // 提交H5表单
  deleteH5Version(id: string): Promise<void>   // 删除H5版本
  toggleH5Active(id: string): Promise<void>    // 切换H5版本激活状态
  
  // 弹窗控制
  closeAppDialog(): void                       // 关闭APP弹窗
  closeH5Dialog(): void                        // 关闭H5弹窗
  closeConfirmDialog(): void                   // 关闭确认弹窗
  
  // 表单操作
  resetAppForm(): void                         // 重置APP表单
  resetH5Form(): void                          // 重置H5表单
  
  // 树形操作
  toggleExpand(appId: string): void            // 切换展开/收起
  expandAll(): void                            // 展开所有
  collapseAll(): void                          // 收起所有
}

// =============================================================================
// 组合接口
// =============================================================================

export interface IVersionManageViewModel {
  state: IVersionManageState
  computes: IVersionManageComputed
  actions: IVersionManageActions
} 