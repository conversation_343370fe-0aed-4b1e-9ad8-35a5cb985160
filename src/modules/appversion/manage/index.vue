<!-- 版本管理页面 -->
<template>
  <div class="version-manage h-full flex flex-col">
    <!-- 页面标题和统计 -->
    <div class="page-header mb-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">应用版本管理</h1>
          <div class="flex items-center gap-6 mt-2 text-sm text-gray-600">
            <span>APP版本: {{ vm.computes.totalAppVersions.value }}</span>
            <span>H5版本: {{ vm.computes.totalH5Versions.value }}</span>
            <span>已激活: {{ vm.computes.activeH5Versions.value }}</span>
          </div>
        </div>
        <div class="flex gap-3">
          <el-button type="primary" @click="vm.actions.createAppVersion">
            <el-icon><Plus /></el-icon>
            新建APP版本
          </el-button>
        </div>
      </div>
    </div>

    <!-- 筛选操作栏 -->
    <div class="filter-bar mb-4 bg-white p-4 rounded-lg shadow-sm">
      <el-form :inline="true" class="flex items-center">
        <el-form-item label="客户端类型">
          <el-select 
            v-model="vm.state.filter.clientType"
            placeholder="全部类型"
            clearable
            class="w-32"
            @change="(value) => vm.actions.updateFilter({ clientType: value })"
          >
            <el-option label="收银端Android" value="CASHIER_ANDROID" />
            <el-option label="收银端Windows" value="CASHIER_WINDOWS" />
            <el-option label="移动点单Android" value="MOBILE_ORDER" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="环境">
          <el-select
            v-model="vm.state.filter.environment"
            placeholder="全部环境"
            clearable
            class="w-32"
            @change="val => vm.actions.updateFilter({ environment: val })"
          >
            <el-option label="测试" value="TEST" />
            <el-option label="预览" value="PREVIEW" />
            <el-option label="线上" value="PRODUCTION" />
          </el-select>
        </el-form-item>

        <el-form-item label="搜索">
          <el-input
            v-model="vm.state.filter.keyword"
            placeholder="版本名称、升级标题、H5标签"
            class="w-64"
            @input="(value) => vm.actions.updateFilter({ keyword: value })"
          >
            <template #suffix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="vm.actions.refreshData">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 版本列表 -->
    <div class="flex-1 overflow-hidden bg-white rounded-lg shadow-sm">
      <el-table
        v-loading="vm.state.loading"
        :data="vm.computes.filteredVersionList.value"
        row-key="id"
        height="100%"
        :default-expand-all="true"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <!-- 版本号列 -->
        <el-table-column label="版本号" min-width="10%" align="center">
          <template #default="{ row }">
            <template v-if="!row.appUpgradeId">
              <span class="font-mono text-gray-900">{{ row.versionCode }}</span>
            </template>
            <template v-else>
              <span class="text-gray-400">-</span>
            </template>
          </template>
        </el-table-column>

        <!-- 环境列 (APP显示) -->
        <el-table-column label="环境" min-width="8%" align="center">
          <template #default="{ row }">
            {{ vm.computes.getEnvironmentText.value(row.environment) || '-' }}
          </template>
        </el-table-column>

        <!-- 版本信息列 -->
        <el-table-column label="版本信息" min-width="20%">
          <template #default="{ row }">
            <!-- APP版本行 -->
            <div v-if="!row.appUpgradeId" class="app-version-row">
              <div class="font-medium text-gray-900">{{ row.versionName }}</div>
              <div class="text-sm text-gray-500">
                {{ vm.computes.getClientTypeText.value(row.clientType) }}
              </div>
            </div>
            
            <!-- H5版本行 -->
            <div v-else class="flex items-center gap-2 h5-version-row">
              <el-icon class="text-blue-500"><Link /></el-icon>
              <div>
                <div class="font-medium text-blue-700">{{ row.h5Tag }}</div>
                <div v-if="row.description" class="text-sm text-gray-500">{{ row.description }}</div>
              </div>
            </div>
          </template>
        </el-table-column>

        <!-- 状态列 -->
        <el-table-column label="状态" min-width="10%" align="center">
          <template #default="{ row }">
            <el-tag v-if="!row.appUpgradeId" type="success" size="small">
              {{ row.statusText }}
            </el-tag>
            <el-tag 
              v-else
              :type="vm.computes.getStatusTagType.value(row.isActive)"
              size="small"
            >
              {{ row.statusText }}
            </el-tag>
          </template>
        </el-table-column>

        <!-- 创建时间列 -->
        <el-table-column label="创建时间" min-width="18%" align="center">
          <template #default="{ row }">
            {{ row.createTime }}
          </template>
        </el-table-column>

        <!-- 强制升级列 (仅APP版本) -->
        <el-table-column label="是否强制升级" min-width="12%" align="center">
          <template #default="{ row }">
            <template v-if="!row.appUpgradeId">
              <el-tag :type="row.forceUpgrade ? 'danger' : 'info'" size="small">
                {{ row.forceUpgrade ? '是' : '否' }}
              </el-tag>
            </template>
            <template v-else>
              <span class="text-gray-400">-</span>
            </template>
          </template>
        </el-table-column>

        <!-- 操作列 -->
        <el-table-column label="操作" min-width="20%" fixed="right">
          <template #default="{ row }">
            <!-- APP版本操作 -->
            <template v-if="!row.appUpgradeId">
              <el-button 
                link 
                type="primary" 
                size="small"
                @click="vm.actions.editAppVersion(row)"
              >
                编辑
              </el-button>
              <el-button 
                link 
                type="success" 
                size="small"
                @click="vm.actions.createH5Version(row.id)"
              >
                新增H5
              </el-button>
              <el-button 
                link 
                type="danger" 
                size="small"
                @click="vm.actions.deleteAppVersion(row.id)"
              >
                删除
              </el-button>
            </template>
            
            <!-- H5版本操作 -->
            <template v-else>
              <el-button 
                link 
                type="primary" 
                size="small"
                @click="vm.actions.editH5Version(row)"
              >
                编辑
              </el-button>
              <el-button 
                link 
                :type="row.isActive ? 'warning' : 'success'"
                size="small"
                @click="vm.actions.toggleH5Active(row.id)"
              >
                {{ row.isActive ? '停用' : '激活' }}
              </el-button>
              <el-button 
                link 
                type="danger" 
                size="small"
                @click="vm.actions.deleteH5Version(row.id)"
              >
                删除
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper mt-4 flex justify-end">
      <el-pagination
        v-model:current-page="vm.state.pagination.pageNum"
        v-model:page-size="vm.state.pagination.pageSize"
        :total="vm.state.pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @current-change="vm.actions.changePage"
        @size-change="vm.actions.changePageSize"
      />
    </div>

    <!-- APP版本编辑弹窗 -->
    <el-dialog
      v-model="vm.state.showAppDialog"
      :title="`${vm.state.editMode === 'create' ? '新建' : '编辑'}APP版本`"
      width="600px"
      @close="vm.actions.closeAppDialog"
    >
      <el-form 
        :model="vm.state.appFormData"
        label-position="top"
        :disabled="vm.state.submitting"
      >
        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="客户端类型" required>
              <el-select v-model="vm.state.appFormData.clientType" class="w-full">
                <el-option label="收银端Android" value="CASHIER_ANDROID" />
                <el-option label="收银端Windows" value="CASHIER_WINDOWS" />
                <el-option label="移动点单Android" value="MOBILE_ORDER" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="环境" required>
              <el-select v-model="vm.state.appFormData.environment" class="w-full">
                <el-option label="测试" value="TEST" />
                <el-option label="预览" value="PREVIEW" />
                <el-option label="线上" value="PRODUCTION" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="版本号" required>
              <el-input-number 
                v-model="vm.state.appFormData.versionCode"
                :min="1"
                class="w-full"
                placeholder="如: 1001"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="版本名称" required>
          <el-input 
            v-model="vm.state.appFormData.versionName"
            placeholder="如: 1.0.1"
          />
        </el-form-item>

        <el-form-item label="升级标题">
          <el-input 
            v-model="vm.state.appFormData.upgradeTitle"
            placeholder="如: 新版本发布"
          />
        </el-form-item>

        <el-form-item label="升级内容">
          <el-input 
            v-model="vm.state.appFormData.upgradeContent"
            type="textarea"
            :rows="3"
            placeholder="描述本次版本的更新内容"
          />
        </el-form-item>

        <el-form-item label="下载地址" required>
          <el-input 
            v-model="vm.state.appFormData.downloadUrl"
            placeholder="APK/IPA文件的下载地址"
          />
        </el-form-item>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="文件大小(字节)">
              <el-input-number 
                v-model="vm.state.appFormData.fileSize"
                :min="0"
                class="w-full"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="强制升级">
              <el-switch v-model="vm.state.appFormData.forceUpgrade" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="MD5校验值">
          <el-input 
            v-model="vm.state.appFormData.fileMd5"
            placeholder="文件MD5值（可选）"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="vm.actions.closeAppDialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="vm.state.submitting"
            :disabled="!vm.computes.appFormValid.value"
            @click="vm.actions.submitAppForm"
          >
            {{ vm.state.editMode === 'create' ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- H5版本编辑弹窗 -->
    <el-dialog
      v-model="vm.state.showH5Dialog"
      :title="`${vm.state.editMode === 'create' ? '新建' : '编辑'}H5版本`"
      width="600px"
      @close="vm.actions.closeH5Dialog"
    >
      <el-form 
        :model="vm.state.h5FormData"
        label-position="top"
        :disabled="vm.state.submitting"
      >
        <el-form-item label="域名/前缀" required>
          <el-input 
            v-model="vm.state.h5FormData.domain"
            placeholder="如: https://merp.dev.com/"
          />
        </el-form-item>

        <el-form-item label="环境" required>
          <el-select v-model="vm.state.h5FormData.environment" class="w-full">
            <el-option label="测试" value="TEST" />
            <el-option label="预览" value="PREVIEW" />
            <el-option label="线上" value="PRODUCTION" />
          </el-select>
        </el-form-item>

        <el-form-item label="H5版本标签" required>
          <el-input 
            v-model="vm.state.h5FormData.h5Tag"
            placeholder="如: v1.0.1.2025"
          />
        </el-form-item>

        <el-form-item label="完整URL">
          <el-input 
            :model-value="`${vm.state.h5FormData.domain.endsWith('/') ? vm.state.h5FormData.domain : vm.state.h5FormData.domain + '/'}${vm.state.h5FormData.h5Tag}`"
            disabled
          />
        </el-form-item>

        <el-form-item label="版本描述">
          <el-input 
            v-model="vm.state.h5FormData.description"
            type="textarea"
            :rows="3"
            placeholder="描述这个H5版本的用途或特性"
          />
        </el-form-item>

        <el-form-item label="激活状态">
          <el-switch 
            v-model="vm.state.h5FormData.isActive"
            active-text="激活"
            inactive-text="停用"
          />
          <div class="text-sm text-gray-500 mt-1">
            同一APP版本下只能有一个激活的H5版本
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="vm.actions.closeH5Dialog">取消</el-button>
          <el-button 
            type="primary" 
            :loading="vm.state.submitting"
            :disabled="!vm.computes.h5FormValid.value"
            @click="vm.actions.submitH5Form"
          >
            {{ vm.state.editMode === 'create' ? '创建' : '保存' }}
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { 
  Plus, 
  Search, 
  Refresh, 
  Link 
} from '@element-plus/icons-vue'
import type { IVersionManageViewModel } from './viewmodel'
import { useVersionManagePresenter } from './presenter'

// 创建视图模型实例
const vm: IVersionManageViewModel = useVersionManagePresenter()
</script>

<style scoped>
.version-manage {
  height: calc(100vh - var(--header-height));
}

.page-header {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 16px;
}

.app-version-row {
  padding: 4px 0;
}

.h5-version-row {
  padding: 2px 0;
}

.filter-bar :deep(.el-form-item) {
  margin-bottom: 0;
}

.pagination-wrapper {
  padding: 16px 0;
  border-top: 1px solid #f0f0f0;
}

/* 树形表格的缩进样式优化 */
:deep(.el-table__row .cell) {
  padding: 8px 11px;
}

:deep(.el-table .el-table__expand-column .cell) {
  padding: 0;
}

/* 状态标签样式 */
.el-tag {
  border: none;
  font-weight: 500;
}

/* 操作按钮样式 */
.el-button--text {
  padding: 0;
  margin-right: 8px;
}

.el-button--text:last-child {
  margin-right: 0;
}

/* 弹窗表单样式 */
.dialog-footer {
  text-align: right;
}

.dialog-footer .el-button {
  margin-left: 12px;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
  }
  
  .page-header .flex {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .filter-bar .el-form {
    flex-direction: column;
    gap: 16px;
  }
  
  .filter-bar .el-form-item {
    width: 100%;
  }
}

/* 隐藏 Element Plus 树形表格展开箭头 */
:deep(.el-table__expand-icon) {
  display: none !important;
}
</style> 