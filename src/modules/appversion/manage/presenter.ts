import { reactive, computed, ComputedRef, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import type {
  IVersionManageViewModel,
  IVersionManageState,
  IVersionManageComputed,
  IVersionManageActions,
  VersionTreeView,
  AppVersionView,
  H5VersionView,
  FilterState
} from './viewmodel'
import { VersionManageConverter } from './converter'
import { useVersionManageInteractor } from './interactor'

/**
 * 版本管理模块展示层
 */
export class VersionManagePresenter implements IVersionManageViewModel {
  private interactor = useVersionManageInteractor()
  public state: IVersionManageState

  constructor() {
    // 初始化状态
    this.state = reactive(VersionManageConverter.createInitialState())
    // 设置生命周期
    this.setupLifecycles()
  }

  /**
   * 计算属性
   */
  public computes: IVersionManageComputed = {
    // 过滤后的版本列表
    filteredVersionList: computed(() => {
      return VersionManageConverter.filterVersionTree(
        this.state.versionTreeList,
        this.state.filter
      )
    }),

    // APP版本总数
    totalAppVersions: computed(() => {
      return this.state.versionTreeList.length
    }),

    // H5版本总数
    totalH5Versions: computed(() => {
      return this.state.versionTreeList.reduce((total, app) => total + app.h5Count, 0)
    }),

    // 激活的H5版本数
    activeH5Versions: computed(() => {
      return this.state.versionTreeList.reduce((total, app) => total + app.activeH5Count, 0)
    }),

    // APP表单验证
    appFormValid: computed(() => {
      return VersionManageConverter.validateAppForm(this.state.appFormData)
    }),

    // H5表单验证
    h5FormValid: computed(() => {
      return VersionManageConverter.validateH5Form(this.state.h5FormData)
    }),

    // 获取客户端类型文本
    getClientTypeText: computed(() => (type: string): string => {
      return VersionManageConverter.getClientTypeText(type)
    }),

    // 获取状态标签类型
    getStatusTagType: computed(() => (isActive: boolean): string => {
      return VersionManageConverter.getStatusTagType(isActive)
    }),

    // 获取环境文本
    getEnvironmentText: computed(() => (env: any): string => {
      return VersionManageConverter.getEnvironmentText(env)
    }),

    // 格式化文件大小
    formatFileSize: computed(() => (size: number): string => {
      return VersionManageConverter.formatFileSize(size)
    })
  }

  /**
   * 动作方法
   */
  public actions: IVersionManageActions = {
    // =========================================================================
    // 数据加载
    // =========================================================================
    
    loadVersionTree: async () => {
      this.state.loading = true
      try {
        const result = await this.interactor.fetchVersionTree(
          this.state.filter,
          this.state.pagination
        )
        
        this.state.versionTreeList = result.list
        this.state.pagination.total = result.total
      } catch (error) {
        console.error('加载版本树失败:', error)
      } finally {
        this.state.loading = false
      }
    },

    refreshData: async () => {
      await this.actions.loadVersionTree()
    },

    // =========================================================================
    // 筛选和分页
    // =========================================================================
    
    updateFilter: (filter: Partial<FilterState>) => {
      Object.assign(this.state.filter, filter)
      // 重置页码
      this.state.pagination.pageNum = 1
      // 重新加载数据
      this.actions.loadVersionTree()
    },

    changePage: (page: number) => {
      this.state.pagination.pageNum = page
      this.actions.loadVersionTree()
    },

    changePageSize: (size: number) => {
      this.state.pagination.pageSize = size
      this.state.pagination.pageNum = 1
      this.actions.loadVersionTree()
    },

    // =========================================================================
    // APP版本操作
    // =========================================================================
    
    createAppVersion: () => {
      this.state.editMode = 'create'
      this.state.operationType = 'app'
      this.state.appFormData = VersionManageConverter.createEmptyAppForm()
      this.state.showAppDialog = true
    },

    editAppVersion: (app: AppVersionView) => {
      this.state.editMode = 'edit'
      this.state.operationType = 'app'
      this.state.currentApp = app
      this.state.appFormData = VersionManageConverter.appViewToForm(app)
      this.state.showAppDialog = true
    },

    submitAppForm: async () => {
      // 表单验证
      const validation = this.interactor.validateAppForm(this.state.appFormData)
      if (!validation.valid) {
        ElMessage.warning(validation.message)
        return
      }

      this.state.submitting = true
      try {
        let success = false
        
        if (this.state.editMode === 'create') {
          success = await this.interactor.createApp(this.state.appFormData)
        } else {
          success = await this.interactor.updateApp(this.state.appFormData)
        }

        if (success) {
          this.actions.closeAppDialog()
          await this.actions.refreshData()
        }
      } catch (error) {
        console.error('提交APP表单失败:', error)
      } finally {
        this.state.submitting = false
      }
    },

    deleteAppVersion: async (id: string) => {
      const app = this.state.versionTreeList.find(item => item.id === id)
      if (!app) return

      const success = await this.interactor.deleteApp(id, app.versionName)
      if (success) {
        await this.actions.refreshData()
      }
    },

    // =========================================================================
    // H5版本操作
    // =========================================================================
    
    createH5Version: (appId: string) => {
      const app = this.state.versionTreeList.find(item => item.id === appId)
      if (!app) return

      this.state.editMode = 'create'
      this.state.operationType = 'h5'
      this.state.currentApp = app
      
      // 预填充H5表单数据
      const h5FormData = VersionManageConverter.createEmptyH5Form()
      h5FormData.appUpgradeId = appId
      h5FormData.h5Tag = this.interactor.generateH5Tag(app.versionName)
      
      this.state.h5FormData = h5FormData
      this.state.showH5Dialog = true
    },

    editH5Version: (h5: H5VersionView) => {
      this.state.editMode = 'edit'
      this.state.operationType = 'h5'
      this.state.currentH5 = h5
      this.state.h5FormData = VersionManageConverter.h5ViewToForm(h5)
      this.state.showH5Dialog = true
    },

    submitH5Form: async () => {
      // 表单验证
      const validation = this.interactor.validateH5Form(this.state.h5FormData)
      if (!validation.valid) {
        ElMessage.warning(validation.message)
        return
      }

      this.state.submitting = true
      try {
        let success = false
        
        if (this.state.editMode === 'create') {
          success = await this.interactor.createH5(this.state.h5FormData)
        } else {
          success = await this.interactor.updateH5(this.state.h5FormData)
        }

        if (success) {
          this.actions.closeH5Dialog()
          await this.actions.refreshData()
        }
      } catch (error) {
        console.error('提交H5表单失败:', error)
      } finally {
        this.state.submitting = false
      }
    },

    deleteH5Version: async (id: string) => {
      // 找到对应的H5版本
      let h5: H5VersionView | undefined
      for (const app of this.state.versionTreeList) {
        h5 = app.children.find(item => item.id === id)
        if (h5) break
      }

      if (!h5) return

      const success = await this.interactor.deleteH5(id, h5.h5Tag)
      if (success) {
        await this.actions.refreshData()
      }
    },

    toggleH5Active: async (id: string) => {
      // 找到对应的H5版本
      let h5: H5VersionView | undefined
      for (const app of this.state.versionTreeList) {
        h5 = app.children.find(item => item.id === id)
        if (h5) break
      }

      if (!h5) return

      const h5FormData = VersionManageConverter.h5ViewToForm(h5)
      const success = await this.interactor.toggleH5Active(h5FormData)
      
      if (success) {
        await this.actions.refreshData()
      }
    },

    // =========================================================================
    // 弹窗控制
    // =========================================================================
    
    closeAppDialog: () => {
      this.state.showAppDialog = false
      this.state.currentApp = null
      this.actions.resetAppForm()
    },

    closeH5Dialog: () => {
      this.state.showH5Dialog = false
      this.state.currentH5 = null
      this.actions.resetH5Form()
    },

    closeConfirmDialog: () => {
      this.state.showConfirmDialog = false
    },

    // =========================================================================
    // 表单操作
    // =========================================================================
    
    resetAppForm: () => {
      this.state.appFormData = VersionManageConverter.createEmptyAppForm()
    },

    resetH5Form: () => {
      this.state.h5FormData = VersionManageConverter.createEmptyH5Form()
    },

    // =========================================================================
    // 树形操作 (由Element Plus自动处理)
    // =========================================================================
    
    toggleExpand: (appId: string) => {
      // Element Plus 会自动处理展开/收起逻辑
    },

    expandAll: () => {
      // Element Plus 会自动处理展开/收起逻辑
    },

    collapseAll: () => {
      // Element Plus 会自动处理展开/收起逻辑
    }
  }

  /**
   * 设置生命周期钩子
   */
  private setupLifecycles() {
    onMounted(() => {
      this.actions.loadVersionTree()
    })
  }
}

/**
 * 创建版本管理视图模型实例的组合函数
 */
export function useVersionManagePresenter(): IVersionManageViewModel {
  return new VersionManagePresenter()
} 