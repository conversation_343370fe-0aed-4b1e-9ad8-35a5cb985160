<template>
  <div class="min-h-screen w-full flex items-stretch bg-gray-50">
    <!-- 左侧品牌展示区 -->
    <div class="hidden lg:flex lg:w-[60%] bg-gradient-to-br from-blue-600 to-indigo-800">
      <div class="flex-1 flex flex-col items-center justify-center p-8 text-center text-white">
        <img src="@/assets/images/logo.png" alt="Logo" class="w-32 h-32 mb-8 object-contain">
        <h1 class="text-4xl font-bold mb-4">商务授权管理系统</h1>
        <p class="text-xl opacity-80">高效 · 安全 · 专业</p>
      </div>
    </div>

    <!-- 右侧登录区 -->
    <div class="flex-1 flex items-center justify-center p-4 md:p-8 bg-gray-50">
      <div class="w-full max-w-md bg-white rounded-lg shadow-xl p-8">
        <h2 class="text-2xl font-bold text-center text-gray-800 mb-8">账号登录</h2>
        
        <el-form
          ref="formRef"
          :model="vm.state"
          :rules="vm.computes.formRules.value"
          label-position="top"
          @submit.prevent="vm.actions.handleSubmit(formRef)"
          class="w-full"
        >
          <!-- 手机号输入框 -->
          <el-form-item label="手机号" prop="mobile" class="mb-4">
            <el-input
              v-model="vm.state.mobile"
              placeholder="请输入手机号"
              @change="vm.actions.handleMobileChange"
              class="w-full"
            >
              <template #prefix>
                <el-icon><Phone /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 密码输入框 -->
          <el-form-item label="密码" prop="password" class="mb-4">
            <el-input
              v-model="vm.state.password"
              type="password"
              placeholder="请输入密码"
              @change="vm.actions.handlePasswordChange"
              show-password
              class="w-full"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <!-- 记住密码 -->
          <el-form-item class="mb-6">
            <el-checkbox
              v-model="vm.state.rememberMe"
              class="text-gray-600"
            >
              记住密码
            </el-checkbox>
          </el-form-item>

          <!-- 错误提示 -->
          <div v-if="vm.state.errorMessage" class="mb-4 text-sm text-red-500">
            {{ vm.state.errorMessage }}
          </div>

          <!-- 锁定提示 -->
          <div v-if="vm.computes.isLocked.value" class="mb-4 text-sm text-orange-500">
            账号已锁定，请在 {{ vm.computes.lockCountdown.value }} 秒后重试
          </div>

          <!-- 登录按钮 -->
          <el-button
            type="primary"
            native-type="submit"
            :loading="vm.state.loading"
            :disabled="!vm.computes.isFormValid.value || vm.computes.isLocked.value"
            class="w-full h-10 text-base"
          >
            {{ vm.state.loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form>

        <!-- 底部版权信息 -->
        <div class="mt-8 text-center text-sm text-gray-500">
          <p>© {{ new Date().getFullYear() }} Thunder ERP. All rights reserved.</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { Phone, Lock } from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { useLoginPresenter } from './presenter'
import type { ILoginViewModel } from './viewmodel'

const formRef = ref<FormInstance>()
const vm: ILoginViewModel = useLoginPresenter()
</script>

<style scoped>
/* 原因分析：当使用 scoped 样式时，组件内部或子组件的样式会被添加独有属性，导致直接覆盖 Element Plus 的样式无效。
   解决方案：使用 Vue 3 提供的深度选择器 ::v-deep 来使样式作用于子组件内部的 .el-input__wrapper 类。 */
::v-deep .el-input__wrapper {
  box-shadow: 0 0 0 1px #d4d5dc !important;
  background-color: #fff;
  border-radius: 4px;
  transition: all 0.2s;
}

::v-deep .el-input__wrapper:hover {
  box-shadow: 0 0 0 1px #919197 !important;
}

::v-deep .el-input__wrapper.is-focus {
  box-shadow: 0 0 0 1px var(--el-color-primary) !important;
}

::v-deep .el-input__wrapper.is-error {
  box-shadow: 0 0 0 1px var(--el-color-danger) !important;
}
</style>
