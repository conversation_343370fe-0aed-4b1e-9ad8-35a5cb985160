import type { ILoginState } from './viewmodel'
import type { BusinessLoginReqDto } from '@/entities/auth/BusinessLoginReqDto'
import type { BusinessLoginVO } from '@/entities/auth/BusinessLoginVO'
import type { LoginStorageData } from './types/LoginStorageTypes'

export class LoginConverter {
    /**
     * 创建视图模型初始状态
     */
    static createInitialState(): ILoginState {
        return {
            mobile: '',
            password: '',
            rememberMe: false,
            loading: false,
            errorMessage: ''
        }
    }

    /**
     * 将存储数据转换为视图状态
     */
    static toViewStateFromStorage(state: ILoginState, fromStorage: LoginStorageData | null): void {
        if (!fromStorage) {
            return
        }

        state.mobile = fromStorage.mobile || ''
        state.rememberMe = Boolean(fromStorage.rememberMe)
    }

    /**
     * 将登录VO转换为视图状态
     */
    static toViewStateFromVO(state: ILoginState, fromVO: BusinessLoginVO): void {
        state.errorMessage = ''  // 登录成功时清空错误信息
        state.loading = false
    }

    /**
     * 将错误转换为视图状态
     */
    static toViewStateFromError(state: ILoginState, error: unknown): void {
        state.errorMessage = error instanceof Error ? error.message : '未知错误'
        state.loading = false
    }

    /**
     * 将视图状态转换为登录DTO
     */
    static toLoginDTO(state: ILoginState): BusinessLoginReqDto {
        return {
            mobile: state.mobile.trim(),
            password: state.password
        }
    }

    /**
     * 将视图状态转换为存储数据
     */
    static toStorage(state: ILoginState): LoginStorageData {
        return {
            mobile: state.mobile.trim(),
            rememberMe: state.rememberMe
        }
    }
}