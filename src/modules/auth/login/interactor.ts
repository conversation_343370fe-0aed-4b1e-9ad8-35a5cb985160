import { businessAuthApi } from '@/api/business/auth'
import { useAuthStore } from '@/shared/store/authStore'
import type { BusinessLoginReqDto } from '@/entities/auth/BusinessLoginReqDto'
import type { BusinessLoginVO } from '@/entities/auth/BusinessLoginVO'
import type { LoginStorageData } from './types/LoginStorageTypes'
import { LoginErrorCode, LoginError } from './types/LoginErrors'
import { LoginStatus } from '@/entities/auth/BusinessLoginVO'
import { ApiError } from '@/shared/types/ApiError'

export class LoginInteractor {
    private authStore = useAuthStore()
    private lockInfo: {
        mobile: string;
        isLocked: boolean;
        lockedUntil: number;
    } = {
        mobile: '',
        isLocked: false,
        lockedUntil: 0
    }

    /**
     * 处理API错误
     */
    private handleApiError(error: ApiError): never {
        // 根据API错误码转换为业务错误
        switch (error.code) {
            case LoginErrorCode.INVALID_PASSWORD:
                throw LoginError.fromApiError(error)

            case LoginErrorCode.INVALID_MOBILE:
                throw LoginError.fromApiError(error)

            case LoginErrorCode.ACCOUNT_LOCKED:
                const lockedUntil = error.attachments?.lockedUntil
                if (lockedUntil) {
                    this.setLockStatus(error.attachments?.mobile || '', true, Number(lockedUntil))
                }
                throw new LoginError(
                    LoginErrorCode.ACCOUNT_LOCKED,
                    `账号已锁定，请在${this.getLockCountdown()}秒后重试`
                )

            default:
                throw LoginError.fromApiError(error)
        }
    }

    /**
     * 处理登录业务
     */
    async handleLogin(loginDto: BusinessLoginReqDto): Promise<BusinessLoginVO> {
        try {
            // 检查是否被锁定
            if (this.isAccountLocked(loginDto.mobile)) {
                throw new LoginError(
                    LoginErrorCode.ACCOUNT_LOCKED,
                    `账号已锁定，请在${this.getLockCountdown()}秒后重试`
                )
            }

            // 调用登录接口
            const response = await businessAuthApi.login(loginDto)

            // 处理登录响应
            switch (response.status) {
                case LoginStatus.NORMAL:
                    await this.handleLoginSuccess(response)
                    return response

                case LoginStatus.PENDING_REVIEW:
                    throw new LoginError(
                        LoginErrorCode.PENDING_REVIEW,
                        '账号正在审核中，请等待审核通过'
                    )

                case LoginStatus.DISABLED:
                    throw new LoginError(
                        LoginErrorCode.INVALID_MOBILE,
                        '账号已被禁用，请联系管理员'
                    )

                default:
                    throw new LoginError(
                        LoginErrorCode.SYSTEM_ERROR,
                        '登录失败，请重试'
                    )
            }
        } catch (error) {
            // 已经是业务错误，直接抛出
            if (error instanceof LoginError) {
                throw error
            }
            // 处理API错误
            if (error instanceof ApiError) {
                // console.log("before handleApiError", error, "error.code", error.code)
                this.handleApiError(error)
            }

            // 其他未知错误
            throw new LoginError(
                LoginErrorCode.SYSTEM_ERROR,
                error instanceof Error ? error.message : '系统错误，请稍后重试'
            )
        }
    }

    /**
     * 处理登录成功
     */
    private async handleLoginSuccess(loginVO: BusinessLoginVO): Promise<void> {
        // 更新认证状态
        this.authStore.handleLoginSuccess(loginVO)
        // 清除锁定状态
        this.clearLockStatus()
    }

    /**
     * 获取存储的登录数据
     */
    getStorageData(): LoginStorageData | null {
        try {
            const data = localStorage.getItem('loginInfo')
            return data ? JSON.parse(data) : null
        } catch {
            return null
        }
    }

    /**
     * 保存登录数据
     */
    saveStorageData(data: LoginStorageData | null): void {
        if (data) {
            localStorage.setItem('loginInfo', JSON.stringify(data))
        } else {
            localStorage.removeItem('loginInfo')
        }
    }

    /**
     * 登出
     */
    async handleLogout(): Promise<void> {
        try {
            await businessAuthApi.logout()
            this.clearLoginState()
        } catch (error) {
            console.error('Logout failed:', error)
            // 即使登出API失败，也清除本地状态
            this.clearLoginState()
        }
    }

    /**
     * 清除登录状态
     */
    private clearLoginState(): void {
        this.authStore.reset()
        this.clearLockStatus()
        this.saveStorageData(null)
    }

    /**
     * 检查账号是否被锁定
     */
    isAccountLocked(mobile: string): boolean {
        const now = Date.now() / 1000
        return this.lockInfo.mobile === mobile && 
               this.lockInfo.isLocked && 
               this.lockInfo.lockedUntil > now
    }

    /**
     * 获取锁定倒计时
     */
    getLockCountdown(): number {
        const now = Date.now() / 1000
        if (!this.lockInfo.isLocked) return 0
        const countdown = Math.ceil(this.lockInfo.lockedUntil - now)
        console.log("countdown", countdown, "this.lockInfo.lockedUntil", this.lockInfo.lockedUntil, "now", now)
        return countdown > 0 ? countdown : 0
    }

    /**
     * 设置锁定状态
     */
    setLockStatus(mobile: string, isLocked: boolean, lockedUntil: number = 0): void {
        this.lockInfo = { mobile, isLocked, lockedUntil }
    }

    /**
     * 清除锁定状态
     */
    clearLockStatus(): void {
        this.lockInfo = { mobile: '', isLocked: false, lockedUntil: 0 }
    }
}

export function useLoginInteractor() {
    return new LoginInteractor()
}
