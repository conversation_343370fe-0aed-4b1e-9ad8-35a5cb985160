import { ApiError } from '@/shared/types/ApiError'

/**
 * 登录业务错误
 */
export class LoginError extends Error {
    constructor(
        public code: number,
        message: string,
        public attachments?: any
    ) {
        super(message)
        this.name = 'LoginError'
    }

    /**
     * 从API错误创建登录错误
     */
    static fromApiError(error: ApiError): LoginError {
        return new LoginError(error.code, error.message, error.attachments)
    }
}

/**
 * 登录错误码
 */
export enum LoginErrorCode {
    INVALID_MOBILE = 4003,
    PENDING_REVIEW = 4004,
    INVALID_PASSWORD = 4005,
    ACCOUNT_LOCKED = 4002,
    SYSTEM_ERROR = 500
} 