import { ComputedRef } from 'vue'
import type { FormRules, FormInstance, FormItemRule } from 'element-plus'

// 登录表单状态
export interface ILoginState {
  mobile: string
  password: string
  rememberMe: boolean
  loading: boolean
  errorMessage: string
}

// 登录表单计算属性
export interface ILoginComputed {
  isLocked: ComputedRef<boolean>
  lockCountdown: ComputedRef<number>
  formRules: ComputedRef<FormRules>
  isFormValid: ComputedRef<boolean>
  showErrorMessage: ComputedRef<boolean>
}

// 登录表单动作
export interface ILoginActions {
  handleSubmit(formEl: FormInstance | undefined): Promise<void>
  handleMobileChange(): void
  handlePasswordChange(): void
  resetForm(formEl: FormInstance | undefined): void
  handleRememberMeChange(): void
}

// 组合接口
export interface ILoginViewModel {
  state: ILoginState
  computes: ILoginComputed
  actions: ILoginActions
}

// 表单验证规则类型
export interface ILoginFormRules {
  mobile: FormItemRule[]
  password: FormItemRule[]
}

// 登录结果类型
export interface ILoginResult {
  success: boolean
  error?: string
}
