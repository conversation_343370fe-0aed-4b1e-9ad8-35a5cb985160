import { reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { FormInstance } from 'element-plus'
import type { ILoginViewModel } from './viewmodel'
import { useLoginInteractor } from './interactor'
import { LoginConverter } from './converter'
import { LoginError, LoginErrorCode } from './types/LoginErrors'

export function useLoginPresenter(): ILoginViewModel {
    const router = useRouter()
    const interactor = useLoginInteractor()
    
    // 初始化视图状态
    const state = reactive(LoginConverter.createInitialState())
    
    // 计算属性
    const computes = {
        // 是否锁定
        isLocked: computed(() => !!state.mobile && interactor.isAccountLocked(state.mobile)),
        
        // 锁定倒计时
        lockCountdown: computed(() => interactor.getLockCountdown()),
        
        // 表单验证规则
        formRules: computed(() => ({
            mobile: [
                { required: true, message: '请输入手机号', trigger: 'blur' },
                { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式', trigger: 'blur' }
            ],
            password: [
                { required: true, message: '请输入密码', trigger: 'blur' },
                { min: 8, message: '密码长度不能小于8位', trigger: 'blur' }
            ]
        })),
        
        // 表单是否有效
        isFormValid: computed(() => {
            return !!state.mobile && !!state.password && 
                   /^1[3-9]\d{9}$/.test(state.mobile) &&
                   state.password.length >= 8
        }),

        // 是否显示错误信息
        showErrorMessage: computed(() => !!state.errorMessage)
    }
    
    // 动作处理
    const actions = {
        /**
         * 处理表单提交
         */
        async handleSubmit(formEl: FormInstance | undefined) {
            if (!formEl) return
            
            try {
                // 表单验证
                await formEl.validate()
                state.loading = true
                state.errorMessage = ''
                
                // 转换并提交数据
                const loginDto = LoginConverter.toLoginDTO(state)
                await interactor.handleLogin(loginDto)
                
                // 处理记住密码
                if (state.rememberMe) {
                    const storageData = LoginConverter.toStorage(state)
                    interactor.saveStorageData(storageData)
                } else {
                    interactor.saveStorageData(null)
                }
                
                // 登录成功，跳转
                const redirect = router.currentRoute.value.query.redirect as string
                router.push(redirect || '/')
                
            } catch (error) {
                // 错误处理
                if (error instanceof LoginError) {
                    switch (error.code) {
                        case LoginErrorCode.ACCOUNT_LOCKED:
                            state.errorMessage = error.message
                            break
                        case LoginErrorCode.INVALID_MOBILE:
                            state.errorMessage = '手机号不存在'
                            break
                        case LoginErrorCode.PENDING_REVIEW:
                            state.errorMessage = '账号正在审核中，请等待审核通过'
                            break
                        case LoginErrorCode.INVALID_PASSWORD:
                            console.log("error.attachments", error.attachments)
                            // 检查剩余尝试次数
                            if (error.attachments?.remainingAttempts <= 3) {
                                state.errorMessage = `密码错误，再输错${error.attachments.remainingAttempts}次将锁定账号`
                            } else {
                                state.errorMessage = '密码错误'
                            }
                            break
                        default:
                            state.errorMessage = error.message
                    }
                } else {
                    state.errorMessage = '登录失败，请重试'
                }
            } finally {
                state.loading = false
            }
        },

        /**
         * 处理手机号变更
         */
        handleMobileChange() {
            state.errorMessage = ''
            interactor.clearLockStatus()
        },

        /**
         * 处理密码变更
         */
        handlePasswordChange() {
            state.errorMessage = ''
        },

        /**
         * 处理记住密码变更
         */
        handleRememberMeChange() {
            if (state.rememberMe) {
                const storageData = LoginConverter.toStorage(state)
                interactor.saveStorageData(storageData)
            } else {
                interactor.saveStorageData(null)
            }
        },

        /**
         * 重置表单
         */
        resetForm(formEl: FormInstance | undefined) {
            if (!formEl) return
            formEl.resetFields()
            state.errorMessage = ''
            interactor.clearLockStatus()
            interactor.saveStorageData(null)
        }
    }

    // 生命周期处理
    onMounted(() => {
        // 从存储加载数据
        const storageData = interactor.getStorageData()
        if (storageData) {
            LoginConverter.toViewStateFromStorage(state, storageData)
        }
        // 清除锁定状态
        interactor.clearLockStatus()
    })

    onUnmounted(() => {
        interactor.clearLockStatus()
    })

    return {
        state,
        computes,
        actions
    }
}
