import { reactive, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { IProfileViewModel } from './types/ProfileState'
import { ProfileViewModel } from './viewmodel'
import { ProfileConverter } from './converter'
import { useProfileInteractor } from './interactor'
import { ProfileError } from './types/ProfileErrors'

/**
 * 个人中心Presenter
 */
export function useProfilePresenter(): IProfileViewModel {
  // 获取业务交互器
  const interactor = useProfileInteractor()
  const router = useRouter()

  // 创建响应式状态
  const state = reactive({
    ...ProfileConverter.createInitialState(),
    error: null as string | null
  })

  // 创建计算属性
  const computes = {
    displayName: computed(() => ProfileConverter.toDisplayName(state.userInfo)),
    maskedMobile: computed(() => ProfileConverter.toMaskedMobile(state.userInfo.mobile)),
    lastLoginTime: computed(() => ProfileConverter.toLastLoginTime(state.userInfo.lastLogin || 0)),
    permissionText: computed(() => ProfileConverter.toPermissionText(state.userInfo.permissionLevel)),
    statusType: computed(() => ProfileConverter.toStatusType(state.userInfo.permissionLevel)),
    statusText: computed(() => ProfileConverter.toStatusText(state.userInfo.permissionLevel)),
    defaultAvatar: computed(() => 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png')
  }

  // 创建动作处理器
  const actions = {
    /**
     * 刷新用户信息
     */
    refreshUserInfo() {
      try {
        state.loading = true
        state.error = null
        const user = interactor.getUserProfile()
        ProfileConverter.toViewState(state, user)
      } catch (error) {
        if (error instanceof ProfileError) {
          state.error = error.message
        } else {
          state.error = '获取用户信息失败'
        }
        console.error('刷新用户信息失败:', error)
      } finally {
        state.loading = false
      }
    },

    /**
     * 清除错误信息
     */
    clearError() {
      state.error = null
    },
  }

  // 创建视图模型
  const viewModel = ProfileViewModel.create(state, computes, actions)

  // 设置生命周期
  setupLifecycles()

  return viewModel

  /**
   * 设置生命周期
   */
  function setupLifecycles() {
    onMounted(() => {
      loadInitialData()
    })

    onUnmounted(() => {
      // 清理资源
      actions.clearError()
    })
  }

  /**
   * 加载初始数据
   */
  function loadInitialData() {
    actions.refreshUserInfo()
  }
} 