import type { IProfileState, IProfileComputed, IProfileActions, IProfileViewModel } from './types/ProfileState'
import type { User } from '@/shared/types/user'

/**
 * 个人中心视图模型实现
 */
export class ProfileViewModel implements IProfileViewModel {
  constructor(
    public readonly state: IProfileState,
    public readonly computes: IProfileComputed,
    public readonly actions: IProfileActions
  ) {}

  /**
   * 创建视图模型实例
   */
  static create(
    state: IProfileState,
    computes: IProfileComputed,
    actions: IProfileActions
  ): IProfileViewModel {
    return new ProfileViewModel(state, computes, actions)
  }
}

/**
 * 个人中心视图模型工厂函数
 */
export interface IProfileViewModelFactory {
  /**
   * 创建视图模型
   */
  createViewModel(): IProfileViewModel
} 