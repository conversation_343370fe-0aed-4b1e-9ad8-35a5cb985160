import type { ComputedRef } from 'vue'
import type { User } from '@/shared/types/user'

/**
 * 个人中心页面状态
 */
export interface IProfileState {
  /** 加载状态 */
  loading: boolean
  /** 用户信息 */
  userInfo: User
  /** 错误信息 */
  error: string | null
}

/**
 * 个人中心页面计算属性
 */
export interface IProfileComputed {
  /** 显示名称 */
  displayName: ComputedRef<string>
  /** 脱敏手机号 */
  maskedMobile: ComputedRef<string>
  /** 最后登录时间 */
  lastLoginTime: ComputedRef<string>
  /** 权限文本 */
  permissionText: ComputedRef<string>
  /** 状态类型 */
  statusType: ComputedRef<'success' | 'warning' | 'danger'>
  /** 状态文本 */
  statusText: ComputedRef<string>
  /** 默认头像 */
  defaultAvatar: ComputedRef<string>
}

/**
 * 个人中心页面动作
 */
export interface IProfileActions {
  /** 刷新用户信息 */
  refreshUserInfo(): void
  /** 清除错误信息 */
  clearError(): void
}

/**
 * 个人中心视图模型
 */
export interface IProfileViewModel {
  state: IProfileState
  computes: IProfileComputed
  actions: IProfileActions
} 