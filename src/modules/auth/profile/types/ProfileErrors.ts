import { ApiError } from '@/shared/types/ApiError'

/**
 * 个人中心错误码枚举
 */
export enum ProfileErrorCode {
  /** 更新信息失败 */
  UPDATE_FAILED = 10001,
  /** 头像上传失败 */
  AVATAR_UPLOAD_FAILED = 10002,
  /** 密码修改失败 */
  PASSWORD_CHANGE_FAILED = 10003,
  /** 手机号绑定失败 */
  MOBILE_BIND_FAILED = 10004,
  /** 验证码发送失败 */
  VERIFICATION_CODE_SEND_FAILED = 10005,
  /** 验证码验证失败 */
  VERIFICATION_CODE_VERIFY_FAILED = 10006
}

/**
 * 个人中心错误类型
 */
export class ProfileError extends ApiError {
  constructor(
    public code: ProfileErrorCode,
    message: string,
    public data?: any
  ) {
    super(code, message, data)
    this.name = 'ProfileError'
  }

  /**
   * 创建更新失败错误
   */
  static updateFailed(message = '更新个人信息失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.UPDATE_FAILED, message)
  }

  /**
   * 创建头像上传失败错误
   */
  static avatarUploadFailed(message = '头像上传失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.AVATAR_UPLOAD_FAILED, message)
  }

  /**
   * 创建密码修改失败错误
   */
  static passwordChangeFailed(message = '密码修改失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.PASSWORD_CHANGE_FAILED, message)
  }

  /**
   * 创建手机号绑定失败错误
   */
  static mobileBindFailed(message = '手机号绑定失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.MOBILE_BIND_FAILED, message)
  }

  /**
   * 创建验证码发送失败错误
   */
  static verificationCodeSendFailed(message = '验证码发送失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.VERIFICATION_CODE_SEND_FAILED, message)
  }

  /**
   * 创建验证码验证失败错误
   */
  static verificationCodeVerifyFailed(message = '验证码验证失败'): ProfileError {
    return new ProfileError(ProfileErrorCode.VERIFICATION_CODE_VERIFY_FAILED, message)
  }
} 