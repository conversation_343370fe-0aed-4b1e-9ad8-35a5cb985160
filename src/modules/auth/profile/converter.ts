import type { IProfileState } from './types/ProfileState'
import type { User } from '@/shared/types/user'

export class ProfileConverter {
  /**
   * 创建初始状态
   */
  static createInitialState(): IProfileState {
    return {
      loading: false,
      error: null,
      userInfo: {
        id: '',
        name: '',
        mobile: '',
        permissionLevel: 0,
        lastLogin: 0
      }
    }
  }

  /**
   * 将用户信息转换为视图状态
   */
  static toViewState(state: IProfileState, user: User): void {
    state.userInfo = { ...user }
  }

  /**
   * 将手机号转换为脱敏显示
   */
  static toMaskedMobile(mobile: string): string {
    if (!mobile) return '未绑定'
    return mobile.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
  }

  /**
   * 获取显示名称
   */
  static toDisplayName(user: User): string {
    return user.name || '未设置'
  }

  /**
   * 格式化最后登录时间
   */
  static toLastLoginTime(timestamp: number): string {
    if (!timestamp) return '暂无记录'
    return new Date(timestamp).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  /**
   * 转换权限等级为文本
   */
  static toPermissionText(permissionLevel: number): string {
    return permissionLevel === 1 ? '完全权限' : '基础权限'
  }

  /**
   * 转换权限等级为状态类型
   */
  static toStatusType(permissionLevel: number): 'success' | 'warning' | 'danger' {
    switch (permissionLevel) {
      case 1:
        return 'success'
      case 0:
        return 'warning'
      default:
        return 'danger'
    }
  }

  /**
   * 转换权限等级为状态文本
   */
  static toStatusText(permissionLevel: number): string {
    switch (permissionLevel) {
      case 1:
        return '正常'
      case 0:
        return '待审核'
      default:
        return '已禁用'
    }
  }
} 