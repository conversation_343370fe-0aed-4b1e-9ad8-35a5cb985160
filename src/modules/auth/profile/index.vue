<template>
  <div class="p-6">
    <!-- 加载状态 -->
    <el-skeleton :loading="vm.state.loading" animated :rows="3">
      <template #template>
        <div class="flex flex-col gap-4">
          <el-skeleton-item variant="p" style="width: 100%; height: 200px" />
          <el-skeleton-item variant="p" style="width: 100%; height: 300px" />
        </div>
      </template>

      <template #default>
        <el-row :gutter="20">
          <!-- 左侧：头像和基本信息 -->
          <el-col :span="8">
            <el-card class="profile-card h-full">
              <!-- 头像区域 -->
              <div class="flex flex-col items-center mb-6">
                <el-avatar 
                  :size="120" 
                  :src="vm.computes.defaultAvatar.value" 
                  class="mb-4"
                />
                <h3 class="text-lg font-medium">{{ vm.computes.displayName.value }}</h3>
              </div>

              <!-- 基本信息 -->
              <el-descriptions direction="vertical" :column="1" border>
                <el-descriptions-item label="手机号">
                  {{ vm.computes.maskedMobile.value }}
                </el-descriptions-item>
                <el-descriptions-item label="最后登录">
                  {{ vm.computes.lastLoginTime.value }}
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>

          <!-- 右侧：安全信息 -->
          <el-col :span="16">
            <el-card class="security-card h-full">
              <template #header>
                <div class="card-header">
                  <span>账号信息</span>
                </div>
              </template>

              <el-descriptions direction="vertical" :column="1" border>
                <el-descriptions-item label="账号ID">
                  {{ vm.state.userInfo.id }}
                </el-descriptions-item>
                <el-descriptions-item label="权限等级">
                  {{ vm.computes.permissionText }}
                </el-descriptions-item>
                <el-descriptions-item label="账号状态">
                  <el-tag :type="vm.computes.statusType">
                    {{ vm.computes.statusText }}
                  </el-tag>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </el-col>
        </el-row>

        <!-- 错误提示 -->
        <el-alert
          v-if="vm.state.error"
          :title="vm.state.error"
          type="error"
          show-icon
          class="mt-4"
          @close="vm.actions.clearError"
        />
      </template>
    </el-skeleton>
  </div>
</template>

<script setup lang="ts">
import { useProfilePresenter } from './presenter'

// 视图模型
const vm = useProfilePresenter()
</script>

<style scoped>

.profile-card,
.security-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style> 