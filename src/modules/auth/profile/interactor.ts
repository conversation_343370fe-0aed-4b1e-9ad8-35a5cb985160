import { useAuthStore } from '@/shared/store/authStore'
import type { User } from '@/shared/types/user'
import { ProfileError } from './types/ProfileErrors'

/**
 * 个人中心业务交互器
 */
export class ProfileInteractor {
  constructor(private readonly authStore = useAuthStore()) {}

  /**
   * 获取用户信息
   * @throws {ProfileError} 当获取用户信息失败时抛出
   */
  getUserProfile(): User {
    const userInfo = this.authStore.userInfo
    if (!userInfo) {
      throw ProfileError.updateFailed('用户未登录')
    }
    
    // 验证用户数据
    this.validateUserData(userInfo)
    
    return userInfo
  }

  /**
   * 验证用户数据
   * @throws {ProfileError} 当用户数据无效时抛出
   */
  private validateUserData(user: User): void {
    if (!user.id) {
      throw ProfileError.updateFailed('用户ID不能为空')
    }
    if (!user.name) {
      throw ProfileError.updateFailed('用户名称不能为空')
    }
    if (typeof user.permissionLevel !== 'number') {
      throw ProfileError.updateFailed('用户权限级别无效')
    }
  }
}

/**
 * 个人中心业务交互器工厂函数
 */
export function useProfileInteractor(): ProfileInteractor {
  return new ProfileInteractor()
} 