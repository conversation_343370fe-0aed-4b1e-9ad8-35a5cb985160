/**
 * API错误响应结构
 */
export interface ApiErrorResponse {
  code: number;
  message?: string;
  attachments?: Record<string, string>;
}

/**
 * 错误码枚举
 */
export enum ErrorCode {
  // 成功
  SUCCESS = 0,
  
  // 客户端错误
  PARAM_ERROR = 400,      // 参数错误
  NOT_AUTHORIZED = 401,   // 未授权
  NOT_LOGIN = 402,        // 未登录
  FORBIDDEN = 403,        // 禁止访问
  NOT_FOUND = 404,        // 资源不存在
  TOKEN_EXPIRED = 407,    // 令牌过期
  THROTTLED = 418,        // 请求频率限制
  
  // 服务器错误
  SERVER_ERROR = 500      // 服务器内部错误
}

/**
 * API错误类
 */
export class ApiError extends Error {
  code: number;
  attachments?: Record<string, string>;

  constructor(code: number, message: string, attachments?: Record<string, string>) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.attachments = attachments;
  }

  /**
   * 从响应对象创建ApiError实例
   */
  static fromResponse(response: ApiErrorResponse): ApiError {
    return new ApiError(
      response.code,
      response.message || ApiError.getDefaultMessage(response.code),
      response.attachments
    );
  }

  /**
   * 根据错误码获取默认错误信息
   */
  static getDefaultMessage(code: number): string {
    switch (code) {
      case ErrorCode.PARAM_ERROR:
        return '参数错误';
      case ErrorCode.NOT_AUTHORIZED:
        return '未授权';
      case ErrorCode.NOT_LOGIN:
        return '未登录';
      case ErrorCode.FORBIDDEN:
        return '禁止访问';
      case ErrorCode.NOT_FOUND:
        return '资源不存在';
      case ErrorCode.TOKEN_EXPIRED:
        return '登录已过期，请重新登录';
      case ErrorCode.THROTTLED:
        return '请求过于频繁，请稍后再试';
      case ErrorCode.SERVER_ERROR:
        return '服务器内部错误';
      default:
        return '未知错误';
    }
  }

  /**
   * 判断是否为特定错误码
   */
  is(code: number): boolean {
    return this.code === code;
  }
} 