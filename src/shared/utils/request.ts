import axios from 'axios';
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ElMessage } from 'element-plus';
import { useAuthStore } from '@/shared/store/authStore';
import { ApiError, ErrorCode, type ApiErrorResponse } from '@/shared/types/ApiError';
import { router } from '@/router';

// 响应数据基础结构
export interface ApiResponse<T = any> {
  code: number;
  message?: string;
  data: T;
  attachments?: Record<string, string>;
}

// 创建axios实例
const service: AxiosInstance = axios.create({
  baseURL: import.meta.env.VITE_APP_BASE_API,
  timeout: 10000,
  headers: { 'Content-Type': 'application/json;charset=utf-8' }
});

// 请求拦截器
service.interceptors.request.use((config) => {
  const authStore = useAuthStore();
  if (authStore.token) {
    config.headers.Authorization = `Bearer ${authStore.token}`;
  }
  return config;
});

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    const { code, message, data } = response.data;
    
    if (code === 0) {
      return data;
    }
    
    const error = ApiError.fromResponse({
      code,
      message,
      attachments: response.data.attachments
    } as ApiErrorResponse);

    // 处理TOKEN_EXPIRED错误，自动导航到登录页
    if (error.code === ErrorCode.TOKEN_EXPIRED) {
      const authStore = useAuthStore();
      authStore.logout();
      router.push({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      ElMessage.error(error.message || '登录已过期，请重新登录');
    } else {
      ElMessage.error(error.message);
    }
    
    return Promise.reject(error);
  },
  (error) => {
    const errorMessage = error.response?.data?.message || error.message;
    
    // 处理TOKEN_EXPIRED错误，自动导航到登录页
    if (error.response?.status === ErrorCode.TOKEN_EXPIRED || error.response?.status === ErrorCode.NOT_AUTHORIZED) {
      const authStore = useAuthStore();
      authStore.logout();
      router.replace({
        path: '/login',
        query: { redirect: router.currentRoute.value.fullPath }
      });
      ElMessage.error('登录已过期，请重新登录');
    } else {
      ElMessage.error(errorMessage || '服务器异常');
    }
    
    return Promise.reject(
      new ApiError(
        error.response?.status || 500,
        errorMessage || '服务器异常',
        error.response?.data?.attachments
      )
    );
  }
);

/**
 * 通用GET请求
 * @template T 响应数据类型
 * @param {string} url 请求地址
 * @param {object} params 查询参数
 * @param {AxiosRequestConfig} config 额外配置
 */
export function get<T = any>(
  url: string,
  params?: object,
  config?: AxiosRequestConfig
): Promise<T> {
  return service.get(url, { params, ...config });
}

/**
 * 通用POST请求
 * @template T 响应数据类型
 * @param {string} url 请求地址
 * @param {object} data 请求体数据
 * @param {AxiosRequestConfig} config 额外配置
 */
export function post<T = any>(
  url: string,
  data?: object,
  config?: AxiosRequestConfig
): Promise<T> {
  return service.post(url, data, config);
}

/**
 * 通用请求处理
 * @template T 响应数据类型
 * @param {Promise<ApiResponse<T>>} requestPromise 请求Promise
 */
export async function handleRequest<T>(requestPromise: Promise<ApiResponse<T>>): Promise<T> {
  try {
    const response = await requestPromise;
    if (response.code !== 0) {
      throw new ApiError(response.code, response.message || '请求失败');
    }
    return response.data!;
  } catch (error) {
    if (error instanceof ApiError) {
      console.error(`API Error [${error.code}]: ${error.message}`);
    }
    throw error;
  }
} 