import { defineStore } from 'pinia'
import type { User } from '@/shared/types/user'
import type { BusinessLoginVO } from '@/entities/auth/BusinessLoginVO'

interface AuthState {
  token: string | null
  userInfo: User | null
  permissionLevel: number
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => ({
    token: null,
    userInfo: null,
    permissionLevel: 0
  }),
  
  actions: {
    /**
     * 设置认证令牌
     */
    setToken(token: string | null) {
      this.token = token
    },

    /**
     * 设置用户信息
     */
    setUserInfo(userInfo: User | null) {
      this.userInfo = userInfo
      if (userInfo) {
        this.permissionLevel = userInfo.permissionLevel
      }
    },

    /**
     * 处理登录成功
     */
    handleLoginSuccess(loginVO: BusinessLoginVO) {
      this.setToken(loginVO.token)
      this.setUserInfo({
        id: loginVO.staffId,
        name: loginVO.staffName,
        mobile: '', // 需要从其他接口获取
        permissionLevel: loginVO.permissionLevel
      })
    },

    /**
     * 重置认证状态
     */
    reset() {
      this.token = null
      this.userInfo = null
      this.permissionLevel = 0
    },

    /**
     * 注销处理
     */
    logout() {
      this.reset()
    },

    /**
     * 更新用户信息
     */
    updateUserInfo(user: Partial<User>) {
      if (this.userInfo) {
        this.userInfo = { ...this.userInfo, ...user }
      }
    }
  },

  getters: {
    /** 是否已认证 */
    isAuthenticated: (state): boolean => !!state.token,
    
    /** 用户显示名称 */
    userName: (state): string => state.userInfo?.name || '',
    
    /** 用户ID */
    userId: (state): string => state.userInfo?.id || '',
  },

  persist: true  // 使用默认配置进行持久化
}) 