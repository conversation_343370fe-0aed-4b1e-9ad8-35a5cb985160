<template>
  <div class="app-layout flex h-screen">
    <!-- 侧边导航栏 - 在手机模式下使用抽屉式布局 -->
    <el-drawer
      v-model="layoutState.sidebar.mobileVisible"
      direction="ltr"
      size="80%"
      :with-header="false"
      class="p-0 mobile-drawer"
      v-if="layoutState.screenSize === 'mobile'"
    >
      <SideNavigation 
        :collapsed="false"
        @toggle="toggleSidebar"
      />
    </el-drawer>

    <!-- 桌面/平板模式下的常规侧边栏 -->
    <aside 
      v-if="layoutState.screenSize !== 'mobile'"
      :class="[
        'transition-all duration-300 ease-in-out bg-gray-800 text-white',
        layoutState.sidebar.collapsed ? 'w-16' : 'w-64'
      ]"
    >
      <SideNavigation 
        :collapsed="layoutState.sidebar.collapsed"
        @toggle="toggleSidebar"
      />
    </aside>

    <!-- 主内容区 -->
    <main class="flex-1 flex flex-col overflow-hidden">
      <!-- 全局操作栏 -->
      <GlobalHeader 
        @toggle-mobile-menu="toggleMobileSidebar"
        :screen-size="layoutState.screenSize"
      />
      
      <!-- 内容区域 -->
      <div class="flex-1 overflow-auto p-6 bg-gray-100">
        <slot></slot>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { reactive, onMounted, onUnmounted } from 'vue'
import SideNavigation from './SideNavigation.vue'
import GlobalHeader from './GlobalHeader.vue'
import type { LayoutState } from '@/shared/types/layout'

// 布局状态管理
const layoutState = reactive<LayoutState>({
  sidebar: {
    collapsed: false,
    visible: true,
    mobileVisible: false
  },
  screenSize: 'desktop'
})

// 侧边栏切换
const toggleSidebar = () => {
  layoutState.sidebar.collapsed = !layoutState.sidebar.collapsed
}

// 手机模式下切换侧边栏
const toggleMobileSidebar = () => {
  layoutState.sidebar.mobileVisible = !layoutState.sidebar.mobileVisible
}

// 响应式处理
const handleResize = () => {
  const width = window.innerWidth
  if (width < 768) {
    layoutState.screenSize = 'mobile'
    layoutState.sidebar.mobileVisible = false
  } else if (width < 1280) {
    layoutState.screenSize = 'tablet'
    layoutState.sidebar.collapsed = true
  } else {
    layoutState.screenSize = 'desktop'
    layoutState.sidebar.collapsed = false
  }
}

// 初始执行一次屏幕尺寸检测
onMounted(() => {
  handleResize()
  window.addEventListener('resize', handleResize)
})

// 清理事件监听
onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.app-layout {
  --header-height: 64px;
  --sidebar-width: 256px;
  --sidebar-collapsed-width: 64px;
}

.mobile-drawer :deep(.el-drawer__body) {
  padding: 0;
}
</style> 