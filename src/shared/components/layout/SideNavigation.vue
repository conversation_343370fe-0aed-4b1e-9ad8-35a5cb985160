<template>
  <div class="side-navigation h-full flex flex-col">
    <!-- Logo区域 -->
    <div class="logo-area p-4 border-b border-gray-700 flex items-center justify-between">
      <img 
        :class="['transition-all', collapsed ? 'w-8' : 'w-32']"
        src="@/assets/images/logo.png" 
        alt="系统Logo"
      >
      <button 
        class="text-gray-400 hover:text-white"
        @click="$emit('toggle')"
      >
        <el-icon :size="16">
          <ArrowLeft v-if="!collapsed" />
          <ArrowRight v-else />
        </el-icon>
      </button>
    </div>

    <!-- 导航菜单 -->
    <nav class="flex-1 py-4">
      <el-menu
        :collapse="collapsed"
        background-color="#1F2937"
        text-color="#fff"
        active-text-color="#409EFF"
        :router="true"
        class="mobile-optimized-menu"
      >
        <el-menu-item index="/store/audit">
          <el-icon><Document /></el-icon>
          <template #title>门店管理</template>
        </el-menu-item>

        <el-menu-item index="/store/license">
          <el-icon><Key /></el-icon>
          <template #title>授权管理</template>
        </el-menu-item>

        <el-menu-item index="/store/data-import">
          <el-icon><Upload /></el-icon>
          <template #title>数据导入</template>
        </el-menu-item>

        <el-menu-item index="/exception">
          <el-icon><Warning /></el-icon>
          <template #title>异常处理</template>
        </el-menu-item>

        <el-menu-item index="/appversion/manage">
          <el-icon><Setting /></el-icon>
          <template #title>应用版本管理</template>
        </el-menu-item>
      </el-menu>
    </nav>
  </div>
</template>

<script setup lang="ts">
import { Document, Key, Setting, Warning, ArrowLeft, ArrowRight, Upload } from '@element-plus/icons-vue'
import type { SideNavigationProps } from '@/shared/types/layout'

defineProps<SideNavigationProps>()
defineEmits<{
  (e: 'toggle'): void
}>()
</script>

<style scoped>
.side-navigation :deep(.el-menu) {
  border-right: none;
}

.side-navigation :deep(.el-menu-item) {
  &.is-active {
    background-color: #2D3748;
  }
  
  &:hover {
    background-color: #374151;
  }
}

/* 移动端优化 */
.mobile-optimized-menu :deep(.el-menu-item) {
  height: 56px;
  line-height: 56px;
  padding: 0 20px;
}

@media (max-width: 767px) {
  .side-navigation :deep(.el-menu-item) {
    padding-left: 24px;
    font-size: 16px;
  }
  
  .side-navigation :deep(.el-icon) {
    font-size: 20px;
  }
}
</style> 