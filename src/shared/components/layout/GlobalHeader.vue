<template>
  <header class="global-header h-16 bg-white border-b border-gray-200 px-4 sm:px-6 flex items-center justify-between">
    <!-- 左侧区域：菜单按钮（移动端）或面包屑导航（桌面端） -->
    <div class="flex items-center">
      <!-- 移动端菜单按钮 -->
      <el-button 
        v-if="screenSize === 'mobile'"
        :icon="Menu"
        @click="$emit('toggle-mobile-menu')"
        circle
      />
      
      <!-- 桌面端面包屑 -->
      <el-breadcrumb separator="/" v-else>
        <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>{{ currentRoute }}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <!-- 右侧区域：用户信息、通知、帮助 -->
    <div class="flex items-center space-x-2 sm:space-x-4">
      <!-- 通知按钮 -->
      <el-badge :value="3" class="notification-badge hidden sm:block">
        <el-button 
          type="info" 
          :icon="Bell"
          circle
        />
      </el-badge>

      <!-- 帮助按钮 - 在移动端隐藏 -->
      <el-button 
        type="info" 
        :icon="QuestionFilled"
        circle
        class="hidden sm:block"
      />

      <!-- 用户信息下拉菜单 -->
      <el-dropdown trigger="click" @command="handleCommand">
        <div class="flex items-center cursor-pointer">
          <el-avatar 
            size="small" 
            src="https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png"
          />
          <span class="ml-2 text-gray-700 hidden sm:inline">{{ authStore.userName || '管理员' }}</span>
        </div>
        
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">
              <el-icon><User /></el-icon>
              个人中心
            </el-dropdown-item>
            <el-dropdown-item divided command="logout">
              <el-icon><SwitchButton /></el-icon>
              退出登录
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </header>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/shared/store/authStore'
import { 
  Bell,
  QuestionFilled,
  User,
  SwitchButton,
  Menu
} from '@element-plus/icons-vue'
import type { GlobalHeaderProps } from '@/shared/types/layout'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 接收父组件传递的屏幕尺寸
defineProps<GlobalHeaderProps>()

// 定义事件
defineEmits<{
  (e: 'toggle-mobile-menu'): void
}>()

const currentRoute = computed(() => {
  return route.meta.title || route.name || '未知页面'
})

// 处理下拉菜单命令
const handleCommand = (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'logout':
      authStore.logout()
      router.push('/login')
      break
  }
}
</script>

<style scoped>
.notification-badge :deep(.el-badge__content) {
  z-index: 1;
}

/* 移动端样式调整 */
@media (max-width: 640px) {
  .global-header {
    padding: 0 0.75rem;
  }
}
</style> 