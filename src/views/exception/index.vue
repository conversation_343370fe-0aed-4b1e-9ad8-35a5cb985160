<template>
  <div class="exception-page">
    <div class="page-header">
      <h1 class="page-title">异常处理</h1>
      <p class="page-description">对房间状态进行强制关房操作</p>
    </div>
    
    <div class="page-content">
      <el-card class="operation-card">
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>强制关房操作</span>
          </div>
        </template>

        <el-form 
          ref="formRef" 
          :model="form" 
          :rules="rules" 
          label-width="120px"
          class="exception-form"
        >
          <!-- KTV选择 -->
          <el-form-item label="选择KTV" prop="selectedKtv">
            <el-select
              v-model="form.selectedKtv"
              placeholder="请选择KTV"
              @change="onKtvChange"
              :loading="ktvLoading"
              filterable
              clearable
              class="w-full"
            >
              <el-option
                v-for="ktv in ktvList"
                :key="ktv.id"
                :label="ktv.name"
                :value="ktv.id"
              />
            </el-select>
          </el-form-item>

          <!-- 房间选择 -->
          <el-form-item label="选择房间" prop="selectedRoom">
            <el-select
              v-model="form.selectedRoom"
              placeholder="请先选择KTV"
              :disabled="!form.selectedKtv"
              :loading="roomLoading"
              @change="onRoomChange"
              filterable
              clearable
              class="w-full"
            >
              <el-option
                v-for="room in roomList"
                :key="room.id"
                :label="`${room.name} (${room.status})`"
                :value="room.id"
              >
                <div class="flex items-center justify-between">
                  <span>{{ room.name }}</span>
                  <el-tag 
                    :color="getRoomStatusColor(getOriginalStatus(room.status))" 
                    size="small"
                    style="color: white; border: none;"
                  >
                    {{ room.status }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>

          <!-- 手动输入确认 -->
          <el-divider content-position="left">
            <span class="text-orange-500">安全确认</span>
          </el-divider>

          <el-form-item label="确认KTV名称" prop="confirmKtvName">
            <el-input
              v-model="form.confirmKtvName"
              placeholder="请手动输入KTV名称进行确认"
              :disabled="!selectedKtvInfo"
            />
            <div class="form-hint" v-if="selectedKtvInfo">
              请输入：{{ selectedKtvInfo.originalName || selectedKtvInfo.name }}
            </div>
          </el-form-item>

          <el-form-item label="确认房间名称" prop="confirmRoomName">
            <el-input
              v-model="form.confirmRoomName"
              placeholder="请手动输入房间名称进行确认"
              :disabled="!selectedRoomInfo"
            />
            <div class="form-hint" v-if="selectedRoomInfo">
              请输入：{{ selectedRoomInfo.name }}
            </div>
          </el-form-item>

          <!-- 备注字段 -->
          <el-form-item label="关闭原因" prop="exceptionDescription">
            <el-input
              v-model="form.exceptionDescription"
              type="textarea"
              :rows="3"
              placeholder="请详细描述关闭原因，如：设备故障、客户投诉、安全隐患等"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>

          <!-- 操作按钮 -->
          <el-form-item>
            <el-button 
              type="danger" 
              :loading="submitLoading"
              @click="handleForceCloseRoom"
              :disabled="!canSubmit"
            >
              <el-icon><Close /></el-icon>
              强制关房
            </el-button>
            <el-button @click="resetForm">
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 操作记录 -->
      <el-card class="mt-6">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>最近操作记录</span>
          </div>
        </template>

        <el-table :data="operationHistory" stripe v-loading="historyLoading">
          <el-table-column 
            type="index" 
            label="序号" 
            width="80"
            :index="(index: number) => (pagination.currentPage - 1) * pagination.pageSize + index + 1"
          />
          <el-table-column prop="exceptionTime" label="异常时间" width="180" />
          <el-table-column prop="ktvName" label="KTV名称" />
          <el-table-column prop="roomName" label="房间名称" />
          <el-table-column prop="operatorName" label="操作人" />
          <el-table-column prop="exceptionDescription" label="关闭原因" />
          <el-table-column prop="exceptionType" label="类型" />
        </el-table>

        <!-- 分页组件 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.currentPage"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[20, 50, 100, 200]"
            :total="pagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handlePageChange"
          />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { Warning, Close, Document } from '@element-plus/icons-vue'
import * as exceptionApi from '@/api/exception'
import type { KtvEntity, RoomEntity, OperationHistoryEntity } from '@/entities/exception'

// 视图模型类型定义（用于UI展示）
interface KtvInfo {
  id: string
  name: string
  originalName?: string  // 保存原始名称用于确认
  address?: string
}

interface RoomInfo {
  id: string
  name: string
  status: string
  ktvId: string
}

interface OperationRecord {
  exceptionTime: string
  ktvName: string
  roomName: string
  operatorName: string
  exceptionDescription: string  // 关闭原因
  exceptionType: string         // 类型
}

// 响应式数据
const formRef = ref<FormInstance>()
const ktvLoading = ref(false)
const roomLoading = ref(false)
const submitLoading = ref(false)

const form = reactive({
  selectedKtv: '',
  selectedRoom: '',
  confirmKtvName: '',
  confirmRoomName: '',
  exceptionDescription: ''  // 备注字段
})

const ktvList = ref<KtvInfo[]>([])
const roomList = ref<RoomInfo[]>([])
const operationHistory = ref<OperationRecord[]>([])

// 分页相关数据
const pagination = reactive({
  currentPage: 1,
  pageSize: 50,
  total: 0
})
const historyLoading = ref(false)

// 计算属性
const selectedKtvInfo = computed(() => {
  return ktvList.value.find(ktv => ktv.id === form.selectedKtv)
})

const selectedRoomInfo = computed(() => {
  return roomList.value.find(room => room.id === form.selectedRoom)
})

const canSubmit = computed(() => {
  const expectedKtvName = selectedKtvInfo.value?.originalName || selectedKtvInfo.value?.name
  return form.selectedKtv && 
         form.selectedRoom && 
         form.confirmKtvName === expectedKtvName &&
         form.confirmRoomName === selectedRoomInfo.value?.name
})

// 表单验证规则
const rules: FormRules = {
  selectedKtv: [
    { required: true, message: '请选择KTV', trigger: 'change' }
  ],
  selectedRoom: [
    { required: true, message: '请选择房间', trigger: 'change' }
  ],
  confirmKtvName: [
    { required: true, message: '请输入KTV名称确认', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        const expectedName = selectedKtvInfo.value?.originalName || selectedKtvInfo.value?.name
        if (value !== expectedName) {
          callback(new Error('输入的KTV名称不正确'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  confirmRoomName: [
    { required: true, message: '请输入房间名称确认', trigger: 'blur' },
    {
      validator: (rule, value, callback) => {
        if (value !== selectedRoomInfo.value?.name) {
          callback(new Error('输入的房间名称不正确'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ],
  exceptionDescription: [
    { required: true, message: '请输入关闭原因', trigger: 'blur' },
    { min: 2, max: 200, message: '关闭原因长度在 2 到 200 个字符', trigger: 'blur' }
  ]
}

// 数据转换函数
const convertKtvEntityToInfo = (entity: KtvEntity): KtvInfo => {
  // 拼接联系信息到名称后面
  let displayName = entity.name
  const contactInfo = []
  
  if (entity.contact) {
    contactInfo.push(`联系人: ${entity.contact}`)
  }
  
  if (entity.contactPhone) {
    contactInfo.push(`电话: ${entity.contactPhone}`)
  }
  
  if (contactInfo.length > 0) {
    displayName = `${entity.name} (${contactInfo.join(', ')})`
  }
  
  return {
    id: entity.id,
    name: displayName,
    originalName: entity.name,
    address: entity.address
  }
}

// 房间状态转换函数
const getRoomStatusText = (status: string): string => {
  const statusMap: Record<string, string> = {
    'idle': '空闲',
    'in_use': '使用中',
    'fault': '故障',
    'cleaning': '清扫中',
    'with_guest': '带客'
  }
  return statusMap[status] || status
}

// 获取房间状态颜色
const getRoomStatusColor = (status: string): string => {
  const colorMap: Record<string, string> = {
    'idle': '#67C23A',      // 绿色 - 空闲
    'in_use': '#E6A23C',    // 橙色 - 使用中
    'fault': '#F56C6C',     // 红色 - 故障
    'cleaning': '#909399',  // 灰色 - 清扫中
    'with_guest': '#409EFF' // 蓝色 - 带客
  }
  return colorMap[status] || '#909399'
}

// 根据中文状态获取原始状态码
const getOriginalStatus = (chineseStatus: string): string => {
  const reverseMap: Record<string, string> = {
    '空闲': 'idle',
    '使用中': 'in_use',
    '故障': 'fault',
    '清扫中': 'cleaning',
    '带客': 'with_guest'
  }
  return reverseMap[chineseStatus] || chineseStatus
}

// 时间戳转换函数 - 中国时区
const formatTimestamp = (timestamp: string | number): string => {
  try {
    let timestampNum = Number(timestamp)
    if (isNaN(timestampNum)) {
      return String(timestamp) // 如果转换失败，返回原始值
    }
    
    // 自动检测时间戳格式：如果小于等于10位数字，认为是秒级时间戳，需要转换为毫秒
    if (timestampNum.toString().length <= 10) {
      timestampNum = timestampNum * 1000 // 秒级转毫秒级
    }
    
    const date = new Date(timestampNum)
    if (isNaN(date.getTime())) {
      return String(timestamp) // 如果转换失败，返回原始值
    }
    
    // 确保使用中国时区显示
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false,
      timeZone: 'Asia/Shanghai'  // 明确指定中国时区
    })
  } catch (error) {
    console.error('时间戳转换失败:', error)
    return String(timestamp)
  }
}

const convertRoomEntityToInfo = (entity: RoomEntity): RoomInfo => ({
  id: entity.id,
  name: entity.name,
  status: getRoomStatusText(entity.status),
  ktvId: entity.venueId
})

const convertOperationHistoryToRecord = (entity: OperationHistoryEntity): OperationRecord => ({
  exceptionTime: formatTimestamp(entity.exceptionTime),
  ktvName: entity.venueName,
  roomName: entity.roomName,
  operatorName: entity.operatorName,
  exceptionDescription: entity.exceptionDescription || '',
  exceptionType: entity.exceptionType || ''
})

// 方法
const loadKtvList = async () => {
  ktvLoading.value = true
  try {
    // 加载所有已审核通过的KTV
    const response = await exceptionApi.getKtvList({
      auditStatus: 1,  // 只获取审核状态为1的KTV
      pageNum: 1,
      pageSize: 1000  // 设置一个较大的数值来获取所有KTV
    })
    ktvList.value = response.data.map(convertKtvEntityToInfo)
    console.log(`成功加载 ${ktvList.value.length} 个已审核通过的KTV`)
  } catch (error) {
    console.error('加载KTV列表失败:', error)
    ElMessage.error('加载KTV列表失败')
    
    // 使用模拟数据作为降级方案
    await new Promise(resolve => setTimeout(resolve, 1000))
    ktvList.value = [
      { id: '1', name: '欢乐颂KTV (联系人: 张经理, 电话: 138-0013-8001)', originalName: '欢乐颂KTV', address: '北京市朝阳区' },
      { id: '2', name: '麦霸KTV (联系人: 李经理, 电话: 138-0013-8002)', originalName: '麦霸KTV', address: '北京市海淀区' },
      { id: '3', name: '星光大道KTV (联系人: 王经理, 电话: 138-0013-8003)', originalName: '星光大道KTV', address: '北京市西城区' },
      { id: '4', name: '金嗓子KTV (联系人: 赵经理, 电话: 138-0013-8004)', originalName: '金嗓子KTV', address: '北京市东城区' },
      { id: '5', name: '好乐迪KTV (联系人: 刘经理, 电话: 138-0013-8005)', originalName: '好乐迪KTV', address: '北京市丰台区' }
    ]
    console.log(`使用模拟数据，加载 ${ktvList.value.length} 个KTV`)
  } finally {
    ktvLoading.value = false
  }
}

const loadRoomList = async (ktvId: string) => {
  roomLoading.value = true
  try {
    // 加载该KTV的所有房间
    const response = await exceptionApi.getRoomList(ktvId, {
      pageNum: 1,
      pageSize: 1000  // 设置一个较大的数值来获取所有房间
    })
    roomList.value = response.data.map(convertRoomEntityToInfo)
    console.log(`成功加载KTV(${ktvId})的 ${roomList.value.length} 个房间`)
  } catch (error) {
    console.error('加载房间列表失败:', error)
    ElMessage.error('加载房间列表失败')
    
    // 使用模拟数据作为降级方案
    await new Promise(resolve => setTimeout(resolve, 800))
    roomList.value = [
      { id: '101', name: '豪华包间A', status: '使用中', ktvId },
      { id: '102', name: '豪华包间B', status: '空闲', ktvId },
      { id: '103', name: 'VIP包间', status: '使用中', ktvId },
      { id: '104', name: '普通包间1', status: '故障', ktvId },
      { id: '105', name: '普通包间2', status: '空闲', ktvId },
      { id: '106', name: '小包间1', status: '带客', ktvId },
      { id: '107', name: '小包间2', status: '清扫中', ktvId },
      { id: '108', name: '中包间1', status: '故障', ktvId }
    ]
    console.log(`使用模拟数据，加载KTV(${ktvId})的 ${roomList.value.length} 个房间`)
  } finally {
    roomLoading.value = false
  }
}

const loadOperationHistory = async (page: number = 1) => {
  historyLoading.value = true
  try {
    const response = await exceptionApi.getOperationHistory({ 
      pageNum: page, 
      pageSize: pagination.pageSize 
    })
    operationHistory.value = response.data.map(convertOperationHistoryToRecord)
    pagination.total = response.total
    pagination.currentPage = page
  } catch (error) {
    console.error('加载操作记录失败:', error)
    ElMessage.error('加载操作记录失败')
    
    // 使用模拟数据
    const mockTotal = 156 // 模拟总数
    const mockData = [
      {
        exceptionTime: formatTimestamp(1748422325),
        ktvName: '上地',
        roomName: 'C01',
        operatorName: '贾磊',
        exceptionDescription: '设备故障，无法正常使用',
        exceptionType: '设备异常'
      },
      {
        exceptionTime: formatTimestamp(1705300510),
        ktvName: '麦霸KTV',
        roomName: 'VIP包间',
        operatorName: '管理员',
        exceptionDescription: '客户投诉，强制关房处理',
        exceptionType: '客户投诉'
      },
      {
        exceptionTime: formatTimestamp(1705200400),
        ktvName: '星光大道KTV',
        roomName: '豪华包间A',
        operatorName: '值班经理',
        exceptionDescription: '音响设备损坏，影响正常使用',
        exceptionType: '设备异常'
      },
      {
        exceptionTime: formatTimestamp(1705100300),
        ktvName: '金嗓子KTV',
        roomName: '中包间2',
        operatorName: '技术员',
        exceptionDescription: '空调故障，温度过高',
        exceptionType: '环境异常'
      },
      {
        exceptionTime: formatTimestamp(1705000200),
        ktvName: '好乐迪KTV',
        roomName: '小包间5',
        operatorName: '前台',
        exceptionDescription: '客户醉酒闹事，安全考虑',
        exceptionType: '安全问题'
      }
    ]
    
    // 模拟分页数据
    const startIndex = (page - 1) * pagination.pageSize
    const endIndex = startIndex + pagination.pageSize
    operationHistory.value = mockData.slice(startIndex, endIndex)
    pagination.total = mockTotal
    pagination.currentPage = page
  } finally {
    historyLoading.value = false
  }
}

const onKtvChange = (ktvId: string) => {
  form.selectedRoom = ''
  form.confirmKtvName = ''
  form.confirmRoomName = ''
  form.exceptionDescription = ''
  roomList.value = []
  
  if (ktvId) {
    loadRoomList(ktvId)
  }
}

const onRoomChange = () => {
  form.confirmKtvName = ''
  form.confirmRoomName = ''
  form.exceptionDescription = ''
}

const handleForceCloseRoom = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    const confirmText = `确认要强制关闭以下房间吗？\n\nKTV：${selectedKtvInfo.value?.originalName || selectedKtvInfo.value?.name}\n房间：${selectedRoomInfo.value?.name}\n\n此操作不可撤销！`
    
    await ElMessageBox.confirm(confirmText, '危险操作确认', {
      confirmButtonText: '确认强制关房',
      cancelButtonText: '取消',
      type: 'warning',
      confirmButtonClass: 'el-button--danger'
    })
    
    submitLoading.value = true
    
    try {
      await exceptionApi.forceCloseRoom({
        venueId: form.selectedKtv,
        roomId: form.selectedRoom,
        venueName: selectedKtvInfo.value?.originalName || selectedKtvInfo.value?.name || '',
        roomName: selectedRoomInfo.value?.name || '',
        reason: '强制关房操作',
        exceptionDescription: form.exceptionDescription
      })
    } catch (apiError) {
      console.error('强制关房API调用失败:', apiError)
      // 模拟API调用作为降级方案
      await new Promise(resolve => setTimeout(resolve, 2000))
    }
    
    ElMessage.success('强制关房操作成功')
    
    // 重置表单并刷新数据
    resetForm()
    loadOperationHistory(pagination.currentPage)
    
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('强制关房操作失败')
    }
  } finally {
    submitLoading.value = false
  }
}

const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  roomList.value = []
}

// 分页处理函数
const handlePageChange = (page: number) => {
  loadOperationHistory(page)
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
  loadOperationHistory(1)
}

// 生命周期
onMounted(() => {
  loadKtvList()
  loadOperationHistory()
})
</script>

<style scoped>
.exception-page {
  @apply min-h-full;
}

.page-header {
  @apply p-6 bg-white rounded-lg shadow-sm mb-6;
}

.page-title {
  @apply text-2xl font-bold text-gray-900 mb-2;
}

.page-description {
  @apply text-gray-600;
}

.page-content {
  @apply space-y-6;
}

.operation-card {
  @apply shadow-sm;
}

.card-header {
  @apply flex items-center gap-2 text-lg font-medium;
}

.exception-form {
  @apply max-w-2xl;
}

.form-hint {
  @apply text-sm text-blue-600 mt-1;
}

:deep(.el-divider__text) {
  @apply font-medium;
}

:deep(.el-button--danger) {
  @apply bg-red-500 border-red-500 hover:bg-red-600 hover:border-red-600;
}

.pagination-container {
  @apply flex justify-center mt-4;
}
</style> 