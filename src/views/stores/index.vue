<template>
  <div class="page-container">
    <!-- 搜索栏 -->
    <el-card class="mb-4">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="门店名称">
          <el-input v-model="searchForm.name" placeholder="请输入门店名称" />
        </el-form-item>
        <el-form-item label="门店状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option label="正常营业" value="active" />
            <el-option label="暂停营业" value="suspended" />
            <el-option label="已关闭" value="closed" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span>门店列表</span>
          <el-button type="primary" @click="handleAdd">
            <el-icon><Plus /></el-icon>新增门店
          </el-button>
        </div>
      </template>

      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="门店ID" width="100" />
        <el-table-column prop="name" label="门店名称" />
        <el-table-column prop="address" label="门店地址" />
        <el-table-column prop="manager" label="店长" />
        <el-table-column prop="phone" label="联系电话" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">编辑</el-button>
            <el-button type="primary" link @click="handleDetail(row)">详情</el-button>
            <el-button type="danger" link @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Search, Plus } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const searchForm = ref({
  name: '',
  status: ''
})

const tableData = ref([
  {
    id: '001',
    name: '北京朝阳店',
    address: '北京市朝阳区xxx街道',
    manager: '张三',
    phone: '13800138000',
    status: '正常营业'
  },
  // 更多测试数据...
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const getStatusType = (status) => {
  const map = {
    '正常营业': 'success',
    '暂停营业': 'warning',
    '已关闭': 'danger'
  }
  return map[status] || 'info'
}

const handleSearch = () => {
  // 实现搜索逻辑
}

const resetSearch = () => {
  searchForm.value = {
    name: '',
    status: ''
  }
}

const handleAdd = () => {
  // 实现新增逻辑
}

const handleEdit = (row) => {
  // 实现编辑逻辑
}

const handleDetail = (row) => {
  // 实现查看详情逻辑
}

const handleDelete = (row) => {
  ElMessageBox.confirm(
    `确定要删除门店"${row.name}"吗？`,
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 实现删除逻辑
    ElMessage.success('删除成功')
  })
}

const handleSizeChange = (val) => {
  pageSize.value = val
  // 重新加载数据
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 重新加载数据
}
</script>
