```vue
<template>
  <div class="page-container">
    <!-- 搜索栏 -->
    <el-card class="mb-4">
      <el-form :inline="true" :model="searchForm">
        <el-form-item label="申请编号">
          <el-input v-model="searchForm.id" placeholder="请输入申请编号" />
        </el-form-item>
        <el-form-item label="门店名称">
          <el-input v-model="searchForm.storeName" placeholder="请输入门店名称" />
        </el-form-item>
        <el-form-item label="申请状态">
          <el-select v-model="searchForm.status" placeholder="请选择状态">
            <el-option label="全部" value="" />
            <el-option label="待审核" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>搜索
          </el-button>
          <el-button @click="resetSearch">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 数据表格 -->
    <el-card>
      <el-table :data="tableData" border style="width: 100%">
        <el-table-column prop="id" label="申请编号" width="120" />
        <el-table-column prop="storeName" label="门店名称" />
        <el-table-column prop="type" label="申请类型" />
        <el-table-column prop="applicant" label="申请人" />
        <el-table-column prop="createTime" label="申请时间" />
        <el-table-column prop="status" label="状态">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              link 
              @click="handleReview(row)"
              v-if="row.status === '待审核'"
            >
              审核
            </el-button>
            <el-button type="primary" link @click="handleDetail(row)">
              详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="flex justify-end mt-4">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog
      v-model="reviewDialogVisible"
      title="申请审核"
      width="500px"
    >
      <el-form :model="reviewForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="reviewForm.result">
            <el-radio label="approved">通过</el-radio>
            <el-radio label="rejected">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核意见">
          <el-input
            v-model="reviewForm.comment"
            type="textarea"
            rows="4"
            placeholder="请输入审核意见"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="reviewDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitReview">
            确认
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

const searchForm = ref({
  id: '',
  storeName: '',
  status: '',
  dateRange: []
})

const tableData = ref([
  {
    id: 'AP001',
    storeName: '北京朝阳店',
    type: '开店申请',
    applicant: '张三',
    createTime: '2024-03-20 10:30:00',
    status: '待审核'
  },
  // 更多测试数据...
])

const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(100)

const reviewDialogVisible = ref(false)
const reviewForm = ref({
  result: 'approved',
  comment: ''
})

const getStatusType = (status) => {
  const map = {
    '待审核': 'warning',
    '已通过': 'success',
    '已拒绝': 'danger'
  }
  return map[status] || 'info'
}

const handleSearch = () => {
  // 实现搜索逻辑
}

const resetSearch = () => {
  searchForm.value = {
    id: '',
    storeName: '',
    status: '',
    dateRange: []
  }
}

const handleReview = (row) => {
  reviewDialogVisible.value = true
  // 可以设置当前审核的申请信息
}

const handleDetail = (row) => {
  // 实现查看详情逻辑
}

const submitReview = () => {
  // 实现提交审核逻辑
  ElMessage.success('审核提交成功')
  reviewDialogVisible.value = false
}

const handleSizeChange = (val) => {
  pageSize.value = val
  // 重新加载数据
}

const handleCurrentChange = (val) => {
  currentPage.value = val
  // 重新加载数据
}
</script>
```