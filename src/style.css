@tailwind base;
@tailwind components;
@tailwind utilities;

/* 自定义全局样式 */
@layer components {
  .sidebar-item {
    @apply flex items-center px-4 py-2 text-gray-600 hover:bg-gray-100 hover:text-primary;
  }
  
  .page-container {
    @apply p-6 bg-gray-50 min-h-screen;
  }

  /* 登录页样式 */
  .login-page {
    @apply min-h-screen flex bg-gray-50;
  }

  .brand-section {
    @apply hidden lg:flex flex-col items-center justify-center bg-gradient-to-br from-blue-600 to-indigo-800 text-white p-8;
  }

  .login-section {
    @apply flex-1 flex items-center justify-center p-8;
  }

  .login-container {
    @apply bg-white rounded-lg shadow-lg p-8 w-full max-w-md;
  }

  .form-header {
    @apply text-2xl font-bold text-center text-gray-800 mb-8;
  }

  .form-footer {
    @apply mt-8 text-center text-sm text-gray-500;
  }
}

/* Element Plus 样式覆盖 */
.el-button--primary {
  @apply bg-blue-500 border-blue-500 hover:bg-blue-600 hover:border-blue-600;
}


.el-form-item__label {
  @apply font-medium;
}

/* 动画效果 */
.fade-enter-active,
.fade-leave-active {
  @apply transition-opacity duration-300;
}

.fade-enter-from,
.fade-leave-to {
  @apply opacity-0;
}
