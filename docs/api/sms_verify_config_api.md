# 短信验证配置接口文档

## 概述

短信验证配置接口用于管理门店的短信验证开关，支持取酒短信验证和会员消费短信验证等功能。

## 基础信息

- **协议**: HTTP
- **请求方式**: POST
- **数据格式**: JSON
- **字符编码**: UTF-8
- **基础URL**: `http://127.0.0.1:18501`

## 接口列表

### 1. 获取短信验证配置

**接口地址**: `/api/smsVerifyConfig/get`

**请求参数**:
```json
{
  "venueId": "35RYMWLmBa",           // 门店ID（必填）
  "configKey": "wine_withdrawal_verify"  // 配置键名（可选，为空时获取所有配置）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": {
    "list": [
      {
        "id": "de18815d9f1346cb8de5e3619c0c4fe5",
        "venueId": "35RYMWLmBa",
        "configKey": "wine_withdrawal_verify",
        "enabled": 1,
        "description": "取酒短信验证开关",
        "remark": "",
        "ctime": 1753867470,
        "utime": 1753867470,
        "state": 0,
        "version": 0
      }
    ]
  },
  "requestID": "dda0fa22-b306-4acd-acdc-31a862caa840",
  "serverTime": 1753868114
}
```

### 2. 更新单个短信验证配置

**接口地址**: `/api/smsVerifyConfig/update`

**请求参数**:
```json
{
  "venueId": "35RYMWLmBa",                    // 门店ID（必填）
  "configKey": "wine_withdrawal_verify",      // 配置键名（必填）
  "enabled": 1,                               // 是否启用：0-禁用，1-启用（可选）
  "description": "取酒短信验证开关",            // 配置描述（可选）
  "remark": "备注信息"                         // 备注（可选）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": "配置更新成功",
  "requestID": "a0081221-6e6e-489a-af98-65d38dc669cc",
  "serverTime": 1753867470
}
```

### 3. 批量更新短信验证配置

**接口地址**: `/api/smsVerifyConfig/batchUpdate`

**请求参数**:
```json
{
  "venueId": "35RYMWLmBa",
  "configs": [
    {
      "venueId": "35RYMWLmBa",
      "configKey": "wine_withdrawal_verify",
      "enabled": 1,
      "description": "取酒短信验证开关-已启用"
    },
    {
      "venueId": "35RYMWLmBa",
      "configKey": "member_consume_verify",
      "enabled": 1,
      "description": "会员消费短信验证开关-已启用"
    }
  ]
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": "批量配置更新成功",
  "requestID": "4d041c6d-b006-49d7-974d-b92622477ca7",
  "serverTime": 1753868922
}
```

### 4. 删除短信验证配置

**接口地址**: `/api/smsVerifyConfig/delete`

**请求参数**:
```json
{
  "configId": "de18815d9f1346cb8de5e3619c0c4fe5"  // 配置ID（必填）
}
```

**响应示例**:
```json
{
  "code": 0,
  "message": "success",
  "data": "短信验证配置删除成功",
  "requestID": "1eba8ff1-5385-40dc-9a0c-52b3485740c4",
  "serverTime": 1753866981
}
```

## 配置键名常量

| 配置键名 | 说明 | 示例值 |
|---------|------|--------|
| `wine_withdrawal_verify` | 取酒短信验证 | `wine_withdrawal_verify` |
| `member_consume_verify` | 会员消费短信验证 | `member_consume_verify` |

## 字段说明

### 请求字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| venueId | string | 是 | 门店ID |
| configKey | string | 是 | 配置键名 |
| enabled | int | 否 | 是否启用：0-禁用，1-启用 |
| description | string | 否 | 配置描述 |
| remark | string | 否 | 备注信息 |
| configId | string | 是 | 配置ID（删除时使用） |

### 响应字段

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | string | 配置ID |
| venueId | string | 门店ID |
| configKey | string | 配置键名 |
| enabled | int | 是否启用：0-禁用，1-启用 |
| description | string | 配置描述 |
| remark | string | 备注 |
| ctime | int64 | 创建时间 |
| utime | int64 | 更新时间 |
| state | int | 状态 |
| version | int | 版本 |

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 0 | 成功 |
| 400 | 参数错误 |
| 500 | 服务器内部错误 |

## 使用示例

### JavaScript/TypeScript 示例

```javascript
// 获取所有配置
async function getConfigs(venueId) {
  const response = await fetch('/api/smsVerifyConfig/get', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      venueId: venueId
    })
  });
  return await response.json();
}

// 更新单个配置
async function updateConfig(venueId, configKey, enabled, description) {
  const response = await fetch('/api/smsVerifyConfig/update', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      venueId: venueId,
      configKey: configKey,
      enabled: enabled,
      description: description
    })
  });
  return await response.json();
}

// 批量更新配置
async function batchUpdateConfigs(venueId, configs) {
  const response = await fetch('/api/smsVerifyConfig/batchUpdate', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      venueId: venueId,
      configs: configs
    })
  });
  return await response.json();
}

// 删除配置
async function deleteConfig(configId) {
  const response = await fetch('/api/smsVerifyConfig/delete', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      configId: configId
    })
  });
  return await response.json();
}
```

### cURL 示例

```bash
# 获取所有配置
curl 'http://127.0.0.1:18501/api/smsVerifyConfig/get' \
  -H 'Content-Type: application/json' \
  --data-raw '{"venueId":"35RYMWLmBa"}'

# 更新单个配置
curl 'http://127.0.0.1:18501/api/smsVerifyConfig/update' \
  -H 'Content-Type: application/json' \
  --data-raw '{"venueId":"35RYMWLmBa","configKey":"wine_withdrawal_verify","enabled":1,"description":"取酒短信验证开关"}'

# 批量更新配置
curl 'http://127.0.0.1:18501/api/smsVerifyConfig/batchUpdate' \
  -H 'Content-Type: application/json' \
  --data-raw '{"venueId":"35RYMWLmBa","configs":[{"venueId":"35RYMWLmBa","configKey":"wine_withdrawal_verify","enabled":1,"description":"取酒短信验证开关"},{"venueId":"35RYMWLmBa","configKey":"member_consume_verify","enabled":1,"description":"会员消费短信验证开关"}]}'

# 删除配置
curl 'http://127.0.0.1:18501/api/smsVerifyConfig/delete' \
  -H 'Content-Type: application/json' \
  --data-raw '{"configId":"de18815d9f1346cb8de5e3619c0c4fe5"}'
```

## 注意事项

1. **门店ID一致性**: 批量更新时，所有配置的门店ID必须一致
2. **事务保证**: 批量更新使用数据库事务，确保原子性
3. **配置键名**: 使用预定义的配置键名常量
4. **启用状态**: enabled字段为0表示禁用，1表示启用
5. **时间戳**: 所有时间戳均为Unix时间戳（秒）
