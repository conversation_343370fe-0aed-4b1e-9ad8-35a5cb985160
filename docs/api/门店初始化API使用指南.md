# 门店初始化API使用指南

## 概述

门店数据初始化框架提供了一套完整的商务后台API接口，用于管理门店功能的批量初始化、状态查询、进度监控等操作。

## API接口列表

### 1. 批量初始化功能

**接口地址：** `POST /api/business/venue/batch-init-feature`

**功能描述：** 为所有门店批量初始化指定功能

**请求参数：**
```json
{
    "featureName": "print_template"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "taskId": "1234567890123456789"
    }
}
```

### 2. 查询初始化状态

**接口地址：** `POST /api/business/venue/init-status`

**功能描述：** 查询门店功能初始化状态列表

**请求参数：**
```json
{
    "venueId": "venue_001",          // 可选，门店ID
    "featureName": "print_template", // 可选，功能名称
    "status": "completed",           // 可选，状态：pending/running/completed/failed
    "pageNum": 1,                    // 可选，页码，默认1
    "pageSize": 20                   // 可选，每页条数，默认20
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": "init_001",
                "venueId": "venue_001",
                "venueName": "测试门店",
                "featureName": "print_template",
                "version": "1.0.0",
                "status": "completed",
                "startTime": 1640995200000,
                "endTime": 1640995260000,
                "duration": 60000,
                "operator": "admin",
                "ctime": 1640995200000,
                "utime": 1640995260000
            }
        ],
        "total": 1
    }
}
```

### 3. 查询初始化进度

**接口地址：** `POST /api/business/system/init-progress`

**功能描述：** 查询系统级别的初始化任务进度

**请求参数：**
```json
{
    "taskId": "1234567890123456789", // 可选，任务ID
    "featureName": "print_template", // 可选，功能名称
    "status": "running",             // 可选，状态：pending/running/completed/failed
    "pageNum": 1,                    // 可选，页码，默认1
    "pageSize": 20                   // 可选，每页条数，默认20
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": {
        "tasks": [
            {
                "taskId": "1234567890123456789",
                "featureName": "print_template",
                "version": "1.0.0",
                "status": "completed",
                "totalVenues": 100,
                "successCount": 95,
                "failedCount": 5,
                "pendingCount": 0,
                "progress": 100,
                "startTime": 1640995200000,
                "endTime": 1640995800000,
                "duration": 600000,
                "operator": "admin",
                "createdAt": 1640995200000,
                "updatedAt": 1640995800000
            }
        ],
        "total": 1
    }
}
```

### 4. 重试失败的初始化

**接口地址：** `POST /api/business/system/retry-failed-init`

**功能描述：** 重试指定门店的失败初始化任务

**请求参数：**
```json
{
    "venueId": "venue_001",
    "featureName": "print_template"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": "重试成功"
}
```

### 5. 初始化新门店（内部接口）

**接口地址：** `POST /api/business/venue/init-new`

**功能描述：** 为新创建的门店初始化所有必需功能

**请求参数：**
```json
{
    "venueId": "venue_001"
}
```

**响应示例：**
```json
{
    "code": 200,
    "msg": "success",
    "data": "初始化成功"
}
```

## 状态说明

### 初始化状态
- `pending`: 待处理
- `running`: 运行中
- `completed`: 已完成
- `failed`: 失败

### 任务状态
- `PENDING`: 待处理
- `RUNNING`: 运行中
- `COMPLETED`: 已完成
- `FAILED`: 失败
- `CANCELLED`: 已取消

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 权限不足 |
| 500 | 服务器错误 |

## 使用示例

### 批量初始化打印模板功能

```bash
curl -X POST "http://localhost:8080/api/business/venue/batch-init-feature" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "featureName": "print_template"
  }'
```

### 查询初始化进度

```bash
curl -X POST "http://localhost:8080/api/business/system/init-progress" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -d '{
    "featureName": "print_template",
    "pageNum": 1,
    "pageSize": 10
  }'
```

## 注意事项

1. **权限要求**：所有接口都需要商务后台管理员权限
2. **请求方法**：所有接口都使用POST方法，采用JSON格式传递参数
3. **分页查询**：支持分页的接口默认页码为1，每页20条记录
4. **任务监控**：批量初始化会返回任务ID，可通过查询进度接口监控执行状态
5. **错误重试**：失败的初始化可以通过重试接口单独处理

## 自动化功能

系统在启动时会自动检测新功能并触发批量初始化，无需手动干预。如果需要手动管理，可以使用上述API接口。
