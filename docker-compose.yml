version: '3'
services:
  vue-builder:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
    # 使用命令替换来获取当前用户的 UID 和 GID
    command: >
      /bin/sh -c "
        set -ex
        echo 'Starting pnpm install...'
        npm install --registry=https://registry.npmmirror.com
        echo 'Starting build...'
        npm run build
      "
    restart: 'no'
  vue-builder-stage:
    image: uhub.service.ucloud.cn/leishi/node:22-alpine
    working_dir: /app
    volumes:
      - .:/app
    environment:
      - FORCE_COLOR=1
      - NPM_CONFIG_LOGLEVEL=info
    # 使用命令替换来获取当前用户的 UID 和 GID
    command: >
      /bin/sh -c "
        set -ex
        echo 'Starting pnpm install...'
        npm install --registry=https://registry.npmmirror.com
        echo 'Starting build:stage...'
        npm run build:stage
      "
    restart: 'no'
