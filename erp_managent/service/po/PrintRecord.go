package po

import (
	_const "voderpltvv/const"
)

// PrintRecord 打印记录实体
type PrintRecord struct {
	Id        *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`     // 打印记录ID
	VenueId   *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`   // 门店ID
	PrintType *string `gorm:"column:print_type;type:varchar(50);not null" json:"printType"` // 打印类型 (例如: OPEN_TABLE, CHECKOUT, SHIFT_CHANGE, REFUND, PRODUCT_OUT)
	PrintNo   *string `gorm:"column:print_no;type:varchar(64);default:''" json:"printNo"`   // 打印单号（该打印单的唯一标识）
	PrintTime *int64  `gorm:"column:print_time;type:bigint;default:0" json:"printTime"`     // 打印时间戳 (毫秒)

	// 统一关联
	SessionId *string `gorm:"column:session_id;type:varchar(64);default:''" json:"sessionId"` // 开台单号/场次ID（所有类型都关联）

	// 通用业务关联字段（根据print_type解释）
	BusinessId    *string `gorm:"column:business_id;type:varchar(64);default:''" json:"businessId"`        // 业务单号（如订单号、结账单号等，根据print_type解释）
	SubBusinessId *string `gorm:"column:sub_business_id;type:varchar(64);default:''" json:"subBusinessId"` // 子业务单号（如出品单号等，根据print_type解释）

	// 操作信息
	OperatorId   *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`      // 操作员ID
	OperatorName *string `gorm:"column:operator_name;type:varchar(100);default:''" json:"operatorName"` // 操作员姓名
	DeviceName   *string `gorm:"column:device_name;type:varchar(100);default:''" json:"deviceName"`     // 打印设备名称/标识
	Status       *int    `gorm:"column:status;type:int;default:0" json:"status"`                        // 打印状态 (0: 成功, 1: 失败)
	ErrorMsg     *string `gorm:"column:error_msg;type:text" json:"errorMsg"`                            // 失败信息 (如果 status=1)
	Content      *string `gorm:"column:content;type:text" json:"content"`                               // 打印内容 (特定结构的JSON字符串)
	Remark       *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`              // 备注说明

	Ctime   *int64 `gorm:"column:ctime;type:bigint;default:0" json:"ctime"`  // 创建时间
	Utime   *int64 `gorm:"column:utime;type:bigint;default:0" json:"utime"`  // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本号
}

// TableName 设置表名
func (PrintRecord) TableName() string {
	return "print_record"
}

// GetId 获取ID
func (p PrintRecord) GetId() string {
	if p.Id == nil {
		return ""
	}
	return *p.Id
}

// --- 类型定义 ---

// PrintType 定义打印类型常量
type PrintType string

// PrintStatus 定义打印状态常量
type PrintStatus int

// 使用统一的常量定义
const (
	PrintTypeOpenTable    PrintType = _const.PRINT_TYPE_OPEN_TABLE    // 开台单
	PrintTypeCheckout     PrintType = _const.PRINT_TYPE_CHECKOUT      // 消费结账单
	PrintTypeShiftChange  PrintType = _const.PRINT_TYPE_SHIFT_CHANGE  // 交班单
	PrintTypeRefund       PrintType = _const.PRINT_TYPE_REFUND        // 退款单
	PrintTypeProductOut   PrintType = _const.PRINT_TYPE_PRODUCT_OUT   // 出品单
	PrintTypeWineStorage  PrintType = _const.PRINT_TYPE_WINE_STORAGE  // 存酒单
	PrintTypeWineWithdraw PrintType = _const.PRINT_TYPE_WINE_WITHDRAW // 取酒单
)

const (
	PrintStatusSuccess PrintStatus = _const.PRINT_STATUS_SUCCESS // 成功
	PrintStatusFailed  PrintStatus = _const.PRINT_STATUS_FAILED  // 失败
)
