package po

// SystemFeatureVersion 系统功能版本管理实体
// 职责：管理系统功能版本和整体初始化进度
type SystemFeatureVersion struct {
	Id                *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                       // ID
	FeatureName       *string `gorm:"column:feature_name;type:varchar(100);not null;index" json:"featureName"`        // 功能名称
	FeatureVersion    *string `gorm:"column:feature_version;type:varchar(20);not null" json:"featureVersion"`         // 功能版本号
	DeployTime        *int64  `gorm:"column:deploy_time;type:bigint;not null;index" json:"deployTime"`                // 部署时间
	InitStatus        *int    `gorm:"column:init_status;type:tinyint;not null;default:0" json:"initStatus"`           // 初始化状态：0=待初始化，1=初始化中，2=完成
	TotalVenues       *int    `gorm:"column:total_venues;type:int;not null;default:0" json:"totalVenues"`             // 总门店数
	InitializedVenues *int    `gorm:"column:initialized_venues;type:int;not null;default:0" json:"initializedVenues"` // 已初始化门店数
	FailedVenues      *int    `gorm:"column:failed_venues;type:int;not null;default:0" json:"failedVenues"`           // 失败门店数

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (SystemFeatureVersion) TableName() string {
	return "system_feature_versions"
}

// GetId 获取ID
func (s SystemFeatureVersion) GetId() string {
	if s.Id == nil {
		return ""
	}
	return *s.Id
}
