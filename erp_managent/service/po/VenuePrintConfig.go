package po

// VenuePrintConfig 门店打印配置实体
// 职责：存储每个门店对特定打印类型的个性化使用偏好
type VenuePrintConfig struct {
	Id                 *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                                                     // 配置唯一标识
	VenueId            *string `gorm:"column:venue_id;type:varchar(64);not null;uniqueIndex:idx_venue_template_code" json:"venueId"`                 // 门店ID
	TemplateCode       *string `gorm:"column:template_code;type:varchar(50);not null;uniqueIndex:idx_venue_template_code;index" json:"templateCode"` // 打印类型编码，与元模板的编码对应
	SelectedTemplateId *string `gorm:"column:selected_template_id;type:varchar(64);not null;index:fk_selected_template" json:"selectedTemplateId"`   // 选择使用的print_templates表ID
	IsEnabled          *bool   `gorm:"column:is_enabled;type:bool;default:true;index" json:"isEnabled"`                                              // 门店级业务开关，控制该门店是否执行此类型的打印
	Copies             *int    `gorm:"column:copies;type:int;default:1" json:"copies"`                                                               // 打印份数
	BusinessConfig     *string `gorm:"column:business_config;type:json" json:"businessConfig"`                                                       // 业务逻辑开关配置JSON，如{"split_by_type": true, "show_discount_detail": true}
	Remark             *string `gorm:"column:remark;type:varchar(500);default:''" json:"remark"`                                                     // 配置备注说明

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (VenuePrintConfig) TableName() string {
	return "venue_print_configs"
}

// GetId 获取ID
func (v VenuePrintConfig) GetId() string {
	if v.Id == nil {
		return ""
	}
	return *v.Id
}
