package po

// ProductStorageOperationLog 存酒操作历史记录表
type ProductStorageOperationLog struct {
	Id               *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`                      // ID
	StorageId        *string `gorm:"column:storage_id;type:varchar(64);default:''" json:"storageId"`                // 关联存酒记录ID
	OrderNo          *string `gorm:"column:order_no;type:varchar(64);default:''" json:"orderNo"`                    // 存酒明细单号
	OperationType    *string `gorm:"column:operation_type;type:varchar(32);default:''" json:"operationType"`        // 操作类型(storage/withdraw/refill/scrap)
	OperationName    *string `gorm:"column:operation_name;type:varchar(32);default:''" json:"operationName"`        // 操作类型名称(存酒/取酒/续存/报废)
	OperationTime    *int64  `gorm:"column:operation_time;type:int;default:0" json:"operationTime"`                 // 操作时间
	Quantity         *int    `gorm:"column:quantity;type:int;default:0" json:"quantity"`                            // 操作数量
	OperatorId       *string `gorm:"column:operator_id;type:varchar(64);default:''" json:"operatorId"`              // 操作人ID
	OperatorName     *string `gorm:"column:operator_name;type:varchar(64);default:''" json:"operatorName"`          // 操作人姓名
	BalanceQty       *int    `gorm:"column:balance_qty;type:int;default:0" json:"balanceQty"`                       // 操作后剩余数量
	Remark           *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`                      // 备注
	RoomName         *string `gorm:"column:room_name;type:varchar(64);default:''" json:"roomName"`                  // 寄存包厢名称（存酒操作时的寄存包厢）
	DeliveryRoomName *string `gorm:"column:delivery_room_name;type:varchar(64);default:''" json:"deliveryRoomName"` // 送达包厅名称（取酒操作时的送达包厢）

	// 门店和营业日期相关字段
	VenueId           *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`                      // 门店ID
	VenueName         *string `gorm:"column:venue_name;type:varchar(64);default:''" json:"venueName"`                  // 门店名称
	BusinessDay       *int64  `gorm:"column:business_day;type:bigint;default:0" json:"businessDay"`                    // 营业日期时间戳
	BusinessDayStr    *string `gorm:"column:business_day_str;type:varchar(32);default:''" json:"businessDayStr"`       // 营业日期字符串
	BusinessStartHour *string `gorm:"column:business_start_hour;type:varchar(16);default:''" json:"businessStartHour"` // 营业开始时间
	BusinessEndHour   *string `gorm:"column:business_end_hour;type:varchar(16);default:''" json:"businessEndHour"`     // 营业结束时间

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (ProductStorageOperationLog) TableName() string {
	return "product_storage_operation_log"
}

func (p ProductStorageOperationLog) GetId() string {
	return *p.Id
}
