package po

// SmsVerifyConfig 短信验证配置实体
type SmsVerifyConfig struct {
	Id          *string `gorm:"column:id;type:varchar(64);primaryKey;not null" json:"id"`           // ID
	VenueId     *string `gorm:"column:venue_id;type:varchar(64);default:''" json:"venueId"`         // 门店ID
	ConfigKey   *string `gorm:"column:config_key;type:varchar(100);default:''" json:"configKey"`    // 配置键名
	Enabled     *int    `gorm:"column:enabled;type:int;default:1" json:"enabled"`                   // 是否启用：0-禁用，1-启用
	Description *string `gorm:"column:description;type:varchar(255);default:''" json:"description"` // 配置描述
	Remark      *string `gorm:"column:remark;type:varchar(255);default:''" json:"remark"`           // 备注

	Ctime   *int64 `gorm:"column:ctime;type:int;default:0" json:"ctime"`     // 创建时间
	Utime   *int64 `gorm:"column:utime;type:int;default:0" json:"utime"`     // 更新时间
	State   *int   `gorm:"column:state;type:int;default:0" json:"state"`     // 状态
	Version *int   `gorm:"column:version;type:int;default:0" json:"version"` // 版本
}

// TableName 设置表名
func (SmsVerifyConfig) TableName() string {
	return "sms_verify_config"
}

func (s SmsVerifyConfig) GetId() string {
	return *s.Id
}
