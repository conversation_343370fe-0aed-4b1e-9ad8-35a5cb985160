package impl

import (
	"fmt"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"
	"voderpltvv/util"
)

type ProductStorageOperationLogService struct{}

func NewProductStorageOperationLogService() *ProductStorageOperationLogService {
	return &ProductStorageOperationLogService{}
}

// 填充业务字段（门店信息和营业日期）
func (service *ProductStorageOperationLogService) populateBusinessFields(
	storageId string,
	operationTime int64,
	opLog *po.ProductStorageOperationLog) error {

	// 1. 获取存酒记录以获取门店信息
	wineStorageService := &WineStorageService{}
	storage, err := wineStorageService.FindWineStorageById(&gin.Context{}, storageId)
	if err != nil {
		return fmt.Errorf("获取存酒记录失败: %v", err)
	}

	// 2. 获取门店信息
	venueService := &VenueService{}
	venue, err := venueService.FindVenueById(&gin.Context{}, *storage.VenueId)
	if err != nil {
		return fmt.Errorf("获取门店信息失败: %v", err)
	}

	// 3. 设置门店相关字段
	opLog.VenueId = storage.VenueId
	opLog.VenueName = venue.Name

	// 4. 设置营业时间字段
	if venue.StartHours != nil && *venue.StartHours != "" {
		opLog.BusinessStartHour = venue.StartHours
	} else {
		opLog.BusinessStartHour = lo.ToPtr("07:00")
	}

	if venue.EndHours != nil && *venue.EndHours != "" {
		opLog.BusinessEndHour = venue.EndHours
	} else {
		opLog.BusinessEndHour = lo.ToPtr("07:00")
	}

	// 5. 计算营业日期字段
	if operationTime > 0 {
		startHour := lo.FromPtrOr(opLog.BusinessStartHour, "07:00")
		businessDay, _, _ := util.GetCurrentBusinessTimeRange(startHour)
		opLog.BusinessDay = lo.ToPtr(businessDay)
		opLog.BusinessDayStr = lo.ToPtr(util.GetYmd(businessDay))
	} else {
		opLog.BusinessDay = lo.ToPtr(int64(0))
		opLog.BusinessDayStr = lo.ToPtr("")
	}

	return nil
}

// 添加存酒操作历史记录
func (service *ProductStorageOperationLogService) AddOperationLog(
	logCtx *gin.Context,
	storageId string,
	orderNo string,
	operationType string,
	operationName string,
	operationTime int64,
	quantity int,
	operatorId string,
	operatorName string,
	balanceQty int,
	remark string) (*po.ProductStorageOperationLog, error) {

	// 创建操作历史记录
	opLog := &po.ProductStorageOperationLog{
		StorageId:     &storageId,
		OrderNo:       &orderNo,
		OperationType: &operationType,
		OperationName: &operationName,
		OperationTime: &operationTime,
		Quantity:      &quantity,
		OperatorId:    &operatorId,
		OperatorName:  &operatorName,
		BalanceQty:    &balanceQty,
		Remark:        &remark,
	}

	// 填充门店信息和营业日期字段
	if err := service.populateBusinessFields(storageId, operationTime, opLog); err != nil {
		// 填充失败不影响主流程，记录日志继续
		fmt.Printf("填充操作记录业务字段失败: %v\n", err)
	}

	// 使用事务确保数据一致性
	err := model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 在事务中保存操作历史记录
		if err := SaveWithTx(opLog, tx); err != nil {
			return err
		}

		// 更新存酒记录的最后操作时间
		storage := &po.ProductStorage{
			Id:                &storageId,
			LastOperationTime: &operationTime,
		}

		// 在事务中更新存酒记录
		if err := UpdateNotNullWithTx(storage, tx); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return opLog, nil
}

// 添加存酒操作历史记录（带包厢信息）
func (service *ProductStorageOperationLogService) AddOperationLogWithRoom(
	logCtx *gin.Context,
	storageId string,
	orderNo string,
	operationType string,
	operationName string,
	operationTime int64,
	quantity int,
	operatorId string,
	operatorName string,
	balanceQty int,
	remark string,
	roomName string,
	deliveryRoomName string) (*po.ProductStorageOperationLog, error) {

	// 创建操作历史记录
	opLog := &po.ProductStorageOperationLog{
		StorageId:        &storageId,
		OrderNo:          &orderNo,
		OperationType:    &operationType,
		OperationName:    &operationName,
		OperationTime:    &operationTime,
		Quantity:         &quantity,
		OperatorId:       &operatorId,
		OperatorName:     &operatorName,
		BalanceQty:       &balanceQty,
		Remark:           &remark,
		RoomName:         &roomName,
		DeliveryRoomName: &deliveryRoomName,
	}

	// 填充门店信息和营业日期字段
	if err := service.populateBusinessFields(storageId, operationTime, opLog); err != nil {
		// 填充失败不影响主流程，记录日志继续
		fmt.Printf("填充操作记录业务字段失败: %v\n", err)
	}

	// 使用事务确保数据一致性
	err := model.DBMaster.Self.Transaction(func(tx *gorm.DB) error {
		// 在事务中保存操作历史记录
		if err := SaveWithTx(opLog, tx); err != nil {
			return err
		}

		// 更新存酒记录的最后操作时间
		storage := &po.ProductStorage{
			Id:                &storageId,
			LastOperationTime: &operationTime,
		}

		// 在事务中更新存酒记录
		if err := UpdateNotNullWithTx(storage, tx); err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return opLog, nil
}

// 查询存酒操作历史记录
func (service *ProductStorageOperationLogService) FindByStorageId(
	logCtx *gin.Context,
	storageId string) ([]*po.ProductStorageOperationLog, error) {

	// 使用模型
	query := dal.Use(model.DBSlave.Self)

	// 使用DAL层查询数据
	logs, err := query.ProductStorageOperationLog.WithContext(logCtx).
		Where(query.ProductStorageOperationLog.StorageId.Eq(storageId)).
		Order(query.ProductStorageOperationLog.OperationTime.Desc()).
		Find()

	// 使用lo库处理可能的nil结果
	return lo.If(err == nil, logs).ElseF(func() []*po.ProductStorageOperationLog {
		return []*po.ProductStorageOperationLog{}
	}), err
}

// 根据ID查询单个操作记录
func (service *ProductStorageOperationLogService) FindById(
	logCtx *gin.Context,
	id string) (*po.ProductStorageOperationLog, error) {

	// 使用模型
	query := dal.Use(model.DBSlave.Self)

	// 使用DAL层查询数据
	log, err := query.ProductStorageOperationLog.WithContext(logCtx).
		Where(query.ProductStorageOperationLog.Id.Eq(id)).
		First()

	return log, err
}

// 创建存酒操作历史记录（事务内）
func (service *ProductStorageOperationLogService) CreateWithinTransaction(
	tx *gorm.DB,
	storageId string,
	orderNo string,
	operationType string,
	operationName string,
	operationTime int64,
	quantity int,
	operatorId string,
	operatorName string,
	balanceQty int,
	remark string) (*po.ProductStorageOperationLog, error) {
	// 调用带包厢信息的方法，包厢信息为空
	return service.CreateWithinTransactionWithRoom(tx, storageId, orderNo, operationType, operationName, operationTime, quantity, operatorId, operatorName, balanceQty, remark, "", "")
}

// 创建存酒操作历史记录（事务内，带包厢信息）
func (service *ProductStorageOperationLogService) CreateWithinTransactionWithRoom(
	tx *gorm.DB,
	storageId string,
	orderNo string,
	operationType string,
	operationName string,
	operationTime int64,
	quantity int,
	operatorId string,
	operatorName string,
	balanceQty int,
	remark string,
	roomName string,
	deliveryRoomName string) (*po.ProductStorageOperationLog, error) {

	// 安全检查
	if tx == nil {
		return nil, fmt.Errorf("事务对象不能为空")
	}

	// 确保所有字符串参数不为空
	if storageId == "" {
		storageId = "unknown"
	}
	if orderNo == "" {
		orderNo = "unknown"
	}
	if operationType == "" {
		operationType = "unknown"
	}
	if operationName == "" {
		operationName = "unknown"
	}
	if remark == "" {
		remark = "无备注"
	}

	// 创建操作历史记录
	opLog := &po.ProductStorageOperationLog{
		StorageId:        &storageId,
		OrderNo:          &orderNo,
		OperationType:    &operationType,
		OperationName:    &operationName,
		OperationTime:    &operationTime,
		Quantity:         &quantity,
		OperatorId:       &operatorId,
		OperatorName:     &operatorName,
		BalanceQty:       &balanceQty,
		Remark:           &remark,
		RoomName:         &roomName,
		DeliveryRoomName: &deliveryRoomName,
	}

	// 填充门店信息和营业日期字段
	if err := service.populateBusinessFields(storageId, operationTime, opLog); err != nil {
		// 填充失败不影响主流程，记录日志继续
		fmt.Printf("填充操作记录业务字段失败: %v\n", err)
	}

	// 使用BaseService的SaveWithTx方法，自动生成ID和基础字段
	err := SaveWithTx(opLog, tx)
	if err != nil {
		return nil, err
	}

	// 同时更新存酒记录的最后操作时间
	storage := &po.ProductStorage{
		Id:                &storageId,
		LastOperationTime: &operationTime,
	}

	// 使用UpdateNotNullWithTx方法在事务中更新存酒记录
	err = UpdateNotNullWithTx(storage, tx)
	if err != nil {
		return nil, err
	}

	return opLog, nil
}
