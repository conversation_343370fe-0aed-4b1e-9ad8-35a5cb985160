package impl

import (
	"errors"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"sync"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"

	"encoding/json"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type VenueService struct {
}

var venueTransfer = transfer.VenueTransfer{}
var venueService = &VenueService{}
var idMutex sync.Mutex

func NewVenueService() *VenueService {
	return &VenueService{}
}

func (service *VenueService) getNextVenueId(logCtx *gin.Context) (string, error) {
	idMutex.Lock()
	defer idMutex.Unlock()

	// 只查询数字格式的id
	var maxId string
	err := model.DBMaster.Self.Model(&po.Venue{}).
		Select("COALESCE(MAX(CAST(id AS UNSIGNED)), 100000) as max_id").
		Where("id REGEXP '^[0-9]+$'").
		Row().Scan(&maxId)
	if err != nil {
		util.Wlog(logCtx).Errorf("获取最大id失败:[%s]", err.Error())
		return "", err
	}

	currentId, err := strconv.Atoi(maxId)
	if err != nil {
		util.Wlog(logCtx).Errorf("转换id失败:[%s]", err.Error())
		return "", err
	}
	nextId := currentId + 1

	if nextId > 999999 {
		return "", errors.New("id exceeded maximum value")
	}

	util.Wlog(logCtx).Infof("当前最大id:[%s], 下一个id:[%d]", maxId, nextId)
	return fmt.Sprintf("%06d", nextId), nil
}

func (service *VenueService) CreateVenue(logCtx *gin.Context, venue *po.Venue) error {
	// 获取下一个可用id
	id, err := service.getNextVenueId(logCtx)
	if err != nil {
		return err
	}
	venue.Id = &id
	util.Wlog(logCtx).Infof("生成的门店编号为:[%s]", *venue.Id)

	return Save(venue)
}

func (service *VenueService) CreateVenueWithTx(logCtx *gin.Context, venue *po.Venue, tx *gorm.DB) error {
	return SaveWithTx(venue, tx)
}

func (service *VenueService) UpdateVenue(logCtx *gin.Context, venue *po.Venue) error {
	return Update(venue)
}

func (service *VenueService) UpdateVenuePartial(logCtx *gin.Context, venue *po.Venue) error {
	return UpdateNotNull(venue)
}

func (service *VenueService) UpdateVenuePartialWithTx(logCtx *gin.Context, venue *po.Venue, tx *gorm.DB) error {
	return UpdateNotNullWithTx(venue, tx)
}

func (service *VenueService) DeleteVenue(logCtx *gin.Context, id string) error {
	return Delete(po.Venue{Id: &id})
}

func (service *VenueService) FindVenueById(logCtx *gin.Context, id string) (venue *po.Venue, err error) {
	if id == "" {
		return nil, errors.New("id不能为空")
	}
	venue = &po.Venue{}
	err = model.DBMaster.Self.Where("id=?", id).First(venue).Error
	return
}

func (service *VenueService) FindAllVenue(logCtx *gin.Context, reqDto *req.QueryVenueReqDto) (list *[]po.Venue, err error) {
	// 检查是否有任何查询条件
	if !hasAnyQueryCondition(reqDto) {
		util.Wlog(logCtx).Warn("没有提供任何查询条件")
		return &[]po.Venue{}, nil // 返回空列表
	}
	db := model.DBSlave.Self.Model(&po.Venue{})
	if reqDto.Ids != nil && len(*reqDto.Ids) > 0 {
		db = db.Where("id IN (?)", *reqDto.Ids)
	}
	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.Name != nil && *reqDto.Name != "" {
		db = db.Where("name LIKE ?", "%"+*reqDto.Name+"%")
	}
	if reqDto.Address != nil && *reqDto.Address != "" {
		db = db.Where("address LIKE ?", "%"+*reqDto.Address+"%")
	}
	if reqDto.Phone != nil && *reqDto.Phone != "" {
		db = db.Where("phone=?", *reqDto.Phone)
	}
	if reqDto.Unionid != nil && *reqDto.Unionid != "" {
		db = db.Where("unionid=?", *reqDto.Unionid)
	}
	if reqDto.AuditStatus != nil {
		db = db.Where("audit_status = ?", *reqDto.AuditStatus)
	}

	db = db.Order("ctime desc")
	list = &[]po.Venue{}
	result := db.Find(list)
	err = result.Error
	if err != nil {
		util.Wlog(logCtx).Errorf("查询门店失败:[%s]", err.Error())
		return nil, err
	}

	// 添加空结果处理
	if len(*list) == 0 {
		util.Wlog(logCtx).Infof("未找到符合条件的门店")
		return list, nil // 返回空列表而不是 nil
	}

	return list, nil
}

// FindAllVenuesForInitialization 获取所有门店用于初始化（包括所有审核状态的门店）
func (service *VenueService) FindAllVenuesForInitialization(logCtx *gin.Context) ([]*po.Venue, error) {
	var venues []*po.Venue

	// 查询所有门店（不限制审核状态，因为初始化数据是所有门店都需要的基础数据）
	// 注意：venue表使用硬删除，所以不需要过滤state字段
	err := model.DBSlave.Self.Model(&po.Venue{}).
		Find(&venues).Error

	if err != nil {
		util.Wlog(logCtx).Errorf("查询门店失败: %v", err)
		return nil, fmt.Errorf("查询门店失败: %w", err)
	}

	util.Wlog(logCtx).Infof("查询到%d个门店（包含所有审核状态）", len(venues))
	return venues, nil
}

// 检查是否有任何查询条件
func hasAnyQueryCondition(reqDto *req.QueryVenueReqDto) bool {
	return (reqDto.Ids != nil && len(*reqDto.Ids) > 0) ||
		(reqDto.Id != nil && *reqDto.Id != "") ||
		(reqDto.Name != nil && *reqDto.Name != "") ||
		(reqDto.Address != nil && *reqDto.Address != "") ||
		(reqDto.Phone != nil && *reqDto.Phone != "") ||
		(reqDto.Unionid != nil && *reqDto.Unionid != "") ||
		(reqDto.AuditStatus != nil && *reqDto.AuditStatus != 0)
}

func (service *VenueService) FindAllVenueWithPagination(logCtx *gin.Context, reqDto *req.QueryVenueReqDto) (list *[]po.Venue, total int64, err error) {
	db := model.DBSlave.Self.Model(&po.Venue{})

	if reqDto.PageNum == nil || *reqDto.PageNum <= 0 {
		reqDto.PageNum = util.GetItPtr(1)
	}
	if reqDto.PageSize == nil || *reqDto.PageSize <= 0 {
		reqDto.PageSize = util.GetItPtr(10)
	}

	if reqDto.Id != nil && *reqDto.Id != "" {
		db = db.Where("id=?", *reqDto.Id)
	}
	if reqDto.Name != nil && *reqDto.Name != "" {
		db = db.Where("name LIKE ?", "%"+*reqDto.Name+"%")
	}
	if reqDto.Address != nil && *reqDto.Address != "" {
		db = db.Where("address LIKE ?", "%"+*reqDto.Address+"%")
	}
	if reqDto.Mac != nil && *reqDto.Mac != "" {
		db = db.Where("mac=?", *reqDto.Mac)
	}
	if reqDto.AuditStatus != nil {
		db = db.Where("audit_status = ?", *reqDto.AuditStatus)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	list = &[]po.Venue{}
	if total <= 0 {
		return
	}
	// 分页+排序
	db = db.Offset((*reqDto.PageNum - 1) * *reqDto.PageSize).Limit(*reqDto.PageSize)
	db = db.Order("ctime desc")
	err = db.Find(list).Error
	return
}

// UpdateVenueAppInfo 更新门店的AppID和AppKey信息
func (service *VenueService) UpdateVenueAppInfo(logCtx *gin.Context, venueId string, dogname string, pwd string) error {
	// 1. 调用第三方接口获取appId和appKey
	ktvInfo, err := service.getKTVInfo(logCtx, dogname, pwd)
	if err != nil {
		util.Wlog(logCtx).Errorf("获取KTV信息失败: %v", err)
		return err
	}

	// 2. 验证接口返回状态
	if ktvInfo.Code != 200 || ktvInfo.Errcode != 0 {
		return fmt.Errorf("获取KTV信息失败: %s", ktvInfo.Errmsg)
	}

	// 3. 更新门店信息
	venue := &po.Venue{
		Id:           &venueId,
		AppId:        &ktvInfo.Result.AppID,
		AppKey:       &ktvInfo.Result.AppKey,
		IsThunderVOD: util.GetItPtr(1),
	}

	err = service.UpdateVenuePartial(logCtx, venue)
	if err != nil {
		util.Wlog(logCtx).Errorf("更新门店AppID和AppKey失败: %v", err)
		return err
	}

	return nil
}

// ThunderKTVInfoResponse 雷石KTV信息响应结构
type ThunderKTVInfoResponse struct {
	Errcode int    `json:"errcode"`
	Errmsg  string `json:"errmsg"`
	Code    int    `json:"code"`
	Msg     string `json:"msg"`
	Result  struct {
		KTVID      string `json:"ktvid"`
		KTVName    string `json:"ktv_name"`
		KTVSubname string `json:"ktv_subname"`
		AppID      string `json:"appid"`
		AppKey     string `json:"appkey"`
	} `json:"result"`
}

// getKTVInfo 获取KTV信息
func (service *VenueService) getKTVInfo(logCtx *gin.Context, dogname string, pwd string) (*ThunderKTVInfoResponse, error) {
	// 构建请求URL
	url := fmt.Sprintf("https://softdog.ktvsky.com/erp/ktvinfo?dogname=%s&pwd=%s", dogname, pwd)

	// 发送HTTP GET请求
	resp, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	// 读取响应内容
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	// 解析JSON响应
	var ktvInfo ThunderKTVInfoResponse
	err = json.Unmarshal(body, &ktvInfo)
	if err != nil {
		return nil, err
	}

	return &ktvInfo, nil
}

// ConvertToVenueVO 转换为场所VO
func (service *VenueService) ConvertToVenueVO(ctx *gin.Context, venue po.Venue) vo.VenueVO {
	return venueTransfer.PoToVo(venue)
}

// ConvertToVenuePO 转换为场所PO
func (service *VenueService) ConvertToVenue(ctx *gin.Context, venueVO vo.VenueVO) po.Venue {
	return venueTransfer.VoToPo(venueVO)
}

func (service *VenueService) FindByVenueID(ctx *gin.Context, venueID string) (*po.Venue, error) {
	venue := &po.Venue{}
	err := model.DBSlave.Self.Where("id=?", venueID).First(venue).Error
	return venue, err
}
