package impl

import (
	"errors"
	"fmt"
	"voderpltvv/erp_managent/service/dal"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// SmsVerifyConfigService 短信验证配置服务
type SmsVerifyConfigService struct {
}

// CreateOrUpdateConfig 创建或更新短信验证配置
func (service *SmsVerifyConfigService) CreateOrUpdateConfig(logCtx *gin.Context, config *po.SmsVerifyConfig) error {
	if config.VenueId == nil || *config.VenueId == "" {
		return errors.New("门店ID不能为空")
	}
	if config.ConfigKey == nil || *config.ConfigKey == "" {
		return errors.New("配置键名不能为空")
	}

	// 检查配置是否已存在
	existing, err := service.GetConfigByKey(logCtx, *config.VenueId, *config.ConfigKey)
	if err != nil && err != gorm.ErrRecordNotFound {
		return fmt.Errorf("查询配置失败: %v", err)
	}

	if existing != nil {
		// 更新现有配置
		config.Id = existing.Id
		return UpdateNotNull(config)
	} else {
		// 创建新配置，使用BaseService的Save方法
		return Save(config)
	}
}

// BatchCreateOrUpdateConfigs 批量创建或更新短信验证配置
func (service *SmsVerifyConfigService) BatchCreateOrUpdateConfigs(logCtx *gin.Context, configs []*po.SmsVerifyConfig) error {
	if len(configs) == 0 {
		return errors.New("配置列表不能为空")
	}

	// 验证所有配置的门店ID是否一致
	venueId := configs[0].VenueId
	for _, config := range configs {
		if config.VenueId == nil || *config.VenueId == "" {
			return errors.New("门店ID不能为空")
		}
		if config.ConfigKey == nil || *config.ConfigKey == "" {
			return errors.New("配置键名不能为空")
		}
		if *config.VenueId != *venueId {
			return errors.New("批量更新时所有配置的门店ID必须一致")
		}
	}

	// 使用事务进行批量操作
	tx := model.DBMaster.Self.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	for _, config := range configs {
		// 检查配置是否已存在
		existing, err := service.GetConfigByKey(logCtx, *config.VenueId, *config.ConfigKey)
		if err != nil && err != gorm.ErrRecordNotFound {
			tx.Rollback()
			return fmt.Errorf("查询配置失败: %v", err)
		}

		if existing != nil {
			// 更新现有配置
			config.Id = existing.Id
			if err := UpdateNotNull(config); err != nil {
				tx.Rollback()
				return fmt.Errorf("更新配置失败: %v", err)
			}
		} else {
			// 创建新配置
			if err := Save(config); err != nil {
				tx.Rollback()
				return fmt.Errorf("创建配置失败: %v", err)
			}
		}
	}

	return tx.Commit().Error
}

// GetConfigByKey 根据门店ID和配置键获取短信验证配置
func (service *SmsVerifyConfigService) GetConfigByKey(logCtx *gin.Context, venueId, configKey string) (*po.SmsVerifyConfig, error) {
	query := dal.Use(model.DBSlave.Self)
	return query.SmsVerifyConfig.WithContext(logCtx).
		Where(query.SmsVerifyConfig.VenueId.Eq(venueId)).
		Where(query.SmsVerifyConfig.ConfigKey.Eq(configKey)).
		Where(query.SmsVerifyConfig.State.Eq(0)). // Note: State is still used in queries for filtering active records.
		First()
}

// GetConfigsByVenue 获取门店所有短信验证配置
func (service *SmsVerifyConfigService) GetConfigsByVenue(logCtx *gin.Context, venueId string) ([]*po.SmsVerifyConfig, error) {
	query := dal.Use(model.DBSlave.Self)
	return query.SmsVerifyConfig.WithContext(logCtx).
		Where(query.SmsVerifyConfig.VenueId.Eq(venueId)).
		Where(query.SmsVerifyConfig.State.Eq(0)). // Note: State is still used in queries for filtering active records.
		Find()
}

// DeleteConfig 删除短信验证配置
func (service *SmsVerifyConfigService) DeleteConfig(logCtx *gin.Context, configId string) error {
	query := dal.Use(model.DBMaster.Self)
	// 物理删除
	_, err := query.SmsVerifyConfig.WithContext(logCtx).
		Where(query.SmsVerifyConfig.Id.Eq(configId)).
		Delete()
	return err
}
