package application

import (
	"errors"

	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"

	. "voderpltvv/const"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	venueapp "voderpltvv/erp_managent/application/venue"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"
	"voderpltvv/model"
	"voderpltvv/util"
)

// VenueAuthAppService 门店授权应用服务接口
type VenueAuthAppService interface {
	// HandleAuthActivation 处理授权激活
	HandleAuthActivation(ctx *gin.Context, request req.ActivateAuthReqDto) error

	// GenerateAuth 生成授权码
	GenerateAuth(ctx *gin.Context, request req.GenerateAuthReqDto, userId string) (string, error)

	// HandleAuditProcess 处理审核流程
	HandleAuditProcess(ctx *gin.Context, request req.AuditVenueReqDto, userId string) error

	// GetAuthStatus 获取授权状态
	GetAuthStatus(ctx *gin.Context, request req.GetAuthStatusReqDto) (*vo.VenueAuthStatusVO, error)

	// QueryAuthCodes 分页查询授权码
	QueryAuthCodes(ctx *gin.Context, request req.QueryVenueAuthCodeReqDto) (*[]vo.VenueAuthCodeVO, int64, error)

	// QueryNonSystemAuthCodes 查询非系统创建的授权码（无分页）
	QueryNonSystemAuthCodes(ctx *gin.Context, request req.QueryNonSystemAuthCodeReqDto) (*[]vo.VenueAuthCodeVO, error)
}

// VenueAuthAppServiceImpl 门店授权应用服务实现
type VenueAuthAppServiceImpl struct {
	venueAuthService    impl.VenueAuthService
	systemRecordService impl.SystemOperationRecordService // 替换为通用服务
	venueAuthTransfer   transfer.VenueAuthTransfer
	venueService        *impl.VenueService // 添加VenueService
}

// NewVenueAuthAppService 创建门店授权应用服务
func NewVenueAuthAppService() VenueAuthAppService {
	return &VenueAuthAppServiceImpl{
		venueAuthService:    impl.NewVenueAuthService(),
		systemRecordService: impl.NewSystemOperationRecordService(), // 初始化新服务
		venueAuthTransfer:   transfer.VenueAuthTransfer{},
		venueService:        impl.NewVenueService(), // 初始化VenueService
	}
}

// HandleAuthActivation 处理授权激活
func (s *VenueAuthAppServiceImpl) HandleAuthActivation(ctx *gin.Context, request req.ActivateAuthReqDto) error {
	if request.VenueId == nil || *request.VenueId == "" {
		return errors.New("venue_id is required")
	}
	if request.AuthCode == nil || *request.AuthCode == "" {
		return errors.New("auth_code is required")
	}

	// 开启事务，确保激活操作的原子性（删除旧记录+激活新记录）
	tx := model.DBMaster.Self.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 使用 defer 处理事务提交或回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // re-throw panic after Rollback
		}
	}()

	// 调用领域服务激活授权码（传递事务）
	err := s.venueAuthService.ActivateAuth(
		ctx,
		*request.VenueId,
		*request.AuthCode,
		tx, // 传递事务对象
	)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	return nil
}

// GenerateAuth 生成授权码
func (s *VenueAuthAppServiceImpl) GenerateAuth(ctx *gin.Context, request req.GenerateAuthReqDto, userId string) (string, error) {
	if request.ValidPeriod == nil || *request.ValidPeriod <= 0 {
		return "", errors.New("valid_period must be greater than 0")
	}

	// venueId可以为空，如果为空则使用空字符串
	venueId := ""
	if request.VenueId != nil {
		venueId = *request.VenueId
	}

	remark := ""
	if request.Remark != nil {
		remark = *request.Remark
	}

	// 获取合同信息
	contractNo := ""
	if request.ContractNo != nil {
		contractNo = *request.ContractNo
	}
	contractName := ""
	if request.ContractName != nil {
		contractName = *request.ContractName
	}

	// 获取创建者ID，如果请求中没有指定，则为空字符串（方法内部会使用userId）
	creatorId := ""
	if request.CreatorId != nil {
		creatorId = *request.CreatorId
	}

	// 非事务调用传nil
	return s.venueAuthService.GenerateAuthCode(
		ctx,
		venueId,
		*request.ValidPeriod,
		userId,
		remark,
		contractNo,
		contractName,
		creatorId, // 创建者ID参数
		nil,       // tx参数
	)
}

// HandleAuditProcess 处理审核流程
func (s *VenueAuthAppServiceImpl) HandleAuditProcess(ctx *gin.Context, request req.AuditVenueReqDto, userId string) error {
	if request.VenueId == nil || *request.VenueId == "" {
		return errors.New("venue_id is required")
	}
	if request.Status == nil || (*request.Status != 1 && *request.Status != 2) {
		return errors.New("status must be 1 or 2")
	}

	// 获取原状态
	venue, err := s.venueAuthService.GetVenueAuthStatus(ctx, *request.VenueId)
	if err != nil {
		return err
	}
	oldStatus := *venue.Status

	// 开启事务
	tx := model.DBMaster.Self.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 使用 defer 处理事务提交或回滚
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // re-throw panic after Rollback
		}
	}()

	// 调用领域服务审核门店
	err = s.venueAuthService.AuditVenue(
		ctx,
		*request.VenueId,
		*request.Status,
		userId,
		*request.Remark,
		tx, // 传递事务对象
	)
	if err != nil {
		tx.Rollback()
		return err
	}

	// 修改记录操作调用
	recordParams := impl.RecordParams{
		OldValue:  map[string]interface{}{"status": oldStatus},
		NewValue:  map[string]interface{}{"status": *request.Status},
		ExtraInfo: map[string]string{"remark": *request.Remark},
	}

	if err := s.systemRecordService.RecordOperation(
		ctx,
		userId,
		CLIENT_ADMIN,               // 使用常量包
		OPERATION_TYPE_AUDIT_VENUE, // 统一操作类型常量
		TARGET_TYPE_VENUE,          // 统一目标类型常量
		*request.VenueId,
		recordParams,
		tx,
	); err != nil {
		util.Wlog(ctx).Errorf("操作记录失败: %v", err)
	}

	// 审核通过
	if *request.Status == 1 {
		// 从配置文件中获取app_id和app_key
		appId := viper.GetString("vod.app_id")
		appKey := viper.GetString("vod.app_key")

		if appId != "" && appKey != "" {
			// 更新门店信息
			venueUpdate := &po.Venue{
				Id:           request.VenueId,
				AppId:        &appId,
				AppKey:       &appKey,
				IsThunderVOD: util.GetItPtr(1), // 设置为雷石VOD
			}

			// 使用事务更新门店信息
			err = s.venueService.UpdateVenuePartialWithTx(ctx, venueUpdate, tx)
			if err != nil {
				util.Wlog(ctx).Errorf("更新门店AppID和AppKey失败: %v", err)
				tx.Rollback()
				return err
			}
			util.Wlog(ctx).Infof("门店审核通过，已更新AppID和AppKey: %s", *request.VenueId)
		} else {
			util.Wlog(ctx).Warnf("配置文件中未找到vod.app_id或vod.app_key")
		}

		// 查询system创建的试用授权码
		authPO, err := s.venueAuthService.GetVenueAuthStatus(ctx, *request.VenueId)

		if err == nil && authPO != nil && *authPO.CreatorId == "system" && *authPO.Status == 0 {
			err = s.venueAuthService.ActivateAuth(ctx, *request.VenueId, *authPO.AuthCode, tx)
			if err != nil {
				tx.Rollback()
				return err
			}
		}
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		tx.Rollback()
		return err
	}

	// 门店审核通过后，触发门店数据初始化（在事务提交后执行，确保审核成功不受影响）
	if *request.Status == 1 {
		venueInitService := venueapp.NewVenueInitializationService()
		if err := venueInitService.InitializeNewVenue(ctx, *request.VenueId); err != nil {
			// 初始化失败不影响审核流程，只记录错误日志
			util.Wlog(ctx).Errorf("门店[%s]审核通过后数据初始化失败: %v", *request.VenueId, err)
		} else {
			util.Wlog(ctx).Infof("门店[%s]审核通过后数据初始化完成", *request.VenueId)
		}
	}

	return nil
}

// GetAuthStatus 获取授权状态
func (s *VenueAuthAppServiceImpl) GetAuthStatus(ctx *gin.Context, request req.GetAuthStatusReqDto) (*vo.VenueAuthStatusVO, error) {
	if request.VenueId == nil || *request.VenueId == "" {
		return nil, errors.New("venue_id is required")
	}

	// 从领域服务获取PO对象
	authPO, err := s.venueAuthService.GetVenueAuthStatus(ctx, *request.VenueId)
	if err != nil {
		return nil, err
	}

	// 使用transfer转换为VO对象
	authVO := s.venueAuthTransfer.PoToVo(authPO)
	return &authVO, nil
}

// QueryAuthCodes 分页查询授权码
func (s *VenueAuthAppServiceImpl) QueryAuthCodes(ctx *gin.Context, request req.QueryVenueAuthCodeReqDto) (*[]vo.VenueAuthCodeVO, int64, error) {
	// 使用现有的VenueAuthCodeService进行分页查询
	venueAuthCodeService := impl.VenueAuthCodeService{}
	list, total, err := venueAuthCodeService.FindAllVenueAuthCodeWithPagination(ctx, &request)
	if err != nil {
		return nil, 0, err
	}

	// 转换为VO
	voList := make([]vo.VenueAuthCodeVO, 0, len(*list))
	for _, po := range *list {
		voList = append(voList, venueAuthCodeService.ConvertToVenueAuthCodeVO(ctx, po))
	}

	return &voList, total, nil
}

// QueryNonSystemAuthCodes 查询非系统创建的授权码（无分页）
func (s *VenueAuthAppServiceImpl) QueryNonSystemAuthCodes(ctx *gin.Context, request req.QueryNonSystemAuthCodeReqDto) (*[]vo.VenueAuthCodeVO, error) {
	// 构造查询参数，过滤掉系统创建的授权码
	queryReq := req.QueryVenueAuthCodeReqDto{
		Status: request.Status, // 使用传入的status参数
	}

	// 使用现有的VenueAuthCodeService进行查询（不分页）
	venueAuthCodeService := impl.VenueAuthCodeService{}
	list, err := venueAuthCodeService.FindAllVenueAuthCode(ctx, &queryReq)
	if err != nil {
		return nil, err
	}

	// 过滤掉CreatorId为"system"的记录
	filteredList := make([]po.VenueAuthCode, 0)
	for _, po := range *list {
		if po.CreatorId != nil && *po.CreatorId != "system" {
			filteredList = append(filteredList, po)
		}
	}

	// 转换为VO并添加门店信息
	voList := make([]vo.VenueAuthCodeVO, 0, len(filteredList))
	for _, authCodePO := range filteredList {
		// 先转换授权码为VO
		authCodeVO := venueAuthCodeService.ConvertToVenueAuthCodeVO(ctx, authCodePO)

		// 如果有门店ID，查询门店信息
		if authCodePO.VenueId != nil && *authCodePO.VenueId != "" {
			venuePO, err := s.venueService.FindVenueById(ctx, *authCodePO.VenueId)
			if err == nil && venuePO != nil {
				// 转换门店PO为VO
				venueVO := s.venueService.ConvertToVenueVO(ctx, *venuePO)
				authCodeVO.VenueVO = &venueVO
			}
		}

		voList = append(voList, authCodeVO)
	}

	return &voList, nil
}
