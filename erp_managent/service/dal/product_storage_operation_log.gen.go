// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newProductStorageOperationLog(db *gorm.DB, opts ...gen.DOOption) productStorageOperationLog {
	_productStorageOperationLog := productStorageOperationLog{}

	_productStorageOperationLog.productStorageOperationLogDo.UseDB(db, opts...)
	_productStorageOperationLog.productStorageOperationLogDo.UseModel(&po.ProductStorageOperationLog{})

	tableName := _productStorageOperationLog.productStorageOperationLogDo.TableName()
	_productStorageOperationLog.ALL = field.NewAsterisk(tableName)
	_productStorageOperationLog.Id = field.NewString(tableName, "id")
	_productStorageOperationLog.StorageId = field.NewString(tableName, "storage_id")
	_productStorageOperationLog.OrderNo = field.NewString(tableName, "order_no")
	_productStorageOperationLog.OperationType = field.NewString(tableName, "operation_type")
	_productStorageOperationLog.OperationName = field.NewString(tableName, "operation_name")
	_productStorageOperationLog.OperationTime = field.NewInt64(tableName, "operation_time")
	_productStorageOperationLog.Quantity = field.NewInt(tableName, "quantity")
	_productStorageOperationLog.OperatorId = field.NewString(tableName, "operator_id")
	_productStorageOperationLog.OperatorName = field.NewString(tableName, "operator_name")
	_productStorageOperationLog.BalanceQty = field.NewInt(tableName, "balance_qty")
	_productStorageOperationLog.Remark = field.NewString(tableName, "remark")
	_productStorageOperationLog.RoomName = field.NewString(tableName, "room_name")
	_productStorageOperationLog.DeliveryRoomName = field.NewString(tableName, "delivery_room_name")
	_productStorageOperationLog.VenueId = field.NewString(tableName, "venue_id")
	_productStorageOperationLog.VenueName = field.NewString(tableName, "venue_name")
	_productStorageOperationLog.BusinessDay = field.NewInt64(tableName, "business_day")
	_productStorageOperationLog.BusinessDayStr = field.NewString(tableName, "business_day_str")
	_productStorageOperationLog.BusinessStartHour = field.NewString(tableName, "business_start_hour")
	_productStorageOperationLog.BusinessEndHour = field.NewString(tableName, "business_end_hour")
	_productStorageOperationLog.Ctime = field.NewInt64(tableName, "ctime")
	_productStorageOperationLog.Utime = field.NewInt64(tableName, "utime")
	_productStorageOperationLog.State = field.NewInt(tableName, "state")
	_productStorageOperationLog.Version = field.NewInt(tableName, "version")

	_productStorageOperationLog.fillFieldMap()

	return _productStorageOperationLog
}

type productStorageOperationLog struct {
	productStorageOperationLogDo

	ALL               field.Asterisk
	Id                field.String
	StorageId         field.String
	OrderNo           field.String
	OperationType     field.String
	OperationName     field.String
	OperationTime     field.Int64
	Quantity          field.Int
	OperatorId        field.String
	OperatorName      field.String
	BalanceQty        field.Int
	Remark            field.String
	RoomName          field.String
	DeliveryRoomName  field.String
	VenueId           field.String
	VenueName         field.String
	BusinessDay       field.Int64
	BusinessDayStr    field.String
	BusinessStartHour field.String
	BusinessEndHour   field.String
	Ctime             field.Int64
	Utime             field.Int64
	State             field.Int
	Version           field.Int

	fieldMap map[string]field.Expr
}

func (p productStorageOperationLog) Table(newTableName string) *productStorageOperationLog {
	p.productStorageOperationLogDo.UseTable(newTableName)
	return p.updateTableName(newTableName)
}

func (p productStorageOperationLog) As(alias string) *productStorageOperationLog {
	p.productStorageOperationLogDo.DO = *(p.productStorageOperationLogDo.As(alias).(*gen.DO))
	return p.updateTableName(alias)
}

func (p *productStorageOperationLog) updateTableName(table string) *productStorageOperationLog {
	p.ALL = field.NewAsterisk(table)
	p.Id = field.NewString(table, "id")
	p.StorageId = field.NewString(table, "storage_id")
	p.OrderNo = field.NewString(table, "order_no")
	p.OperationType = field.NewString(table, "operation_type")
	p.OperationName = field.NewString(table, "operation_name")
	p.OperationTime = field.NewInt64(table, "operation_time")
	p.Quantity = field.NewInt(table, "quantity")
	p.OperatorId = field.NewString(table, "operator_id")
	p.OperatorName = field.NewString(table, "operator_name")
	p.BalanceQty = field.NewInt(table, "balance_qty")
	p.Remark = field.NewString(table, "remark")
	p.RoomName = field.NewString(table, "room_name")
	p.DeliveryRoomName = field.NewString(table, "delivery_room_name")
	p.VenueId = field.NewString(table, "venue_id")
	p.VenueName = field.NewString(table, "venue_name")
	p.BusinessDay = field.NewInt64(table, "business_day")
	p.BusinessDayStr = field.NewString(table, "business_day_str")
	p.BusinessStartHour = field.NewString(table, "business_start_hour")
	p.BusinessEndHour = field.NewString(table, "business_end_hour")
	p.Ctime = field.NewInt64(table, "ctime")
	p.Utime = field.NewInt64(table, "utime")
	p.State = field.NewInt(table, "state")
	p.Version = field.NewInt(table, "version")

	p.fillFieldMap()

	return p
}

func (p *productStorageOperationLog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := p.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (p *productStorageOperationLog) fillFieldMap() {
	p.fieldMap = make(map[string]field.Expr, 23)
	p.fieldMap["id"] = p.Id
	p.fieldMap["storage_id"] = p.StorageId
	p.fieldMap["order_no"] = p.OrderNo
	p.fieldMap["operation_type"] = p.OperationType
	p.fieldMap["operation_name"] = p.OperationName
	p.fieldMap["operation_time"] = p.OperationTime
	p.fieldMap["quantity"] = p.Quantity
	p.fieldMap["operator_id"] = p.OperatorId
	p.fieldMap["operator_name"] = p.OperatorName
	p.fieldMap["balance_qty"] = p.BalanceQty
	p.fieldMap["remark"] = p.Remark
	p.fieldMap["room_name"] = p.RoomName
	p.fieldMap["delivery_room_name"] = p.DeliveryRoomName
	p.fieldMap["venue_id"] = p.VenueId
	p.fieldMap["venue_name"] = p.VenueName
	p.fieldMap["business_day"] = p.BusinessDay
	p.fieldMap["business_day_str"] = p.BusinessDayStr
	p.fieldMap["business_start_hour"] = p.BusinessStartHour
	p.fieldMap["business_end_hour"] = p.BusinessEndHour
	p.fieldMap["ctime"] = p.Ctime
	p.fieldMap["utime"] = p.Utime
	p.fieldMap["state"] = p.State
	p.fieldMap["version"] = p.Version
}

func (p productStorageOperationLog) clone(db *gorm.DB) productStorageOperationLog {
	p.productStorageOperationLogDo.ReplaceConnPool(db.Statement.ConnPool)
	return p
}

func (p productStorageOperationLog) replaceDB(db *gorm.DB) productStorageOperationLog {
	p.productStorageOperationLogDo.ReplaceDB(db)
	return p
}

type productStorageOperationLogDo struct{ gen.DO }

type IProductStorageOperationLogDo interface {
	gen.SubQuery
	Debug() IProductStorageOperationLogDo
	WithContext(ctx context.Context) IProductStorageOperationLogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IProductStorageOperationLogDo
	WriteDB() IProductStorageOperationLogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IProductStorageOperationLogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IProductStorageOperationLogDo
	Not(conds ...gen.Condition) IProductStorageOperationLogDo
	Or(conds ...gen.Condition) IProductStorageOperationLogDo
	Select(conds ...field.Expr) IProductStorageOperationLogDo
	Where(conds ...gen.Condition) IProductStorageOperationLogDo
	Order(conds ...field.Expr) IProductStorageOperationLogDo
	Distinct(cols ...field.Expr) IProductStorageOperationLogDo
	Omit(cols ...field.Expr) IProductStorageOperationLogDo
	Join(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo
	Group(cols ...field.Expr) IProductStorageOperationLogDo
	Having(conds ...gen.Condition) IProductStorageOperationLogDo
	Limit(limit int) IProductStorageOperationLogDo
	Offset(offset int) IProductStorageOperationLogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageOperationLogDo
	Unscoped() IProductStorageOperationLogDo
	Create(values ...*po.ProductStorageOperationLog) error
	CreateInBatches(values []*po.ProductStorageOperationLog, batchSize int) error
	Save(values ...*po.ProductStorageOperationLog) error
	First() (*po.ProductStorageOperationLog, error)
	Take() (*po.ProductStorageOperationLog, error)
	Last() (*po.ProductStorageOperationLog, error)
	Find() ([]*po.ProductStorageOperationLog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorageOperationLog, err error)
	FindInBatches(result *[]*po.ProductStorageOperationLog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.ProductStorageOperationLog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IProductStorageOperationLogDo
	Assign(attrs ...field.AssignExpr) IProductStorageOperationLogDo
	Joins(fields ...field.RelationField) IProductStorageOperationLogDo
	Preload(fields ...field.RelationField) IProductStorageOperationLogDo
	FirstOrInit() (*po.ProductStorageOperationLog, error)
	FirstOrCreate() (*po.ProductStorageOperationLog, error)
	FindByPage(offset int, limit int) (result []*po.ProductStorageOperationLog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IProductStorageOperationLogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.ProductStorageOperationLog, err error)
	DazzyQueryByAny(field string, value string) (result []*po.ProductStorageOperationLog, err error)
	QueryByVenueId(venueId string) (result []*po.ProductStorageOperationLog, err error)
}

// DELETE FROM @@table WHERE id = @id
func (p productStorageOperationLogDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM product_storage_operation_log WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (p productStorageOperationLogDo) QueryOneByID(id string) (result *po.ProductStorageOperationLog, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM product_storage_operation_log WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (p productStorageOperationLogDo) DazzyQueryByAny(field string, value string) (result []*po.ProductStorageOperationLog, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM product_storage_operation_log WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (p productStorageOperationLogDo) QueryByVenueId(venueId string) (result []*po.ProductStorageOperationLog, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM product_storage_operation_log WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = p.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (p productStorageOperationLogDo) Debug() IProductStorageOperationLogDo {
	return p.withDO(p.DO.Debug())
}

func (p productStorageOperationLogDo) WithContext(ctx context.Context) IProductStorageOperationLogDo {
	return p.withDO(p.DO.WithContext(ctx))
}

func (p productStorageOperationLogDo) ReadDB() IProductStorageOperationLogDo {
	return p.Clauses(dbresolver.Read)
}

func (p productStorageOperationLogDo) WriteDB() IProductStorageOperationLogDo {
	return p.Clauses(dbresolver.Write)
}

func (p productStorageOperationLogDo) Session(config *gorm.Session) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Session(config))
}

func (p productStorageOperationLogDo) Clauses(conds ...clause.Expression) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Clauses(conds...))
}

func (p productStorageOperationLogDo) Returning(value interface{}, columns ...string) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Returning(value, columns...))
}

func (p productStorageOperationLogDo) Not(conds ...gen.Condition) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Not(conds...))
}

func (p productStorageOperationLogDo) Or(conds ...gen.Condition) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Or(conds...))
}

func (p productStorageOperationLogDo) Select(conds ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Select(conds...))
}

func (p productStorageOperationLogDo) Where(conds ...gen.Condition) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Where(conds...))
}

func (p productStorageOperationLogDo) Order(conds ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Order(conds...))
}

func (p productStorageOperationLogDo) Distinct(cols ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Distinct(cols...))
}

func (p productStorageOperationLogDo) Omit(cols ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Omit(cols...))
}

func (p productStorageOperationLogDo) Join(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Join(table, on...))
}

func (p productStorageOperationLogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.LeftJoin(table, on...))
}

func (p productStorageOperationLogDo) RightJoin(table schema.Tabler, on ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.RightJoin(table, on...))
}

func (p productStorageOperationLogDo) Group(cols ...field.Expr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Group(cols...))
}

func (p productStorageOperationLogDo) Having(conds ...gen.Condition) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Having(conds...))
}

func (p productStorageOperationLogDo) Limit(limit int) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Limit(limit))
}

func (p productStorageOperationLogDo) Offset(offset int) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Offset(offset))
}

func (p productStorageOperationLogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Scopes(funcs...))
}

func (p productStorageOperationLogDo) Unscoped() IProductStorageOperationLogDo {
	return p.withDO(p.DO.Unscoped())
}

func (p productStorageOperationLogDo) Create(values ...*po.ProductStorageOperationLog) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Create(values)
}

func (p productStorageOperationLogDo) CreateInBatches(values []*po.ProductStorageOperationLog, batchSize int) error {
	return p.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (p productStorageOperationLogDo) Save(values ...*po.ProductStorageOperationLog) error {
	if len(values) == 0 {
		return nil
	}
	return p.DO.Save(values)
}

func (p productStorageOperationLogDo) First() (*po.ProductStorageOperationLog, error) {
	if result, err := p.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOperationLog), nil
	}
}

func (p productStorageOperationLogDo) Take() (*po.ProductStorageOperationLog, error) {
	if result, err := p.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOperationLog), nil
	}
}

func (p productStorageOperationLogDo) Last() (*po.ProductStorageOperationLog, error) {
	if result, err := p.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOperationLog), nil
	}
}

func (p productStorageOperationLogDo) Find() ([]*po.ProductStorageOperationLog, error) {
	result, err := p.DO.Find()
	return result.([]*po.ProductStorageOperationLog), err
}

func (p productStorageOperationLogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.ProductStorageOperationLog, err error) {
	buf := make([]*po.ProductStorageOperationLog, 0, batchSize)
	err = p.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (p productStorageOperationLogDo) FindInBatches(result *[]*po.ProductStorageOperationLog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return p.DO.FindInBatches(result, batchSize, fc)
}

func (p productStorageOperationLogDo) Attrs(attrs ...field.AssignExpr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Attrs(attrs...))
}

func (p productStorageOperationLogDo) Assign(attrs ...field.AssignExpr) IProductStorageOperationLogDo {
	return p.withDO(p.DO.Assign(attrs...))
}

func (p productStorageOperationLogDo) Joins(fields ...field.RelationField) IProductStorageOperationLogDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Joins(_f))
	}
	return &p
}

func (p productStorageOperationLogDo) Preload(fields ...field.RelationField) IProductStorageOperationLogDo {
	for _, _f := range fields {
		p = *p.withDO(p.DO.Preload(_f))
	}
	return &p
}

func (p productStorageOperationLogDo) FirstOrInit() (*po.ProductStorageOperationLog, error) {
	if result, err := p.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOperationLog), nil
	}
}

func (p productStorageOperationLogDo) FirstOrCreate() (*po.ProductStorageOperationLog, error) {
	if result, err := p.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.ProductStorageOperationLog), nil
	}
}

func (p productStorageOperationLogDo) FindByPage(offset int, limit int) (result []*po.ProductStorageOperationLog, count int64, err error) {
	result, err = p.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = p.Offset(-1).Limit(-1).Count()
	return
}

func (p productStorageOperationLogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = p.Count()
	if err != nil {
		return
	}

	err = p.Offset(offset).Limit(limit).Scan(result)
	return
}

func (p productStorageOperationLogDo) Scan(result interface{}) (err error) {
	return p.DO.Scan(result)
}

func (p productStorageOperationLogDo) Delete(models ...*po.ProductStorageOperationLog) (result gen.ResultInfo, err error) {
	return p.DO.Delete(models)
}

func (p *productStorageOperationLogDo) withDO(do gen.Dao) *productStorageOperationLogDo {
	p.DO = *do.(*gen.DO)
	return p
}
