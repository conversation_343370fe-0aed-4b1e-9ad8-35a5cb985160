// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q                          = new(Query)
	BusinessStaff              *businessStaff
	EmployeeRoleAssignment     *employeeRoleAssignment
	Member                     *member
	MemberCardLevel            *memberCardLevel
	MemberCardOperation        *memberCardOperation
	MemberConsumption          *memberConsumption
	MemberDay                  *memberDay
	MemberOrder                *memberOrder
	MemberPoints               *memberPoints
	MemberRechargePackage      *memberRechargePackage
	MemberTransferMoney        *memberTransferMoney
	PayBill                    *payBill
	PermissionResource         *permissionResource
	PermissionRole             *permissionRole
	PointsExchangeRecord       *pointsExchangeRecord
	PointsGoods                *pointsGoods
	ProductStorage             *productStorage
	ProductStorageOperationLog *productStorageOperationLog
	ProductStorageOrder        *productStorageOrder
	ProductWithdraw            *productWithdraw
	ProductWithdrawOrder       *productWithdrawOrder
	RolePermission             *rolePermission
	RolePermissionConfig       *rolePermissionConfig
	RolePermissionTemplate     *rolePermissionTemplate
	SmsVerifyConfig            *smsVerifyConfig
	SystemOperationRecord      *systemOperationRecord
	TimeCard                   *timeCard
	TimeCardVerify             *timeCardVerify
	Venue                      *venue
	VenueAndMember             *venueAndMember
	VenueAuthCode              *venueAuthCode
	WineStorageSetting         *wineStorageSetting
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	BusinessStaff = &Q.BusinessStaff
	EmployeeRoleAssignment = &Q.EmployeeRoleAssignment
	Member = &Q.Member
	MemberCardLevel = &Q.MemberCardLevel
	MemberCardOperation = &Q.MemberCardOperation
	MemberConsumption = &Q.MemberConsumption
	MemberDay = &Q.MemberDay
	MemberOrder = &Q.MemberOrder
	MemberPoints = &Q.MemberPoints
	MemberRechargePackage = &Q.MemberRechargePackage
	MemberTransferMoney = &Q.MemberTransferMoney
	PayBill = &Q.PayBill
	PermissionResource = &Q.PermissionResource
	PermissionRole = &Q.PermissionRole
	PointsExchangeRecord = &Q.PointsExchangeRecord
	PointsGoods = &Q.PointsGoods
	ProductStorage = &Q.ProductStorage
	ProductStorageOperationLog = &Q.ProductStorageOperationLog
	ProductStorageOrder = &Q.ProductStorageOrder
	ProductWithdraw = &Q.ProductWithdraw
	ProductWithdrawOrder = &Q.ProductWithdrawOrder
	RolePermission = &Q.RolePermission
	RolePermissionConfig = &Q.RolePermissionConfig
	RolePermissionTemplate = &Q.RolePermissionTemplate
	SmsVerifyConfig = &Q.SmsVerifyConfig
	SystemOperationRecord = &Q.SystemOperationRecord
	TimeCard = &Q.TimeCard
	TimeCardVerify = &Q.TimeCardVerify
	Venue = &Q.Venue
	VenueAndMember = &Q.VenueAndMember
	VenueAuthCode = &Q.VenueAuthCode
	WineStorageSetting = &Q.WineStorageSetting
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:                         db,
		BusinessStaff:              newBusinessStaff(db, opts...),
		EmployeeRoleAssignment:     newEmployeeRoleAssignment(db, opts...),
		Member:                     newMember(db, opts...),
		MemberCardLevel:            newMemberCardLevel(db, opts...),
		MemberCardOperation:        newMemberCardOperation(db, opts...),
		MemberConsumption:          newMemberConsumption(db, opts...),
		MemberDay:                  newMemberDay(db, opts...),
		MemberOrder:                newMemberOrder(db, opts...),
		MemberPoints:               newMemberPoints(db, opts...),
		MemberRechargePackage:      newMemberRechargePackage(db, opts...),
		MemberTransferMoney:        newMemberTransferMoney(db, opts...),
		PayBill:                    newPayBill(db, opts...),
		PermissionResource:         newPermissionResource(db, opts...),
		PermissionRole:             newPermissionRole(db, opts...),
		PointsExchangeRecord:       newPointsExchangeRecord(db, opts...),
		PointsGoods:                newPointsGoods(db, opts...),
		ProductStorage:             newProductStorage(db, opts...),
		ProductStorageOperationLog: newProductStorageOperationLog(db, opts...),
		ProductStorageOrder:        newProductStorageOrder(db, opts...),
		ProductWithdraw:            newProductWithdraw(db, opts...),
		ProductWithdrawOrder:       newProductWithdrawOrder(db, opts...),
		RolePermission:             newRolePermission(db, opts...),
		RolePermissionConfig:       newRolePermissionConfig(db, opts...),
		RolePermissionTemplate:     newRolePermissionTemplate(db, opts...),
		SmsVerifyConfig:            newSmsVerifyConfig(db, opts...),
		SystemOperationRecord:      newSystemOperationRecord(db, opts...),
		TimeCard:                   newTimeCard(db, opts...),
		TimeCardVerify:             newTimeCardVerify(db, opts...),
		Venue:                      newVenue(db, opts...),
		VenueAndMember:             newVenueAndMember(db, opts...),
		VenueAuthCode:              newVenueAuthCode(db, opts...),
		WineStorageSetting:         newWineStorageSetting(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	BusinessStaff              businessStaff
	EmployeeRoleAssignment     employeeRoleAssignment
	Member                     member
	MemberCardLevel            memberCardLevel
	MemberCardOperation        memberCardOperation
	MemberConsumption          memberConsumption
	MemberDay                  memberDay
	MemberOrder                memberOrder
	MemberPoints               memberPoints
	MemberRechargePackage      memberRechargePackage
	MemberTransferMoney        memberTransferMoney
	PayBill                    payBill
	PermissionResource         permissionResource
	PermissionRole             permissionRole
	PointsExchangeRecord       pointsExchangeRecord
	PointsGoods                pointsGoods
	ProductStorage             productStorage
	ProductStorageOperationLog productStorageOperationLog
	ProductStorageOrder        productStorageOrder
	ProductWithdraw            productWithdraw
	ProductWithdrawOrder       productWithdrawOrder
	RolePermission             rolePermission
	RolePermissionConfig       rolePermissionConfig
	RolePermissionTemplate     rolePermissionTemplate
	SmsVerifyConfig            smsVerifyConfig
	SystemOperationRecord      systemOperationRecord
	TimeCard                   timeCard
	TimeCardVerify             timeCardVerify
	Venue                      venue
	VenueAndMember             venueAndMember
	VenueAuthCode              venueAuthCode
	WineStorageSetting         wineStorageSetting
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		BusinessStaff:              q.BusinessStaff.clone(db),
		EmployeeRoleAssignment:     q.EmployeeRoleAssignment.clone(db),
		Member:                     q.Member.clone(db),
		MemberCardLevel:            q.MemberCardLevel.clone(db),
		MemberCardOperation:        q.MemberCardOperation.clone(db),
		MemberConsumption:          q.MemberConsumption.clone(db),
		MemberDay:                  q.MemberDay.clone(db),
		MemberOrder:                q.MemberOrder.clone(db),
		MemberPoints:               q.MemberPoints.clone(db),
		MemberRechargePackage:      q.MemberRechargePackage.clone(db),
		MemberTransferMoney:        q.MemberTransferMoney.clone(db),
		PayBill:                    q.PayBill.clone(db),
		PermissionResource:         q.PermissionResource.clone(db),
		PermissionRole:             q.PermissionRole.clone(db),
		PointsExchangeRecord:       q.PointsExchangeRecord.clone(db),
		PointsGoods:                q.PointsGoods.clone(db),
		ProductStorage:             q.ProductStorage.clone(db),
		ProductStorageOperationLog: q.ProductStorageOperationLog.clone(db),
		ProductStorageOrder:        q.ProductStorageOrder.clone(db),
		ProductWithdraw:            q.ProductWithdraw.clone(db),
		ProductWithdrawOrder:       q.ProductWithdrawOrder.clone(db),
		RolePermission:             q.RolePermission.clone(db),
		RolePermissionConfig:       q.RolePermissionConfig.clone(db),
		RolePermissionTemplate:     q.RolePermissionTemplate.clone(db),
		SmsVerifyConfig:            q.SmsVerifyConfig.clone(db),
		SystemOperationRecord:      q.SystemOperationRecord.clone(db),
		TimeCard:                   q.TimeCard.clone(db),
		TimeCardVerify:             q.TimeCardVerify.clone(db),
		Venue:                      q.Venue.clone(db),
		VenueAndMember:             q.VenueAndMember.clone(db),
		VenueAuthCode:              q.VenueAuthCode.clone(db),
		WineStorageSetting:         q.WineStorageSetting.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:                         db,
		BusinessStaff:              q.BusinessStaff.replaceDB(db),
		EmployeeRoleAssignment:     q.EmployeeRoleAssignment.replaceDB(db),
		Member:                     q.Member.replaceDB(db),
		MemberCardLevel:            q.MemberCardLevel.replaceDB(db),
		MemberCardOperation:        q.MemberCardOperation.replaceDB(db),
		MemberConsumption:          q.MemberConsumption.replaceDB(db),
		MemberDay:                  q.MemberDay.replaceDB(db),
		MemberOrder:                q.MemberOrder.replaceDB(db),
		MemberPoints:               q.MemberPoints.replaceDB(db),
		MemberRechargePackage:      q.MemberRechargePackage.replaceDB(db),
		MemberTransferMoney:        q.MemberTransferMoney.replaceDB(db),
		PayBill:                    q.PayBill.replaceDB(db),
		PermissionResource:         q.PermissionResource.replaceDB(db),
		PermissionRole:             q.PermissionRole.replaceDB(db),
		PointsExchangeRecord:       q.PointsExchangeRecord.replaceDB(db),
		PointsGoods:                q.PointsGoods.replaceDB(db),
		ProductStorage:             q.ProductStorage.replaceDB(db),
		ProductStorageOperationLog: q.ProductStorageOperationLog.replaceDB(db),
		ProductStorageOrder:        q.ProductStorageOrder.replaceDB(db),
		ProductWithdraw:            q.ProductWithdraw.replaceDB(db),
		ProductWithdrawOrder:       q.ProductWithdrawOrder.replaceDB(db),
		RolePermission:             q.RolePermission.replaceDB(db),
		RolePermissionConfig:       q.RolePermissionConfig.replaceDB(db),
		RolePermissionTemplate:     q.RolePermissionTemplate.replaceDB(db),
		SmsVerifyConfig:            q.SmsVerifyConfig.replaceDB(db),
		SystemOperationRecord:      q.SystemOperationRecord.replaceDB(db),
		TimeCard:                   q.TimeCard.replaceDB(db),
		TimeCardVerify:             q.TimeCardVerify.replaceDB(db),
		Venue:                      q.Venue.replaceDB(db),
		VenueAndMember:             q.VenueAndMember.replaceDB(db),
		VenueAuthCode:              q.VenueAuthCode.replaceDB(db),
		WineStorageSetting:         q.WineStorageSetting.replaceDB(db),
	}
}

type queryCtx struct {
	BusinessStaff              IBusinessStaffDo
	EmployeeRoleAssignment     IEmployeeRoleAssignmentDo
	Member                     IMemberDo
	MemberCardLevel            IMemberCardLevelDo
	MemberCardOperation        IMemberCardOperationDo
	MemberConsumption          IMemberConsumptionDo
	MemberDay                  IMemberDayDo
	MemberOrder                IMemberOrderDo
	MemberPoints               IMemberPointsDo
	MemberRechargePackage      IMemberRechargePackageDo
	MemberTransferMoney        IMemberTransferMoneyDo
	PayBill                    IPayBillDo
	PermissionResource         IPermissionResourceDo
	PermissionRole             IPermissionRoleDo
	PointsExchangeRecord       IPointsExchangeRecordDo
	PointsGoods                IPointsGoodsDo
	ProductStorage             IProductStorageDo
	ProductStorageOperationLog IProductStorageOperationLogDo
	ProductStorageOrder        IProductStorageOrderDo
	ProductWithdraw            IProductWithdrawDo
	ProductWithdrawOrder       IProductWithdrawOrderDo
	RolePermission             IRolePermissionDo
	RolePermissionConfig       IRolePermissionConfigDo
	RolePermissionTemplate     IRolePermissionTemplateDo
	SmsVerifyConfig            ISmsVerifyConfigDo
	SystemOperationRecord      ISystemOperationRecordDo
	TimeCard                   ITimeCardDo
	TimeCardVerify             ITimeCardVerifyDo
	Venue                      IVenueDo
	VenueAndMember             IVenueAndMemberDo
	VenueAuthCode              IVenueAuthCodeDo
	WineStorageSetting         IWineStorageSettingDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		BusinessStaff:              q.BusinessStaff.WithContext(ctx),
		EmployeeRoleAssignment:     q.EmployeeRoleAssignment.WithContext(ctx),
		Member:                     q.Member.WithContext(ctx),
		MemberCardLevel:            q.MemberCardLevel.WithContext(ctx),
		MemberCardOperation:        q.MemberCardOperation.WithContext(ctx),
		MemberConsumption:          q.MemberConsumption.WithContext(ctx),
		MemberDay:                  q.MemberDay.WithContext(ctx),
		MemberOrder:                q.MemberOrder.WithContext(ctx),
		MemberPoints:               q.MemberPoints.WithContext(ctx),
		MemberRechargePackage:      q.MemberRechargePackage.WithContext(ctx),
		MemberTransferMoney:        q.MemberTransferMoney.WithContext(ctx),
		PayBill:                    q.PayBill.WithContext(ctx),
		PermissionResource:         q.PermissionResource.WithContext(ctx),
		PermissionRole:             q.PermissionRole.WithContext(ctx),
		PointsExchangeRecord:       q.PointsExchangeRecord.WithContext(ctx),
		PointsGoods:                q.PointsGoods.WithContext(ctx),
		ProductStorage:             q.ProductStorage.WithContext(ctx),
		ProductStorageOperationLog: q.ProductStorageOperationLog.WithContext(ctx),
		ProductStorageOrder:        q.ProductStorageOrder.WithContext(ctx),
		ProductWithdraw:            q.ProductWithdraw.WithContext(ctx),
		ProductWithdrawOrder:       q.ProductWithdrawOrder.WithContext(ctx),
		RolePermission:             q.RolePermission.WithContext(ctx),
		RolePermissionConfig:       q.RolePermissionConfig.WithContext(ctx),
		RolePermissionTemplate:     q.RolePermissionTemplate.WithContext(ctx),
		SmsVerifyConfig:            q.SmsVerifyConfig.WithContext(ctx),
		SystemOperationRecord:      q.SystemOperationRecord.WithContext(ctx),
		TimeCard:                   q.TimeCard.WithContext(ctx),
		TimeCardVerify:             q.TimeCardVerify.WithContext(ctx),
		Venue:                      q.Venue.WithContext(ctx),
		VenueAndMember:             q.VenueAndMember.WithContext(ctx),
		VenueAuthCode:              q.VenueAuthCode.WithContext(ctx),
		WineStorageSetting:         q.WineStorageSetting.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
