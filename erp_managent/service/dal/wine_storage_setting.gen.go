// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newWineStorageSetting(db *gorm.DB, opts ...gen.DOOption) wineStorageSetting {
	_wineStorageSetting := wineStorageSetting{}

	_wineStorageSetting.wineStorageSettingDo.UseDB(db, opts...)
	_wineStorageSetting.wineStorageSettingDo.UseModel(&po.WineStorageSetting{})

	tableName := _wineStorageSetting.wineStorageSettingDo.TableName()
	_wineStorageSetting.ALL = field.NewAsterisk(tableName)
	_wineStorageSetting.Id = field.NewString(tableName, "id")
	_wineStorageSetting.VenueId = field.NewString(tableName, "venue_id")
	_wineStorageSetting.StorageDays = field.NewInt(tableName, "storage_days")
	_wineStorageSetting.RenewalTimes = field.NewInt(tableName, "renewal_times")
	_wineStorageSetting.RenewalDays = field.NewInt(tableName, "renewal_days")
	_wineStorageSetting.CustomerNotificationDays = field.NewInt(tableName, "customer_notification_days")
	_wineStorageSetting.MerchantNotificationDays = field.NewInt(tableName, "merchant_notification_days")
	_wineStorageSetting.MerchantExpirationReminder = field.NewString(tableName, "merchant_expiration_reminder")
	_wineStorageSetting.AgentExpirationReminder = field.NewString(tableName, "agent_expiration_reminder")
	_wineStorageSetting.AutoConfiscate = field.NewBool(tableName, "auto_confiscate")
	_wineStorageSetting.OverdueWithdrawalLimit = field.NewInt(tableName, "overdue_withdrawal_limit")
	_wineStorageSetting.AutoConfiscateDays = field.NewInt(tableName, "auto_confiscate_days")
	_wineStorageSetting.ConfiscateWarehouse = field.NewString(tableName, "confiscate_warehouse")
	_wineStorageSetting.IsInventoryAssociated = field.NewBool(tableName, "is_inventory_associated")
	_wineStorageSetting.Ctime = field.NewInt64(tableName, "ctime")
	_wineStorageSetting.Utime = field.NewInt64(tableName, "utime")
	_wineStorageSetting.State = field.NewInt(tableName, "state")
	_wineStorageSetting.Version = field.NewInt(tableName, "version")

	_wineStorageSetting.fillFieldMap()

	return _wineStorageSetting
}

type wineStorageSetting struct {
	wineStorageSettingDo

	ALL                        field.Asterisk
	Id                         field.String
	VenueId                    field.String
	StorageDays                field.Int
	RenewalTimes               field.Int
	RenewalDays                field.Int
	CustomerNotificationDays   field.Int
	MerchantNotificationDays   field.Int
	MerchantExpirationReminder field.String
	AgentExpirationReminder    field.String
	AutoConfiscate             field.Bool
	OverdueWithdrawalLimit     field.Int
	AutoConfiscateDays         field.Int
	ConfiscateWarehouse        field.String
	IsInventoryAssociated      field.Bool
	Ctime                      field.Int64
	Utime                      field.Int64
	State                      field.Int
	Version                    field.Int

	fieldMap map[string]field.Expr
}

func (w wineStorageSetting) Table(newTableName string) *wineStorageSetting {
	w.wineStorageSettingDo.UseTable(newTableName)
	return w.updateTableName(newTableName)
}

func (w wineStorageSetting) As(alias string) *wineStorageSetting {
	w.wineStorageSettingDo.DO = *(w.wineStorageSettingDo.As(alias).(*gen.DO))
	return w.updateTableName(alias)
}

func (w *wineStorageSetting) updateTableName(table string) *wineStorageSetting {
	w.ALL = field.NewAsterisk(table)
	w.Id = field.NewString(table, "id")
	w.VenueId = field.NewString(table, "venue_id")
	w.StorageDays = field.NewInt(table, "storage_days")
	w.RenewalTimes = field.NewInt(table, "renewal_times")
	w.RenewalDays = field.NewInt(table, "renewal_days")
	w.CustomerNotificationDays = field.NewInt(table, "customer_notification_days")
	w.MerchantNotificationDays = field.NewInt(table, "merchant_notification_days")
	w.MerchantExpirationReminder = field.NewString(table, "merchant_expiration_reminder")
	w.AgentExpirationReminder = field.NewString(table, "agent_expiration_reminder")
	w.AutoConfiscate = field.NewBool(table, "auto_confiscate")
	w.OverdueWithdrawalLimit = field.NewInt(table, "overdue_withdrawal_limit")
	w.AutoConfiscateDays = field.NewInt(table, "auto_confiscate_days")
	w.ConfiscateWarehouse = field.NewString(table, "confiscate_warehouse")
	w.IsInventoryAssociated = field.NewBool(table, "is_inventory_associated")
	w.Ctime = field.NewInt64(table, "ctime")
	w.Utime = field.NewInt64(table, "utime")
	w.State = field.NewInt(table, "state")
	w.Version = field.NewInt(table, "version")

	w.fillFieldMap()

	return w
}

func (w *wineStorageSetting) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := w.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (w *wineStorageSetting) fillFieldMap() {
	w.fieldMap = make(map[string]field.Expr, 18)
	w.fieldMap["id"] = w.Id
	w.fieldMap["venue_id"] = w.VenueId
	w.fieldMap["storage_days"] = w.StorageDays
	w.fieldMap["renewal_times"] = w.RenewalTimes
	w.fieldMap["renewal_days"] = w.RenewalDays
	w.fieldMap["customer_notification_days"] = w.CustomerNotificationDays
	w.fieldMap["merchant_notification_days"] = w.MerchantNotificationDays
	w.fieldMap["merchant_expiration_reminder"] = w.MerchantExpirationReminder
	w.fieldMap["agent_expiration_reminder"] = w.AgentExpirationReminder
	w.fieldMap["auto_confiscate"] = w.AutoConfiscate
	w.fieldMap["overdue_withdrawal_limit"] = w.OverdueWithdrawalLimit
	w.fieldMap["auto_confiscate_days"] = w.AutoConfiscateDays
	w.fieldMap["confiscate_warehouse"] = w.ConfiscateWarehouse
	w.fieldMap["is_inventory_associated"] = w.IsInventoryAssociated
	w.fieldMap["ctime"] = w.Ctime
	w.fieldMap["utime"] = w.Utime
	w.fieldMap["state"] = w.State
	w.fieldMap["version"] = w.Version
}

func (w wineStorageSetting) clone(db *gorm.DB) wineStorageSetting {
	w.wineStorageSettingDo.ReplaceConnPool(db.Statement.ConnPool)
	return w
}

func (w wineStorageSetting) replaceDB(db *gorm.DB) wineStorageSetting {
	w.wineStorageSettingDo.ReplaceDB(db)
	return w
}

type wineStorageSettingDo struct{ gen.DO }

type IWineStorageSettingDo interface {
	gen.SubQuery
	Debug() IWineStorageSettingDo
	WithContext(ctx context.Context) IWineStorageSettingDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IWineStorageSettingDo
	WriteDB() IWineStorageSettingDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IWineStorageSettingDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IWineStorageSettingDo
	Not(conds ...gen.Condition) IWineStorageSettingDo
	Or(conds ...gen.Condition) IWineStorageSettingDo
	Select(conds ...field.Expr) IWineStorageSettingDo
	Where(conds ...gen.Condition) IWineStorageSettingDo
	Order(conds ...field.Expr) IWineStorageSettingDo
	Distinct(cols ...field.Expr) IWineStorageSettingDo
	Omit(cols ...field.Expr) IWineStorageSettingDo
	Join(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo
	RightJoin(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo
	Group(cols ...field.Expr) IWineStorageSettingDo
	Having(conds ...gen.Condition) IWineStorageSettingDo
	Limit(limit int) IWineStorageSettingDo
	Offset(offset int) IWineStorageSettingDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IWineStorageSettingDo
	Unscoped() IWineStorageSettingDo
	Create(values ...*po.WineStorageSetting) error
	CreateInBatches(values []*po.WineStorageSetting, batchSize int) error
	Save(values ...*po.WineStorageSetting) error
	First() (*po.WineStorageSetting, error)
	Take() (*po.WineStorageSetting, error)
	Last() (*po.WineStorageSetting, error)
	Find() ([]*po.WineStorageSetting, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.WineStorageSetting, err error)
	FindInBatches(result *[]*po.WineStorageSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.WineStorageSetting) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IWineStorageSettingDo
	Assign(attrs ...field.AssignExpr) IWineStorageSettingDo
	Joins(fields ...field.RelationField) IWineStorageSettingDo
	Preload(fields ...field.RelationField) IWineStorageSettingDo
	FirstOrInit() (*po.WineStorageSetting, error)
	FirstOrCreate() (*po.WineStorageSetting, error)
	FindByPage(offset int, limit int) (result []*po.WineStorageSetting, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IWineStorageSettingDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.WineStorageSetting, err error)
	DazzyQueryByAny(field string, value string) (result []*po.WineStorageSetting, err error)
	QueryByVenueId(venueId string) (result []*po.WineStorageSetting, err error)
}

// DELETE FROM @@table WHERE id = @id
func (w wineStorageSettingDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM wine_storage_setting WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (w wineStorageSettingDo) QueryOneByID(id string) (result *po.WineStorageSetting, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM wine_storage_setting WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (w wineStorageSettingDo) DazzyQueryByAny(field string, value string) (result []*po.WineStorageSetting, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM wine_storage_setting WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (w wineStorageSettingDo) QueryByVenueId(venueId string) (result []*po.WineStorageSetting, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM wine_storage_setting WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = w.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (w wineStorageSettingDo) Debug() IWineStorageSettingDo {
	return w.withDO(w.DO.Debug())
}

func (w wineStorageSettingDo) WithContext(ctx context.Context) IWineStorageSettingDo {
	return w.withDO(w.DO.WithContext(ctx))
}

func (w wineStorageSettingDo) ReadDB() IWineStorageSettingDo {
	return w.Clauses(dbresolver.Read)
}

func (w wineStorageSettingDo) WriteDB() IWineStorageSettingDo {
	return w.Clauses(dbresolver.Write)
}

func (w wineStorageSettingDo) Session(config *gorm.Session) IWineStorageSettingDo {
	return w.withDO(w.DO.Session(config))
}

func (w wineStorageSettingDo) Clauses(conds ...clause.Expression) IWineStorageSettingDo {
	return w.withDO(w.DO.Clauses(conds...))
}

func (w wineStorageSettingDo) Returning(value interface{}, columns ...string) IWineStorageSettingDo {
	return w.withDO(w.DO.Returning(value, columns...))
}

func (w wineStorageSettingDo) Not(conds ...gen.Condition) IWineStorageSettingDo {
	return w.withDO(w.DO.Not(conds...))
}

func (w wineStorageSettingDo) Or(conds ...gen.Condition) IWineStorageSettingDo {
	return w.withDO(w.DO.Or(conds...))
}

func (w wineStorageSettingDo) Select(conds ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Select(conds...))
}

func (w wineStorageSettingDo) Where(conds ...gen.Condition) IWineStorageSettingDo {
	return w.withDO(w.DO.Where(conds...))
}

func (w wineStorageSettingDo) Order(conds ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Order(conds...))
}

func (w wineStorageSettingDo) Distinct(cols ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Distinct(cols...))
}

func (w wineStorageSettingDo) Omit(cols ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Omit(cols...))
}

func (w wineStorageSettingDo) Join(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Join(table, on...))
}

func (w wineStorageSettingDo) LeftJoin(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.LeftJoin(table, on...))
}

func (w wineStorageSettingDo) RightJoin(table schema.Tabler, on ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.RightJoin(table, on...))
}

func (w wineStorageSettingDo) Group(cols ...field.Expr) IWineStorageSettingDo {
	return w.withDO(w.DO.Group(cols...))
}

func (w wineStorageSettingDo) Having(conds ...gen.Condition) IWineStorageSettingDo {
	return w.withDO(w.DO.Having(conds...))
}

func (w wineStorageSettingDo) Limit(limit int) IWineStorageSettingDo {
	return w.withDO(w.DO.Limit(limit))
}

func (w wineStorageSettingDo) Offset(offset int) IWineStorageSettingDo {
	return w.withDO(w.DO.Offset(offset))
}

func (w wineStorageSettingDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IWineStorageSettingDo {
	return w.withDO(w.DO.Scopes(funcs...))
}

func (w wineStorageSettingDo) Unscoped() IWineStorageSettingDo {
	return w.withDO(w.DO.Unscoped())
}

func (w wineStorageSettingDo) Create(values ...*po.WineStorageSetting) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Create(values)
}

func (w wineStorageSettingDo) CreateInBatches(values []*po.WineStorageSetting, batchSize int) error {
	return w.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (w wineStorageSettingDo) Save(values ...*po.WineStorageSetting) error {
	if len(values) == 0 {
		return nil
	}
	return w.DO.Save(values)
}

func (w wineStorageSettingDo) First() (*po.WineStorageSetting, error) {
	if result, err := w.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.WineStorageSetting), nil
	}
}

func (w wineStorageSettingDo) Take() (*po.WineStorageSetting, error) {
	if result, err := w.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.WineStorageSetting), nil
	}
}

func (w wineStorageSettingDo) Last() (*po.WineStorageSetting, error) {
	if result, err := w.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.WineStorageSetting), nil
	}
}

func (w wineStorageSettingDo) Find() ([]*po.WineStorageSetting, error) {
	result, err := w.DO.Find()
	return result.([]*po.WineStorageSetting), err
}

func (w wineStorageSettingDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.WineStorageSetting, err error) {
	buf := make([]*po.WineStorageSetting, 0, batchSize)
	err = w.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (w wineStorageSettingDo) FindInBatches(result *[]*po.WineStorageSetting, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return w.DO.FindInBatches(result, batchSize, fc)
}

func (w wineStorageSettingDo) Attrs(attrs ...field.AssignExpr) IWineStorageSettingDo {
	return w.withDO(w.DO.Attrs(attrs...))
}

func (w wineStorageSettingDo) Assign(attrs ...field.AssignExpr) IWineStorageSettingDo {
	return w.withDO(w.DO.Assign(attrs...))
}

func (w wineStorageSettingDo) Joins(fields ...field.RelationField) IWineStorageSettingDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Joins(_f))
	}
	return &w
}

func (w wineStorageSettingDo) Preload(fields ...field.RelationField) IWineStorageSettingDo {
	for _, _f := range fields {
		w = *w.withDO(w.DO.Preload(_f))
	}
	return &w
}

func (w wineStorageSettingDo) FirstOrInit() (*po.WineStorageSetting, error) {
	if result, err := w.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.WineStorageSetting), nil
	}
}

func (w wineStorageSettingDo) FirstOrCreate() (*po.WineStorageSetting, error) {
	if result, err := w.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.WineStorageSetting), nil
	}
}

func (w wineStorageSettingDo) FindByPage(offset int, limit int) (result []*po.WineStorageSetting, count int64, err error) {
	result, err = w.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = w.Offset(-1).Limit(-1).Count()
	return
}

func (w wineStorageSettingDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = w.Count()
	if err != nil {
		return
	}

	err = w.Offset(offset).Limit(limit).Scan(result)
	return
}

func (w wineStorageSettingDo) Scan(result interface{}) (err error) {
	return w.DO.Scan(result)
}

func (w wineStorageSettingDo) Delete(models ...*po.WineStorageSetting) (result gen.ResultInfo, err error) {
	return w.DO.Delete(models)
}

func (w *wineStorageSettingDo) withDO(do gen.Dao) *wineStorageSettingDo {
	w.DO = *do.(*gen.DO)
	return w
}
