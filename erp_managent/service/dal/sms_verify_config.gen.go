// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package dal

import (
	"context"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"voderpltvv/erp_managent/service/po"
)

func newSmsVerifyConfig(db *gorm.DB, opts ...gen.DOOption) smsVerifyConfig {
	_smsVerifyConfig := smsVerifyConfig{}

	_smsVerifyConfig.smsVerifyConfigDo.UseDB(db, opts...)
	_smsVerifyConfig.smsVerifyConfigDo.UseModel(&po.SmsVerifyConfig{})

	tableName := _smsVerifyConfig.smsVerifyConfigDo.TableName()
	_smsVerifyConfig.ALL = field.NewAsterisk(tableName)
	_smsVerifyConfig.Id = field.NewString(tableName, "id")
	_smsVerifyConfig.VenueId = field.NewString(tableName, "venue_id")
	_smsVerifyConfig.ConfigKey = field.NewString(tableName, "config_key")
	_smsVerifyConfig.Enabled = field.NewInt(tableName, "enabled")
	_smsVerifyConfig.Description = field.NewString(tableName, "description")
	_smsVerifyConfig.Remark = field.NewString(tableName, "remark")
	_smsVerifyConfig.Ctime = field.NewInt64(tableName, "ctime")
	_smsVerifyConfig.Utime = field.NewInt64(tableName, "utime")
	_smsVerifyConfig.State = field.NewInt(tableName, "state")
	_smsVerifyConfig.Version = field.NewInt(tableName, "version")

	_smsVerifyConfig.fillFieldMap()

	return _smsVerifyConfig
}

type smsVerifyConfig struct {
	smsVerifyConfigDo

	ALL         field.Asterisk
	Id          field.String
	VenueId     field.String
	ConfigKey   field.String
	Enabled     field.Int
	Description field.String
	Remark      field.String
	Ctime       field.Int64
	Utime       field.Int64
	State       field.Int
	Version     field.Int

	fieldMap map[string]field.Expr
}

func (s smsVerifyConfig) Table(newTableName string) *smsVerifyConfig {
	s.smsVerifyConfigDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s smsVerifyConfig) As(alias string) *smsVerifyConfig {
	s.smsVerifyConfigDo.DO = *(s.smsVerifyConfigDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *smsVerifyConfig) updateTableName(table string) *smsVerifyConfig {
	s.ALL = field.NewAsterisk(table)
	s.Id = field.NewString(table, "id")
	s.VenueId = field.NewString(table, "venue_id")
	s.ConfigKey = field.NewString(table, "config_key")
	s.Enabled = field.NewInt(table, "enabled")
	s.Description = field.NewString(table, "description")
	s.Remark = field.NewString(table, "remark")
	s.Ctime = field.NewInt64(table, "ctime")
	s.Utime = field.NewInt64(table, "utime")
	s.State = field.NewInt(table, "state")
	s.Version = field.NewInt(table, "version")

	s.fillFieldMap()

	return s
}

func (s *smsVerifyConfig) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *smsVerifyConfig) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 10)
	s.fieldMap["id"] = s.Id
	s.fieldMap["venue_id"] = s.VenueId
	s.fieldMap["config_key"] = s.ConfigKey
	s.fieldMap["enabled"] = s.Enabled
	s.fieldMap["description"] = s.Description
	s.fieldMap["remark"] = s.Remark
	s.fieldMap["ctime"] = s.Ctime
	s.fieldMap["utime"] = s.Utime
	s.fieldMap["state"] = s.State
	s.fieldMap["version"] = s.Version
}

func (s smsVerifyConfig) clone(db *gorm.DB) smsVerifyConfig {
	s.smsVerifyConfigDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s smsVerifyConfig) replaceDB(db *gorm.DB) smsVerifyConfig {
	s.smsVerifyConfigDo.ReplaceDB(db)
	return s
}

type smsVerifyConfigDo struct{ gen.DO }

type ISmsVerifyConfigDo interface {
	gen.SubQuery
	Debug() ISmsVerifyConfigDo
	WithContext(ctx context.Context) ISmsVerifyConfigDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() ISmsVerifyConfigDo
	WriteDB() ISmsVerifyConfigDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) ISmsVerifyConfigDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) ISmsVerifyConfigDo
	Not(conds ...gen.Condition) ISmsVerifyConfigDo
	Or(conds ...gen.Condition) ISmsVerifyConfigDo
	Select(conds ...field.Expr) ISmsVerifyConfigDo
	Where(conds ...gen.Condition) ISmsVerifyConfigDo
	Order(conds ...field.Expr) ISmsVerifyConfigDo
	Distinct(cols ...field.Expr) ISmsVerifyConfigDo
	Omit(cols ...field.Expr) ISmsVerifyConfigDo
	Join(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo
	LeftJoin(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo
	RightJoin(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo
	Group(cols ...field.Expr) ISmsVerifyConfigDo
	Having(conds ...gen.Condition) ISmsVerifyConfigDo
	Limit(limit int) ISmsVerifyConfigDo
	Offset(offset int) ISmsVerifyConfigDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) ISmsVerifyConfigDo
	Unscoped() ISmsVerifyConfigDo
	Create(values ...*po.SmsVerifyConfig) error
	CreateInBatches(values []*po.SmsVerifyConfig, batchSize int) error
	Save(values ...*po.SmsVerifyConfig) error
	First() (*po.SmsVerifyConfig, error)
	Take() (*po.SmsVerifyConfig, error)
	Last() (*po.SmsVerifyConfig, error)
	Find() ([]*po.SmsVerifyConfig, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.SmsVerifyConfig, err error)
	FindInBatches(result *[]*po.SmsVerifyConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*po.SmsVerifyConfig) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) ISmsVerifyConfigDo
	Assign(attrs ...field.AssignExpr) ISmsVerifyConfigDo
	Joins(fields ...field.RelationField) ISmsVerifyConfigDo
	Preload(fields ...field.RelationField) ISmsVerifyConfigDo
	FirstOrInit() (*po.SmsVerifyConfig, error)
	FirstOrCreate() (*po.SmsVerifyConfig, error)
	FindByPage(offset int, limit int) (result []*po.SmsVerifyConfig, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) ISmsVerifyConfigDo
	UnderlyingDB() *gorm.DB
	schema.Tabler

	DeleteByID(id string) (err error)
	QueryOneByID(id string) (result *po.SmsVerifyConfig, err error)
	DazzyQueryByAny(field string, value string) (result []*po.SmsVerifyConfig, err error)
	QueryByVenueId(venueId string) (result []*po.SmsVerifyConfig, err error)
}

// DELETE FROM @@table WHERE id = @id
func (s smsVerifyConfigDo) DeleteByID(id string) (err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("DELETE FROM sms_verify_config WHERE id = ? ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Exec(generateSQL.String(), params...) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE id = @id LIMIT 1
func (s smsVerifyConfigDo) QueryOneByID(id string) (result *po.SmsVerifyConfig, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, id)
	generateSQL.WriteString("SELECT * FROM sms_verify_config WHERE id = ? LIMIT 1 ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Raw(generateSQL.String(), params...).Take(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE @field LIKE @value
func (s smsVerifyConfigDo) DazzyQueryByAny(field string, value string) (result []*po.SmsVerifyConfig, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, field)
	params = append(params, value)
	generateSQL.WriteString("SELECT * FROM sms_verify_config WHERE ? LIKE ? ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

// SELECT * FROM @@table WHERE venue_id = @venueId
func (s smsVerifyConfigDo) QueryByVenueId(venueId string) (result []*po.SmsVerifyConfig, err error) {
	var params []interface{}

	var generateSQL strings.Builder
	params = append(params, venueId)
	generateSQL.WriteString("SELECT * FROM sms_verify_config WHERE venue_id = ? ")

	var executeSQL *gorm.DB
	executeSQL = s.UnderlyingDB().Raw(generateSQL.String(), params...).Find(&result) // ignore_security_alert
	err = executeSQL.Error

	return
}

func (s smsVerifyConfigDo) Debug() ISmsVerifyConfigDo {
	return s.withDO(s.DO.Debug())
}

func (s smsVerifyConfigDo) WithContext(ctx context.Context) ISmsVerifyConfigDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s smsVerifyConfigDo) ReadDB() ISmsVerifyConfigDo {
	return s.Clauses(dbresolver.Read)
}

func (s smsVerifyConfigDo) WriteDB() ISmsVerifyConfigDo {
	return s.Clauses(dbresolver.Write)
}

func (s smsVerifyConfigDo) Session(config *gorm.Session) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Session(config))
}

func (s smsVerifyConfigDo) Clauses(conds ...clause.Expression) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s smsVerifyConfigDo) Returning(value interface{}, columns ...string) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s smsVerifyConfigDo) Not(conds ...gen.Condition) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s smsVerifyConfigDo) Or(conds ...gen.Condition) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s smsVerifyConfigDo) Select(conds ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s smsVerifyConfigDo) Where(conds ...gen.Condition) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s smsVerifyConfigDo) Order(conds ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s smsVerifyConfigDo) Distinct(cols ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s smsVerifyConfigDo) Omit(cols ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s smsVerifyConfigDo) Join(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s smsVerifyConfigDo) LeftJoin(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s smsVerifyConfigDo) RightJoin(table schema.Tabler, on ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s smsVerifyConfigDo) Group(cols ...field.Expr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s smsVerifyConfigDo) Having(conds ...gen.Condition) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s smsVerifyConfigDo) Limit(limit int) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s smsVerifyConfigDo) Offset(offset int) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s smsVerifyConfigDo) Scopes(funcs ...func(gen.Dao) gen.Dao) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s smsVerifyConfigDo) Unscoped() ISmsVerifyConfigDo {
	return s.withDO(s.DO.Unscoped())
}

func (s smsVerifyConfigDo) Create(values ...*po.SmsVerifyConfig) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s smsVerifyConfigDo) CreateInBatches(values []*po.SmsVerifyConfig, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s smsVerifyConfigDo) Save(values ...*po.SmsVerifyConfig) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s smsVerifyConfigDo) First() (*po.SmsVerifyConfig, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*po.SmsVerifyConfig), nil
	}
}

func (s smsVerifyConfigDo) Take() (*po.SmsVerifyConfig, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*po.SmsVerifyConfig), nil
	}
}

func (s smsVerifyConfigDo) Last() (*po.SmsVerifyConfig, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*po.SmsVerifyConfig), nil
	}
}

func (s smsVerifyConfigDo) Find() ([]*po.SmsVerifyConfig, error) {
	result, err := s.DO.Find()
	return result.([]*po.SmsVerifyConfig), err
}

func (s smsVerifyConfigDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*po.SmsVerifyConfig, err error) {
	buf := make([]*po.SmsVerifyConfig, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s smsVerifyConfigDo) FindInBatches(result *[]*po.SmsVerifyConfig, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s smsVerifyConfigDo) Attrs(attrs ...field.AssignExpr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s smsVerifyConfigDo) Assign(attrs ...field.AssignExpr) ISmsVerifyConfigDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s smsVerifyConfigDo) Joins(fields ...field.RelationField) ISmsVerifyConfigDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s smsVerifyConfigDo) Preload(fields ...field.RelationField) ISmsVerifyConfigDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s smsVerifyConfigDo) FirstOrInit() (*po.SmsVerifyConfig, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*po.SmsVerifyConfig), nil
	}
}

func (s smsVerifyConfigDo) FirstOrCreate() (*po.SmsVerifyConfig, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*po.SmsVerifyConfig), nil
	}
}

func (s smsVerifyConfigDo) FindByPage(offset int, limit int) (result []*po.SmsVerifyConfig, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s smsVerifyConfigDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s smsVerifyConfigDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s smsVerifyConfigDo) Delete(models ...*po.SmsVerifyConfig) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *smsVerifyConfigDo) withDO(do gen.Dao) *smsVerifyConfigDo {
	s.DO = *do.(*gen.DO)
	return s
}
