package req

// SmsVerifyConfigGetReqDto 获取短信验证配置请求
// @Description 获取短信验证配置请求参数
type SmsVerifyConfigGetReqDto struct {
	BaseReqDto
	ConfigKey *string `json:"configKey" example:"wine_withdrawal_verify"` // 配置键名，为空时获取所有配置
}

// SmsVerifyConfigUpdateReqDto 更新短信验证配置请求
// @Description 更新短信验证配置请求参数
type SmsVerifyConfigUpdateReqDto struct {
	BaseReqDto
	ConfigKey   *string `json:"configKey" example:"wine_withdrawal_verify"` // 配置键名
	Enabled     *int    `json:"enabled" example:"1"`                        // 是否启用：0-禁用，1-启用
	Description *string `json:"description" example:"取酒短信验证开关"`             // 配置描述
	Remark      *string `json:"remark" example:"备注信息"`                      // 备注
}

// SmsVerifyConfigBatchUpdateReqDto 批量更新短信验证配置请求
// @Description 批量更新短信验证配置请求参数
type SmsVerifyConfigBatchUpdateReqDto struct {
	BaseReqDto
	Configs []SmsVerifyConfigUpdateReqDto `json:"configs"` // 配置列表
}

// SmsVerifyConfigDeleteReqDto 删除短信验证配置请求
// @Description 删除短信验证配置请求参数
type SmsVerifyConfigDeleteReqDto struct {
	BaseReqDto
	ConfigId *string `json:"configId" example:"config_123"` // 配置ID
}
