package req

// UpdateVenuePrintConfigReqDto 更新门店打印配置请求DTO
type UpdateVenuePrintConfigReqDto struct {
	VenueId            *string                `json:"venueId" binding:"required"`            // 门店ID
	TemplateCode       *string                `json:"templateCode" binding:"required"`       // 模板编码
	SelectedTemplateId *string                `json:"selectedTemplateId" binding:"required"` // 选择的布局模板ID
	IsEnabled          *bool                  `json:"isEnabled"`                             // 是否启用，默认true
	Copies             *int                   `json:"copies"`                                // 打印份数，默认1
	BusinessConfig     map[string]interface{} `json:"businessConfig"`                        // 业务配置JSON
	Remark             *string                `json:"remark"`                                // 配置备注

	// 模板修改相关字段
	TemplateModified      *bool   `json:"templateModified"`      // 是否修改了模板
	ModifiedLayoutContent *string `json:"modifiedLayoutContent"` // 修改后的模板内容
	CustomTemplateName    *string `json:"customTemplateName"`    // 自定义模板名称
}
