package req

// UpdatePrintTemplateReqDto 更新打印模板请求DTO
type UpdatePrintTemplateReqDto struct {
	Id              *string `json:"id" binding:"required"`        // 模板ID
	Name            *string `json:"name"`                         // 模板名称
	LayoutContent   *string `json:"layoutContent"`                // 布局和样式的JSON定义
	IsEnabled       *bool   `json:"isEnabled"`                    // 是否启用
	Remark          *string `json:"remark"`                       // 模板备注说明
}
