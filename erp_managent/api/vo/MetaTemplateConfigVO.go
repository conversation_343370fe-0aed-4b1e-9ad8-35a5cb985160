package vo

import "voderpltvv/erp_managent/domain/printmeta"

// MetaTemplateConfigVO 元模板配置VO（符合动态配置规范）
type MetaTemplateConfigVO struct {
	TemplateCode  string                   `json:"templateCode"`  // 模板编码
	Name          string                   `json:"name"`          // 显示名称
	Description   string                   `json:"description"`   // 描述
	ConfigOptions []printmeta.ConfigOption `json:"configOptions"` // 动态配置项列表
	DefaultConfig map[string]interface{}   `json:"defaultConfig"` // 默认配置值
}
