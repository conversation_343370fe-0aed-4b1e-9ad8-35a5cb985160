package vo

// BatchInitFeatureRespDto 批量初始化功能响应DTO
type BatchInitFeatureRespDto struct {
	TaskId string `json:"taskId"` // 任务ID，用于查询进度
}

// VenueFeatureInitializationVO 门店功能初始化状态VO
type VenueFeatureInitializationVO struct {
	Id             string `json:"id"`             // 记录ID
	VenueId        string `json:"venueId"`        // 门店ID
	VenueName      string `json:"venueName"`      // 门店名称（关联查询）
	FeatureName    string `json:"featureName"`    // 功能名称
	FeatureVersion string `json:"featureVersion"` // 功能版本
	Status         string `json:"status"`         // 初始化状态（pending/running/completed/failed）
	ErrorMsg       string `json:"errorMsg"`       // 错误信息
	StartTime      int64  `json:"startTime"`      // 开始时间
	EndTime        int64  `json:"endTime"`        // 结束时间
	Duration       int64  `json:"duration"`       // 耗时（毫秒）
	Operator       string `json:"operator"`       // 操作员
	Ctime          int64  `json:"ctime"`          // 创建时间
	Utime          int64  `json:"utime"`          // 更新时间
}

// InitializationTaskVO 初始化任务VO
type InitializationTaskVO struct {
	TaskId         string `json:"taskId"`         // 任务ID
	FeatureName    string `json:"featureName"`    // 功能名称
	FeatureVersion string `json:"featureVersion"` // 功能版本
	Status         string `json:"status"`         // 任务状态（pending/running/completed/failed）
	TotalVenues    int    `json:"totalVenues"`    // 总门店数
	SuccessCount   int    `json:"successCount"`   // 成功数量
	FailedCount    int    `json:"failedCount"`    // 失败数量
	PendingCount   int    `json:"pendingCount"`   // 待处理数量
	Progress       int    `json:"progress"`       // 进度百分比（0-100）
	StartTime      int64  `json:"startTime"`      // 开始时间
	EndTime        int64  `json:"endTime"`        // 结束时间
	Duration       int64  `json:"duration"`       // 耗时（毫秒）
	ErrorMsg       string `json:"errorMsg"`       // 错误信息
	Operator       string `json:"operator"`       // 操作员
	CreatedAt      int64  `json:"createdAt"`      // 创建时间
	UpdatedAt      int64  `json:"updatedAt"`      // 更新时间

	// 详细信息（可选）
	FailedVenues  []VenueFeatureInitializationVO `json:"failedVenues,omitempty"`  // 失败的门店列表
	PendingVenues []VenueFeatureInitializationVO `json:"pendingVenues,omitempty"` // 待处理的门店列表
}

// SystemFeatureVersionVO 系统功能版本VO
type SystemFeatureVersionVO struct {
	Id             string `json:"id"`             // 记录ID
	FeatureName    string `json:"featureName"`    // 功能名称
	FeatureVersion string `json:"featureVersion"` // 功能版本
	InitStatus     string `json:"initStatus"`     // 初始化状态（pending/running/completed/failed）
	TotalVenues    int    `json:"totalVenues"`    // 总门店数
	SuccessCount   int    `json:"successCount"`   // 成功数量
	FailedCount    int    `json:"failedCount"`    // 失败数量
	Progress       int    `json:"progress"`       // 进度百分比（0-100）
	StartTime      int64  `json:"startTime"`      // 开始时间
	EndTime        int64  `json:"endTime"`        // 结束时间
	Duration       int64  `json:"duration"`       // 耗时（毫秒）
	ErrorMsg       string `json:"errorMsg"`       // 错误信息
	Operator       string `json:"operator"`       // 操作员
	Ctime          int64  `json:"ctime"`          // 创建时间
	Utime          int64  `json:"utime"`          // 更新时间
}

// QueryInitStatusRespDto 查询初始化状态响应DTO
type QueryInitStatusRespDto struct {
	Records []VenueFeatureInitializationVO `json:"records"` // 初始化记录列表
	Total   int64                          `json:"total"`   // 总记录数
}

// QueryInitProgressRespDto 查询初始化进度响应DTO
type QueryInitProgressRespDto struct {
	Tasks []InitializationTaskVO `json:"tasks"` // 任务列表
	Total int64                  `json:"total"` // 总任务数
}
