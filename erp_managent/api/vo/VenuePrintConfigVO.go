package vo

import "voderpltvv/erp_managent/domain/printmeta"

// VenuePrintConfigVO 门店打印配置VO
type VenuePrintConfigVO struct {
	Id                 string                 `json:"id"`                 // 配置唯一标识
	VenueId            string                 `json:"venueId"`            // 门店ID
	TemplateCode       string                 `json:"templateCode"`       // 打印类型编码
	SelectedTemplateId string                 `json:"selectedTemplateId"` // 选择的布局模板ID
	IsEnabled          bool                   `json:"isEnabled"`          // 是否启用
	Copies             int                    `json:"copies"`             // 打印份数
	BusinessConfig     map[string]interface{} `json:"businessConfig"`     // 业务配置JSON
	Remark             string                 `json:"remark"`             // 配置备注
	Ctime              int64                  `json:"ctime"`              // 创建时间
	Utime              int64                  `json:"utime"`              // 更新时间

	// 关联数据
	SelectedTemplate *PrintTemplateVO         `json:"selectedTemplate,omitempty"` // 选择的布局模板详情
	ConfigMetaData   []printmeta.ConfigOption `json:"configMetaData,omitempty"`   // 业务配置的元数据"说明书"
}
