package vo

// SmsVerifyConfigVO 短信验证配置值对象
// @Description 短信验证配置信息
type SmsVerifyConfigVO struct {
	Id          string `json:"id" example:"config_123"`                    // ID
	VenueId     string `json:"venueId" example:"35RYMWLmBa"`              // 门店ID
	ConfigKey   string `json:"configKey" example:"wine_withdrawal_verify"` // 配置键名
	Enabled     int    `json:"enabled" example:"1"`                        // 是否启用：0-禁用，1-启用
	Description string `json:"description" example:"取酒短信验证开关"`           // 配置描述
	Remark      string `json:"remark" example:"备注信息"`                     // 备注
	Ctime       int64  `json:"ctime" example:"1753867470"`                 // 创建时间
	Utime       int64  `json:"utime" example:"1753867470"`                 // 更新时间
	State       int    `json:"state" example:"0"`                          // 状态
	Version     int    `json:"version" example:"1"`                        // 版本
}

// SmsVerifyConfigListVO 短信验证配置列表响应
// @Description 短信验证配置列表
type SmsVerifyConfigListVO struct {
	List []SmsVerifyConfigVO `json:"list"` // 配置列表
}
