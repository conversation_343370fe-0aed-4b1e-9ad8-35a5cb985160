package print

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/printmeta"
	"voderpltvv/erp_managent/domain/printmeta/service"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type MetaTemplateAppService struct {
	printingDomainService service.IPrintingDomainService
}

func NewMetaTemplateAppService() *MetaTemplateAppService {
	return &MetaTemplateAppService{
		printingDomainService: service.NewPrintingDomainService(),
	}
}

// ListMetaTemplates 获取所有元模板列表
func (appService *MetaTemplateAppService) ListMetaTemplates(ctx *gin.Context, reqDto *req.ListMetaTemplatesReqDto) ([]vo.MetaTemplateVO, error) {
	util.Wlog(ctx).Infof("开始获取元模板列表")

	// 获取所有元模板
	metaTemplates, err := appService.printingDomainService.GetAllMetaTemplates(ctx)
	if err != nil {
		util.Wlog(ctx).Errorf("获取元模板列表失败: %v", err)
		return nil, err
	}

	// 转换为VO
	var templateVOs []vo.MetaTemplateVO
	for _, template := range metaTemplates {
		templateVO := appService.convertMetaTemplateToVO(template)
		templateVOs = append(templateVOs, templateVO)
	}

	util.Wlog(ctx).Infof("成功获取%d个元模板", len(templateVOs))
	return templateVOs, nil
}

// GetMetaTemplate 获取指定元模板详情
func (appService *MetaTemplateAppService) GetMetaTemplate(ctx *gin.Context, reqDto *req.GetMetaTemplateReqDto) (*vo.MetaTemplateVO, error) {
	util.Wlog(ctx).Infof("开始获取元模板详情: %s", *reqDto.TemplateCode)

	// 获取指定元模板
	templateCode := printmeta.TemplateCode(*reqDto.TemplateCode)
	metaTemplate, err := appService.printingDomainService.GetMetaTemplateByCode(ctx, templateCode)
	if err != nil {
		util.Wlog(ctx).Errorf("获取元模板失败: %v", err)
		return nil, err
	}

	// 转换为VO
	templateVO := appService.convertMetaTemplateToVO(metaTemplate)

	util.Wlog(ctx).Infof("成功获取元模板: %s", *reqDto.TemplateCode)
	return &templateVO, nil
}

// convertMetaTemplateToVO 转换元模板为VO
func (appService *MetaTemplateAppService) convertMetaTemplateToVO(metaTemplate *printmeta.MetaTemplate) vo.MetaTemplateVO {
	// 转换业务配置项（从ConfigOptions转换为旧格式以保持兼容）
	var configVOs []vo.BusinessConfigItemVO
	for _, option := range metaTemplate.ConfigOptions {
		// 将新的ConfigOption转换为旧的BusinessConfigItemVO格式
		configType := "string"
		switch option.DataType {
		case printmeta.DataTypeBoolean:
			configType = "bool"
		case printmeta.DataTypeNumber:
			configType = "number"
		case printmeta.DataTypeString:
			configType = "string"
		case printmeta.DataTypeArray:
			configType = "array"
		}

		configVO := vo.BusinessConfigItemVO{
			Key:          option.Key,
			Name:         option.Label,
			Type:         configType,
			DefaultValue: option.DefaultValue,
			Description:  option.Description,
			Required:     len(option.Validations) > 0, // 简化判断：有验证规则就认为是必填
		}
		configVOs = append(configVOs, configVO)
	}

	// 转换数据组件
	var componentVOs []vo.DataComponentVO
	for _, component := range metaTemplate.AvailableComponents {
		componentVO := vo.DataComponentVO{
			Key:         component.Key,
			Name:        component.Name,
			Description: component.Description,
			DataType:    component.DataType,
		}
		componentVOs = append(componentVOs, componentVO)
	}

	return vo.MetaTemplateVO{
		TemplateCode:         string(metaTemplate.TemplateCode),
		Name:                 metaTemplate.Name,
		Description:          metaTemplate.Description,
		AvailableConfigs:     configVOs,
		AvailableComponents:  componentVOs,
		DefaultLayoutContent: metaTemplate.DefaultLayoutContent,
	}
}
