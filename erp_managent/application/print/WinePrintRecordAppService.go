package print

import (
	"fmt"
	"strings"

	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/print/model/winestorage"
	"voderpltvv/erp_managent/domain/print/model/winewithdraw"
	"voderpltvv/erp_managent/domain/print/repository"
	"voderpltvv/erp_managent/domain/print/service"
	"voderpltvv/erp_managent/infra/repository/print"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/transfer"

	_const "voderpltvv/const"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

// WinePrintRecordAppService 存酒和取酒打印记录应用服务
type WinePrintRecordAppService struct {
	wineStoragePrintRecordRepo        repository.WineStoragePrintRecordRepository
	wineStorageDataAssemblyService    service.WineStorageDataAssemblyService
	wineWithdrawPrintRecordRepo       repository.WineWithdrawPrintRecordRepository
	wineWithdrawalDataAssemblyService service.WineWithdrawalDataAssemblyService
}

// NewWinePrintRecordAppService 创建存酒和取酒打印记录应用服务
func NewWinePrintRecordAppService() *WinePrintRecordAppService {
	return &WinePrintRecordAppService{
		wineStoragePrintRecordRepo:        print.NewWineStoragePrintRecordRepositoryImpl(),
		wineStorageDataAssemblyService:    service.NewWineStorageDataAssemblyService(),
		wineWithdrawPrintRecordRepo:       print.NewWineWithdrawPrintRecordRepositoryImpl(),
		wineWithdrawalDataAssemblyService: service.NewWineWithdrawalDataAssemblyService(),
	}
}

// ========================== 存酒打印记录服务 ==========================

// CreateWineStoragePrintRecord 创建存酒打印记录
func (s *WinePrintRecordAppService) CreateWineStoragePrintRecord(ctx *gin.Context, reqDto *req.CreateWineStoragePrintRecordReqDto) (vo.WineStoragePrintRecordVO, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(reqDto.VenueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}
	if err := lo.Ternary(reqDto.StorageRecordId == "", fmt.Errorf("存酒记录ID不能为空"), nil); err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 使用领域服务组装存酒单数据
	wineStorageBillData, operatorId, operatorName, err := s.wineStorageDataAssemblyService.AssembleWineStorageData(
		ctx,
		reqDto.VenueId,
		reqDto.StorageRecordId,
	)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 创建领域模型 - 使用真实的操作员信息
	record, err := winestorage.NewWineStoragePrintRecord(
		reqDto.VenueId,
		"", // 存酒打印与sessionId无关，ProductStorage系统没有sessionId概念
		reqDto.StorageRecordId,
		operatorId,   // 使用真实的操作员ID
		operatorName, // 使用真实的操作员姓名
	)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 设置内容
	record.Content = wineStorageBillData

	// 保存记录
	err = s.wineStoragePrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.WineStoragePrintRecordVO{}, err
	}

	// 转换为VO返回
	return transfer.ConvertToWineStorageVO(record), nil
}

// ========================== 取酒打印记录服务 ==========================

// CreateWineWithdrawPrintRecord 创建取酒打印记录
func (s *WinePrintRecordAppService) CreateWineWithdrawPrintRecord(ctx *gin.Context, reqDto *req.CreateWineWithdrawPrintRecordReqDto) (vo.WineWithdrawPrintRecordVO, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(reqDto.VenueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}
	if err := lo.Ternary(reqDto.WithdrawalRecordId == "", fmt.Errorf("取酒记录ID不能为空"), nil); err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 使用领域服务组装取酒单数据
	wineWithdrawalBillData, operatorId, operatorName, err := s.wineWithdrawalDataAssemblyService.AssembleWineWithdrawalData(
		ctx,
		reqDto.VenueId,
		reqDto.WithdrawalRecordId,
	)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 创建领域模型 - 使用真实的操作员信息
	record, err := winewithdraw.NewWineWithdrawPrintRecord(
		reqDto.VenueId,
		"", // 取酒打印与sessionId无关
		reqDto.WithdrawalRecordId,
		operatorId,   // 使用真实的操作员ID
		operatorName, // 使用真实的操作员姓名
	)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 设置内容
	record.Content = wineWithdrawalBillData

	// 保存记录
	err = s.wineWithdrawPrintRecordRepo.Save(ctx, record)
	if err != nil {
		return vo.WineWithdrawPrintRecordVO{}, err
	}

	// 转换为VO返回
	return transfer.ConvertToWineWithdrawVO(record), nil
}

// QueryWinePrintRecordByBusinessID 根据业务ID查询存取酒打印记录
func (s *WinePrintRecordAppService) QueryWinePrintRecordByBusinessID(ctx *gin.Context, venueId, printBusinessID string) (*vo.WinePrintRecordUnionVO, error) {
	// 参数验证 - 使用lo.Ternary简化
	if err := lo.Ternary(venueId == "", fmt.Errorf("门店ID不能为空"), nil); err != nil {
		return nil, err
	}
	if err := lo.Ternary(printBusinessID == "", fmt.Errorf("printBusinessID不能为空"), nil); err != nil {
		return nil, err
	}

	// 使用统一的PrintRecordService查询，避免类型转换问题
	printRecordService := &impl.PrintRecordService{}
	record, err := printRecordService.FindUniqueByBusinessIdAsync(venueId, printBusinessID)
	if err != nil {
		// 如果是记录不存在，返回nil而不是错误
		if strings.Contains(err.Error(), "record not found") {
			return nil, nil
		}
		return nil, fmt.Errorf("查询打印记录失败: %v", err)
	}

	if record == nil {
		return nil, nil
	}

	// 根据打印类型选择对应的转换方法
	if record.PrintType == nil {
		return nil, fmt.Errorf("打印记录类型为空")
	}

	var unionVO vo.WinePrintRecordUnionVO
	switch *record.PrintType {
	case _const.PRINT_TYPE_WINE_STORAGE:
		unionVO = transfer.ConvertToWineUnionVOFromStorage(record)
	case _const.PRINT_TYPE_WINE_WITHDRAW:
		unionVO = transfer.ConvertToWineUnionVOFromWithdraw(record)
	case _const.PRINT_TYPE_WINE_RENEWAL:
		unionVO = transfer.ConvertToWineUnionVOFromRenewal(record)
	default:
		return nil, fmt.Errorf("不支持的打印类型: %s", *record.PrintType)
	}

	// 如果venueName为空，实时查询门店名称进行补充
	if unionVO.VenueName == "" && record.VenueId != nil && *record.VenueId != "" {
		venueService := &impl.VenueService{}
		if venue, err := venueService.FindVenueById(ctx, *record.VenueId); err == nil && venue != nil && venue.Name != nil {
			unionVO.VenueName = *venue.Name
		}
	}

	return &unionVO, nil
}

// findUniqueRenewalRecord 根据业务ID查询唯一的续存打印记录
func (s *WinePrintRecordAppService) findUniqueRenewalRecord(ctx *gin.Context, venueId, printBusinessID string) (*vo.WineRenewalPrintRecordVO, error) {
	printRecordService := &impl.PrintRecordService{}

	// 直接使用businessId进行单一查询，printBusinessID具有唯一性
	record, err := printRecordService.FindUniqueByBusinessIdAsync(venueId, printBusinessID)
	if err != nil {
		// 如果是记录不存在的错误，返回nil而不是错误
		if strings.Contains(err.Error(), "record not found") {
			return nil, nil
		}
		return nil, err
	}

	// 使用转换函数组装详细数据
	result := transfer.ConvertToWineRenewalVO(record)
	return &result, nil
}
