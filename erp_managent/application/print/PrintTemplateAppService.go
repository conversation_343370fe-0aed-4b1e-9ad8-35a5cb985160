package print

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/printmeta"
	"voderpltvv/erp_managent/domain/printmeta/service"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type PrintTemplateAppService struct {
	printTemplateService  *impl.PrintTemplateService
	printingDomainService service.IPrintingDomainService
}

func NewPrintTemplateAppService() *PrintTemplateAppService {
	return &PrintTemplateAppService{
		printTemplateService:  impl.NewPrintTemplateService(),
		printingDomainService: service.NewPrintingDomainService(),
	}
}

// ListPrintTemplates 获取布局模板列表
func (appService *PrintTemplateAppService) ListPrintTemplates(ctx *gin.Context, reqDto *req.ListPrintTemplatesReqDto) ([]vo.PrintTemplateVO, error) {
	util.Wlog(ctx).Infof("开始获取布局模板列表")

	// 构建查询条件
	templateCode := ""
	if reqDto.TemplateCode != nil {
		templateCode = *reqDto.TemplateCode
	}

	var isEnabled *bool
	if reqDto.IsEnabled != nil {
		isEnabled = reqDto.IsEnabled
	}

	// 获取模板列表
	templateList, err := appService.printTemplateService.FindAllPrintTemplate(ctx, templateCode, isEnabled)
	if err != nil {
		util.Wlog(ctx).Errorf("获取布局模板列表失败: %v", err)
		return nil, err
	}

	// 转换为VO
	var templateVOs []vo.PrintTemplateVO
	if templateList != nil {
		for _, template := range *templateList {
			templateVO := appService.convertPrintTemplateToVO(&template)
			templateVOs = append(templateVOs, templateVO)
		}
	}

	util.Wlog(ctx).Infof("成功获取%d个布局模板", len(templateVOs))
	return templateVOs, nil
}

// CreatePrintTemplate 创建自定义布局模板
func (appService *PrintTemplateAppService) CreatePrintTemplate(ctx *gin.Context, reqDto *req.CreatePrintTemplateReqDto) (*vo.PrintTemplateVO, error) {
	util.Wlog(ctx).Infof("开始创建布局模板: %s", util.GetStringValue(reqDto.Name))

	// 验证模板编码是否支持
	templateCode := printmeta.TemplateCode(*reqDto.TemplateCode)
	_, err := appService.printingDomainService.GetMetaTemplateByCode(ctx, templateCode)
	if err != nil {
		util.Wlog(ctx).Errorf("不支持的模板编码: %s, 错误: %v", *reqDto.TemplateCode, err)
		return nil, err
	}

	// 构建PO对象
	printTemplate := &po.PrintTemplate{
		TemplateCode:  reqDto.TemplateCode,
		Name:          reqDto.Name,
		LayoutContent: reqDto.LayoutContent,
		Remark:        reqDto.Remark,
	}

	// 设置默认值
	if reqDto.IsSystemDefault != nil {
		printTemplate.IsSystemDefault = reqDto.IsSystemDefault
	} else {
		printTemplate.IsSystemDefault = util.GetBoolPtr(false)
	}

	if reqDto.IsEnabled != nil {
		printTemplate.IsEnabled = reqDto.IsEnabled
	} else {
		printTemplate.IsEnabled = util.GetBoolPtr(true)
	}

	// 创建模板
	err = appService.printTemplateService.CreatePrintTemplate(ctx, printTemplate)
	if err != nil {
		util.Wlog(ctx).Errorf("创建布局模板失败: %v", err)
		return nil, err
	}

	// 转换为VO返回
	templateVO := appService.convertPrintTemplateToVO(printTemplate)

	util.Wlog(ctx).Infof("成功创建布局模板: %s", printTemplate.GetId())
	return &templateVO, nil
}

// UpdatePrintTemplate 更新布局模板
func (appService *PrintTemplateAppService) UpdatePrintTemplate(ctx *gin.Context, reqDto *req.UpdatePrintTemplateReqDto) (*vo.PrintTemplateVO, error) {
	util.Wlog(ctx).Infof("开始更新布局模板: %s", *reqDto.Id)

	// 验证模板是否存在
	_, err := appService.printTemplateService.FindPrintTemplateById(ctx, *reqDto.Id)
	if err != nil {
		util.Wlog(ctx).Errorf("模板不存在: %s", *reqDto.Id)
		return nil, err
	}

	// 构建更新对象
	updateTemplate := &po.PrintTemplate{
		Id: reqDto.Id,
	}

	// 只更新非空字段
	if reqDto.Name != nil {
		updateTemplate.Name = reqDto.Name
	}
	if reqDto.LayoutContent != nil {
		updateTemplate.LayoutContent = reqDto.LayoutContent
	}
	if reqDto.IsEnabled != nil {
		updateTemplate.IsEnabled = reqDto.IsEnabled
	}
	if reqDto.Remark != nil {
		updateTemplate.Remark = reqDto.Remark
	}

	// 执行更新
	err = appService.printTemplateService.UpdatePrintTemplatePartial(ctx, updateTemplate)
	if err != nil {
		util.Wlog(ctx).Errorf("更新布局模板失败: %v", err)
		return nil, err
	}

	// 返回更新后的模板
	updatedTemplate, _ := appService.printTemplateService.FindPrintTemplateById(ctx, *reqDto.Id)
	templateVO := appService.convertPrintTemplateToVO(updatedTemplate)

	util.Wlog(ctx).Infof("成功更新布局模板: %s", *reqDto.Id)
	return &templateVO, nil
}

// convertPrintTemplateToVO 转换布局模板为VO
func (appService *PrintTemplateAppService) convertPrintTemplateToVO(template *po.PrintTemplate) vo.PrintTemplateVO {
	return vo.PrintTemplateVO{
		Id:              template.GetId(),
		TemplateCode:    util.GetStringValue(template.TemplateCode),
		Name:            util.GetStringValue(template.Name),
		LayoutContent:   util.GetStringValue(template.LayoutContent),
		IsSystemDefault: util.GetBoolValue(template.IsSystemDefault),
		IsEnabled:       util.GetBoolValue(template.IsEnabled),
		Remark:          util.GetStringValue(template.Remark),
		Ctime:           util.GetInt64Value(template.Ctime),
		Utime:           util.GetInt64Value(template.Utime),
	}
}
