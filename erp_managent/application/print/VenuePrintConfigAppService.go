package print

import (
	"encoding/json"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/domain/printmeta"
	"voderpltvv/erp_managent/domain/printmeta/service"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type VenuePrintConfigAppService struct {
	venuePrintConfigService *impl.VenuePrintConfigService
	printTemplateService    *impl.PrintTemplateService
	printingDomainService   service.IPrintingDomainService
}

func NewVenuePrintConfigAppService() *VenuePrintConfigAppService {
	return &VenuePrintConfigAppService{
		venuePrintConfigService: impl.NewVenuePrintConfigService(),
		printTemplateService:    impl.NewPrintTemplateService(),
		printingDomainService:   service.NewPrintingDomainService(),
	}
}

// GetVenuePrintConfig 获取门店打印配置
func (appService *VenuePrintConfigAppService) GetVenuePrintConfig(ctx *gin.Context, reqDto *req.GetVenuePrintConfigReqDto) (*vo.VenuePrintConfigVO, error) {
	util.Wlog(ctx).Infof("开始获取门店打印配置: %s-%s", *reqDto.VenueId, *reqDto.TemplateCode)

	// 获取门店打印配置
	config, err := appService.venuePrintConfigService.FindVenuePrintConfigByVenueAndTemplate(
		ctx, *reqDto.VenueId, *reqDto.TemplateCode)
	if err != nil {
		util.Wlog(ctx).Errorf("获取门店打印配置失败: %v", err)
		return nil, err
	}

	// 转换为VO（包含关联的模板信息）
	configVO, err := appService.convertVenuePrintConfigToVO(ctx, config, true)
	if err != nil {
		util.Wlog(ctx).Errorf("转换VO失败: %v", err)
		return nil, err
	}

	util.Wlog(ctx).Infof("成功获取门店打印配置: %s-%s", *reqDto.VenueId, *reqDto.TemplateCode)
	return configVO, nil
}

// ListVenuePrintConfigs 获取门店所有打印配置列表
func (appService *VenuePrintConfigAppService) ListVenuePrintConfigs(ctx *gin.Context, reqDto *req.ListVenuePrintConfigsReqDto) ([]vo.VenuePrintConfigVO, error) {
	util.Wlog(ctx).Infof("开始获取门店打印配置列表: %s", *reqDto.VenueId)

	// 根据是否只查询启用的配置来调用不同方法
	var configList *[]po.VenuePrintConfig
	var err error

	if reqDto.IsEnabled != nil && *reqDto.IsEnabled {
		configList, err = appService.venuePrintConfigService.FindEnabledVenuePrintConfigsByVenue(ctx, *reqDto.VenueId)
	} else {
		configList, err = appService.venuePrintConfigService.FindVenuePrintConfigsByVenue(ctx, *reqDto.VenueId)
	}

	if err != nil {
		util.Wlog(ctx).Errorf("获取门店打印配置列表失败: %v", err)
		return nil, err
	}

	// 转换为VO
	var configVOs []vo.VenuePrintConfigVO
	if configList != nil {
		for _, config := range *configList {
			configVO, err := appService.convertVenuePrintConfigToVO(ctx, &config, false)
			if err != nil {
				util.Wlog(ctx).Errorf("转换VO失败: %v", err)
				continue
			}
			configVOs = append(configVOs, *configVO)
		}
	}

	util.Wlog(ctx).Infof("成功获取门店打印配置列表: %s, 共%d个", *reqDto.VenueId, len(configVOs))
	return configVOs, nil
}

// UpdateVenuePrintConfig 更新门店打印配置
func (appService *VenuePrintConfigAppService) UpdateVenuePrintConfig(ctx *gin.Context, reqDto *req.UpdateVenuePrintConfigReqDto) (*vo.VenuePrintConfigVO, error) {
	util.Wlog(ctx).Infof("开始更新门店打印配置: %s-%s", *reqDto.VenueId, *reqDto.TemplateCode)

	// 验证模板编码是否支持
	templateCode := printmeta.TemplateCode(*reqDto.TemplateCode)
	_, err := appService.printingDomainService.GetMetaTemplateByCode(ctx, templateCode)
	if err != nil {
		util.Wlog(ctx).Errorf("不支持的模板编码: %s", *reqDto.TemplateCode)
		return nil, err
	}

	// 验证布局模板是否存在
	_, err = appService.printTemplateService.FindPrintTemplateById(ctx, *reqDto.SelectedTemplateId)
	if err != nil {
		util.Wlog(ctx).Errorf("布局模板不存在: %s", *reqDto.SelectedTemplateId)
		return nil, err
	}

	// 验证业务配置
	if reqDto.BusinessConfig != nil {
		err = appService.printingDomainService.ValidateBusinessConfig(ctx, templateCode, reqDto.BusinessConfig)
		if err != nil {
			util.Wlog(ctx).Errorf("业务配置验证失败: %v", err)
			return nil, err
		}
	}

	// 查找现有配置
	existingConfig, err := appService.venuePrintConfigService.FindVenuePrintConfigByVenueAndTemplate(
		ctx, *reqDto.VenueId, *reqDto.TemplateCode)

	// 序列化业务配置
	var businessConfigJSON *string
	if reqDto.BusinessConfig != nil {
		configBytes, err := json.Marshal(reqDto.BusinessConfig)
		if err != nil {
			util.Wlog(ctx).Errorf("业务配置JSON序列化失败: %v", err)
			return nil, err
		}
		configStr := string(configBytes)
		businessConfigJSON = &configStr
	}

	var resultConfig *po.VenuePrintConfig

	if err != nil {
		// 配置不存在，创建新的
		resultConfig = appService.createNewVenuePrintConfig(reqDto, businessConfigJSON)
		err = appService.venuePrintConfigService.CreateVenuePrintConfig(ctx, resultConfig)
		if err != nil {
			util.Wlog(ctx).Errorf("创建门店打印配置失败: %v", err)
			return nil, err
		}
		util.Wlog(ctx).Infof("成功创建门店打印配置: %s-%s", *reqDto.VenueId, *reqDto.TemplateCode)
	} else {
		// 配置存在，更新
		resultConfig = appService.updateExistingVenuePrintConfig(existingConfig, reqDto, businessConfigJSON)
		err = appService.venuePrintConfigService.UpdateVenuePrintConfigPartial(ctx, resultConfig)
		if err != nil {
			util.Wlog(ctx).Errorf("更新门店打印配置失败: %v", err)
			return nil, err
		}
		util.Wlog(ctx).Infof("成功更新门店打印配置: %s-%s", *reqDto.VenueId, *reqDto.TemplateCode)
	}

	// 转换为VO返回
	configVO, err := appService.convertVenuePrintConfigToVO(ctx, resultConfig, true)
	if err != nil {
		util.Wlog(ctx).Errorf("转换VO失败: %v", err)
		return nil, err
	}

	return configVO, nil
}

// createNewVenuePrintConfig 创建新的门店打印配置
func (appService *VenuePrintConfigAppService) createNewVenuePrintConfig(reqDto *req.UpdateVenuePrintConfigReqDto, businessConfigJSON *string) *po.VenuePrintConfig {
	newConfig := &po.VenuePrintConfig{
		VenueId:            reqDto.VenueId,
		TemplateCode:       reqDto.TemplateCode,
		SelectedTemplateId: reqDto.SelectedTemplateId,
		BusinessConfig:     businessConfigJSON,
		Remark:             reqDto.Remark,
	}

	// 设置默认值
	if reqDto.IsEnabled != nil {
		newConfig.IsEnabled = reqDto.IsEnabled
	} else {
		newConfig.IsEnabled = util.GetBoolPtr(true)
	}

	if reqDto.Copies != nil && *reqDto.Copies > 0 {
		newConfig.Copies = reqDto.Copies
	} else {
		newConfig.Copies = util.GetItPtr(1)
	}

	return newConfig
}

// updateExistingVenuePrintConfig 更新现有的门店打印配置
func (appService *VenuePrintConfigAppService) updateExistingVenuePrintConfig(existingConfig *po.VenuePrintConfig, reqDto *req.UpdateVenuePrintConfigReqDto, businessConfigJSON *string) *po.VenuePrintConfig {
	updateConfig := &po.VenuePrintConfig{
		Id:                 existingConfig.Id,
		VenueId:            reqDto.VenueId,
		TemplateCode:       reqDto.TemplateCode,
		SelectedTemplateId: reqDto.SelectedTemplateId,
		BusinessConfig:     businessConfigJSON,
		Remark:             reqDto.Remark,
	}

	// 设置默认值
	if reqDto.IsEnabled != nil {
		updateConfig.IsEnabled = reqDto.IsEnabled
	} else {
		updateConfig.IsEnabled = existingConfig.IsEnabled
	}

	if reqDto.Copies != nil && *reqDto.Copies > 0 {
		updateConfig.Copies = reqDto.Copies
	} else {
		updateConfig.Copies = existingConfig.Copies
	}

	return updateConfig
}

// convertVenuePrintConfigToVO 转换门店打印配置为VO
func (appService *VenuePrintConfigAppService) convertVenuePrintConfigToVO(ctx *gin.Context, config *po.VenuePrintConfig, includeTemplate bool) (*vo.VenuePrintConfigVO, error) {
	// 解析业务配置JSON
	var businessConfig map[string]interface{}
	if config.BusinessConfig != nil && *config.BusinessConfig != "" {
		err := json.Unmarshal([]byte(*config.BusinessConfig), &businessConfig)
		if err != nil {
			util.Wlog(ctx).Errorf("解析业务配置JSON失败: %v", err)
			businessConfig = make(map[string]interface{})
		}
	} else {
		businessConfig = make(map[string]interface{})
	}

	configVO := &vo.VenuePrintConfigVO{
		Id:                 config.GetId(),
		VenueId:            util.GetStringValue(config.VenueId),
		TemplateCode:       util.GetStringValue(config.TemplateCode),
		SelectedTemplateId: util.GetStringValue(config.SelectedTemplateId),
		IsEnabled:          util.GetBoolValue(config.IsEnabled),
		Copies:             util.GetIntValue(config.Copies),
		BusinessConfig:     businessConfig,
		Remark:             util.GetStringValue(config.Remark),
		Ctime:              util.GetInt64Value(config.Ctime),
		Utime:              util.GetInt64Value(config.Utime),
	}

	// 如果需要包含模板信息，则获取关联的布局模板
	if includeTemplate && config.SelectedTemplateId != nil && *config.SelectedTemplateId != "" {
		template, err := appService.printTemplateService.FindPrintTemplateById(ctx, *config.SelectedTemplateId)
		if err == nil {
			templateVO := appService.convertPrintTemplateToVO(template)
			configVO.SelectedTemplate = &templateVO
		}

		// 如果包含模板信息，同时包含配置元数据
		if config.TemplateCode != nil && *config.TemplateCode != "" {
			templateCode := printmeta.TemplateCode(*config.TemplateCode)
			metaTemplate, err := appService.printingDomainService.GetMetaTemplateByCode(ctx, templateCode)
			if err == nil {
				configVO.ConfigMetaData = metaTemplate.ConfigOptions
			}
		}
	}

	return configVO, nil
}

// GetMetaTemplateConfig 获取元模板配置元数据
func (appService *VenuePrintConfigAppService) GetMetaTemplateConfig(ctx *gin.Context, reqDto *req.GetMetaTemplateConfigReqDto) (*vo.MetaTemplateConfigVO, error) {
	util.Wlog(ctx).Infof("开始获取元模板配置元数据: %s", *reqDto.TemplateCode)

	// 获取元模板
	templateCode := printmeta.TemplateCode(*reqDto.TemplateCode)
	metaTemplate, err := appService.printingDomainService.GetMetaTemplateByCode(ctx, templateCode)
	if err != nil {
		util.Wlog(ctx).Errorf("获取元模板失败: %v", err)
		return nil, err
	}

	// 转换为VO
	configVO := &vo.MetaTemplateConfigVO{
		TemplateCode:  string(metaTemplate.TemplateCode),
		Name:          metaTemplate.Name,
		Description:   metaTemplate.Description,
		ConfigOptions: metaTemplate.ConfigOptions,
		DefaultConfig: metaTemplate.GetDefaultBusinessConfig(),
	}

	util.Wlog(ctx).Infof("成功获取元模板配置元数据: %s", *reqDto.TemplateCode)
	return configVO, nil
}

// convertPrintTemplateToVO 转换布局模板为VO
func (appService *VenuePrintConfigAppService) convertPrintTemplateToVO(template *po.PrintTemplate) vo.PrintTemplateVO {
	return vo.PrintTemplateVO{
		Id:              template.GetId(),
		TemplateCode:    util.GetStringValue(template.TemplateCode),
		Name:            util.GetStringValue(template.Name),
		LayoutContent:   util.GetStringValue(template.LayoutContent),
		IsSystemDefault: util.GetBoolValue(template.IsSystemDefault),
		IsEnabled:       util.GetBoolValue(template.IsEnabled),
		Remark:          util.GetStringValue(template.Remark),
		Ctime:           util.GetInt64Value(template.Ctime),
		Utime:           util.GetInt64Value(template.Utime),
	}
}
