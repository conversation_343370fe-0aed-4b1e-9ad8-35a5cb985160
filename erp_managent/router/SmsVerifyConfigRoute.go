package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type SmsVerifyConfigRoute struct {
}

func (r *SmsVerifyConfigRoute) InitSmsVerifyConfigRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	smsVerifyConfigController := controller.SmsVerifyConfigController{}
	route := g.Group("/api/smsVerifyConfig")
	{
		route.POST("/get", smsVerifyConfigController.GetSmsVerifyConfig)                 // 获取配置
		route.POST("/update", smsVerifyConfigController.UpdateSmsVerifyConfig)           // 更新单个配置
		route.POST("/batchUpdate", smsVerifyConfigController.BatchUpdateSmsVerifyConfig) // 批量更新配置
		route.POST("/delete", smsVerifyConfigController.DeleteSmsVerifyConfig)           // 删除配置
	}
}
