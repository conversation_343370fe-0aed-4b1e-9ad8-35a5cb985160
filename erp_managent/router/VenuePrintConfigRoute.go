package router

import (
	"voderpltvv/erp_managent/controller"

	"github.com/gin-gonic/gin"
)

type VenuePrintConfigRoute struct {
}

func (s *VenuePrintConfigRoute) InitVenuePrintConfigRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	venuePrintConfigController := controller.NewVenuePrintConfigController()
	route := g.Group("")
	{
		route.POST("/api/venue-print-config/get", venuePrintConfigController.GetVenuePrintConfig)                    // 获取门店打印配置
		route.POST("/api/venue-print-config/list", venuePrintConfigController.ListVenuePrintConfigs)                 // 获取门店所有打印配置列表
		route.POST("/api/venue-print-config/update", venuePrintConfigController.UpdateVenuePrintConfig)              // 更新门店打印配置
		route.POST("/api/venue-print-config/meta-template-config", venuePrintConfigController.GetMetaTemplateConfig) // 获取元模板配置元数据
	}
}
