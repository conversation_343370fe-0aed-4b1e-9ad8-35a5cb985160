package controller

import (
	"voderpltvv/erp_managent/api/req"
	_ "voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/print"

	"github.com/gin-gonic/gin"
)

type VenuePrintConfigController struct {
	venuePrintConfigAppService *print.VenuePrintConfigAppService
}

func NewVenuePrintConfigController() *VenuePrintConfigController {
	return &VenuePrintConfigController{
		venuePrintConfigAppService: print.NewVenuePrintConfigAppService(),
	}
}

// GetVenuePrintConfig 获取门店打印配置
// @Summary 获取门店打印配置
// @Description 根据门店ID和模板编码获取门店的打印配置信息，包含关联的布局模板详情
// @Tags 门店打印配置管理
// @Accept json
// @Produce json
// @Param body body req.GetVenuePrintConfigReqDto true "请求体"
// @Success 200 {object} Result[vo.VenuePrintConfigVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 404 {object} Result[any] "未找到门店打印配置"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/venue-print-config/get [post]
func (controller *VenuePrintConfigController) GetVenuePrintConfig(ctx *gin.Context) {
	reqDto := &req.GetVenuePrintConfigReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	if reqDto.TemplateCode == nil || *reqDto.TemplateCode == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板编码不能为空")
		return
	}

	// 调用应用服务
	configVO, err := controller.venuePrintConfigAppService.GetVenuePrintConfig(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.NotFound.Code, "未找到门店打印配置")
		return
	}

	Result_success[any](ctx, configVO)
}

// ListVenuePrintConfigs 获取门店所有打印配置列表
// @Summary 获取门店所有打印配置列表
// @Description 获取指定门店的所有打印配置列表，支持按启用状态筛选
// @Tags 门店打印配置管理
// @Accept json
// @Produce json
// @Param body body req.ListVenuePrintConfigsReqDto true "请求体"
// @Success 200 {object} Result[[]vo.VenuePrintConfigVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/venue-print-config/list [post]
func (controller *VenuePrintConfigController) ListVenuePrintConfigs(ctx *gin.Context) {
	reqDto := &req.ListVenuePrintConfigsReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	// 调用应用服务
	configVOs, err := controller.venuePrintConfigAppService.ListVenuePrintConfigs(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, configVOs)
}

// UpdateVenuePrintConfig 更新门店打印配置
// @Summary 更新门店打印配置
// @Description 更新门店的打印配置，包括选择的布局模板、业务配置、打印份数等。如果配置不存在则自动创建
// @Tags 门店打印配置管理
// @Accept json
// @Produce json
// @Param body body req.UpdateVenuePrintConfigReqDto true "请求体"
// @Success 200 {object} Result[vo.VenuePrintConfigVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/venue-print-config/update [post]
func (controller *VenuePrintConfigController) UpdateVenuePrintConfig(ctx *gin.Context) {
	reqDto := &req.UpdateVenuePrintConfigReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	if reqDto.TemplateCode == nil || *reqDto.TemplateCode == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板编码不能为空")
		return
	}

	if reqDto.SelectedTemplateId == nil || *reqDto.SelectedTemplateId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "选择的模板ID不能为空")
		return
	}

	// 调用应用服务
	configVO, err := controller.venuePrintConfigAppService.UpdateVenuePrintConfig(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, configVO)
}

// GetMetaTemplateConfig 获取元模板配置元数据
// @Summary 获取元模板配置元数据
// @Description 根据模板编码获取元模板的配置元数据，用于前端动态生成配置表单
// @Tags 门店打印配置管理
// @Accept json
// @Produce json
// @Param body body req.GetMetaTemplateConfigReqDto true "请求体"
// @Success 200 {object} Result[vo.MetaTemplateConfigVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 404 {object} Result[any] "未找到元模板"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/venue-print-config/meta-template-config [post]
func (controller *VenuePrintConfigController) GetMetaTemplateConfig(ctx *gin.Context) {
	reqDto := &req.GetMetaTemplateConfigReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	if reqDto.TemplateCode == nil || *reqDto.TemplateCode == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板编码不能为空")
		return
	}

	// 调用应用服务
	configVO, err := controller.venuePrintConfigAppService.GetMetaTemplateConfig(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.NotFound.Code, "未找到元模板配置")
		return
	}

	Result_success[any](ctx, configVO)
}
