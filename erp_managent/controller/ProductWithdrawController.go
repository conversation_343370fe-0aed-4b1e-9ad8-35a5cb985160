package controller

import (
	"encoding/json"
	"io/ioutil"
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/erp_managent/service/transfer"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type ProductWithdrawController struct{}

var (
	productWithdrawService  = impl.NewProductWithdrawService()
	productWithdrawTransfer = transfer.ProductWithdrawTransfer{}
)

// @Summary 添加商品提取记录
// @Description 添加商品提取记录。取酒成功后会自动创建打印记录，可通过返回的取酒单号作为printBusinessID查询打印单。
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.AddProductWithdrawReqDto true "请求体"
// @Success 200 {object} Result[vo.ProductWithdrawResultVO] "成功。返回取酒结果，包含printBusinessId用于查询和重打取酒单"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product-withdraw/add [post]
func (controller *ProductWithdrawController) AddProductWithdraw(ctx *gin.Context) {
	reqDto := req.AddProductWithdrawReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 先检查存储记录是否存在及数量是否足够
	storageId := reqDto.StorageId
	storageService := impl.ProductStorageService{}
	storage, err := storageService.FindProductStorageById(ctx, storageId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if storage == nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "商品存储记录不存在")
		return
	}

	quantity := reqDto.Quantity
	if *storage.RemainingQty < quantity {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "提取数量超过剩余数量")
		return
	}
	productWithdraw := po.ProductWithdraw{}

	// 转换字符串字段
	productWithdraw.StorageId = &storageId
	productWithdraw.VenueId = storage.VenueId
	productWithdraw.CustomerId = storage.CustomerId
	productWithdraw.CustomerName = storage.CustomerName
	productWithdraw.ProductId = storage.ProductId
	productWithdraw.ProductName = storage.ProductName

	// 从存储记录获取存酒单号，如果DTO中提供了则优先使用DTO中的值
	if reqDto.StorageOrderNo != "" {
		productWithdraw.StorageOrderNo = &reqDto.StorageOrderNo
	} else {
		productWithdraw.StorageOrderNo = storage.OrderNo
	}

	// 转换数字字段
	productWithdraw.Quantity = &quantity

	// 处理其他字段
	withdrawTime := reqDto.WithdrawTime
	productWithdraw.WithdrawTime = &withdrawTime

	// 使用 DTO 中的 DeliveryRoomId/DeliveryRoomName 直接赋值给 PO 的同名字段
	deliveryRoomId := reqDto.DeliveryRoomId // Read from updated DTO field
	productWithdraw.DeliveryRoomId = &deliveryRoomId

	deliveryRoomName := reqDto.DeliveryRoomName // Read from updated DTO field
	productWithdraw.DeliveryRoomName = &deliveryRoomName

	orderNumber := reqDto.OrderNumber
	productWithdraw.OrderNumber = &orderNumber

	remark := reqDto.Remark
	productWithdraw.Remark = &remark

	operatorId := reqDto.OperatorId
	operatorName := reqDto.OperatorName
	productWithdraw.OperatorId = &operatorId
	productWithdraw.OperatorName = &operatorName

	// 更新存储记录的剩余数量
	remainingQty := *storage.RemainingQty - quantity
	storage.RemainingQty = &remainingQty

	// 处理 SendSms 逻辑：默认发送短信(nil)，只有明确传false才不发送
	shouldSendSms := reqDto.SendSms == nil || *reqDto.SendSms
	err = productWithdrawService.CreateProductWithdrawAndUpdateStorage(ctx, &productWithdraw, storage, shouldSendSms)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 构建包含printBusinessId的返回结果
	result := vo.ProductWithdrawResultVO{
		Success:         true,
		Message:         "取酒成功",
		PrintBusinessId: lo.FromPtrOr(productWithdraw.OrderNo, ""), // 使用取酒单号作为printBusinessId
	}

	Result_success[any](ctx, result)
}

// @Summary 查询商品提取记录
// @Description 查询商品提取记录，支持多种条件搜索和分页查询
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.QueryProductWithdrawReqDto true "请求体，包含查询条件和分页参数"
// @Success 200 {object} Result[vo.PageVO[[]vo.ProductWithdrawVO]] "成功，返回分页结果"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product-withdraw/query [post]
func (controller *ProductWithdrawController) QueryProductWithdraws(ctx *gin.Context) {
	reqDto := req.QueryProductWithdrawReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换DTO为指针类型
	dtoPtr := &req.QueryProductWithdrawReqDto{
		Id:                reqDto.Id,
		StorageId:         reqDto.StorageId,
		VenueId:           reqDto.VenueId,
		CustomerId:        reqDto.CustomerId,
		CustomerName:      reqDto.CustomerName,
		PhoneNumber:       reqDto.PhoneNumber,
		OrderNumber:       reqDto.OrderNumber,
		ProductId:         reqDto.ProductId,
		ProductName:       reqDto.ProductName,
		RoomId:            reqDto.RoomId,
		PageNum:           reqDto.PageNum,
		PageSize:          reqDto.PageSize,
		SearchText:        reqDto.SearchText,
		WithdrawTimeStart: reqDto.WithdrawTimeStart,
		WithdrawTimeEnd:   reqDto.WithdrawTimeEnd,
	}

	// 设置默认的分页参数
	if dtoPtr.PageNum <= 0 {
		dtoPtr.PageNum = 1
	}
	if dtoPtr.PageSize <= 0 {
		dtoPtr.PageSize = 200
	}

	// 获取记录列表
	list, err := productWithdrawService.FindAllProductWithdraw(ctx, dtoPtr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换为VO对象
	voList := []vo.ProductWithdrawVO{}
	for _, v := range list {
		voList = append(voList, productWithdrawTransfer.PoToVo(*v))
	}

	// 获取总数以支持分页
	total, err := productWithdrawService.CountProductWithdraw(ctx, dtoPtr)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 使用统一的PageVO结构返回结果
	page := vo.PageVO[[]vo.ProductWithdrawVO]{}
	page.PageNum = dtoPtr.PageNum
	page.PageSize = dtoPtr.PageSize
	page.Total = total
	page.Data = voList

	Result_success[any](ctx, page)
}

// @Summary 批量添加商品提取记录
// @Description 批量添加商品提取记录，支持一次性从多个存储记录中提取酒水。批量取酒成功后会自动创建打印记录，可通过返回的批次号作为printBusinessID查询打印单。
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.BatchAddProductWithdrawReqDto true "请求体"
// @Success 200 {object} Result[vo.BatchProductWithdrawResultVO] "成功。返回批量取酒结果，包含printBusinessId用于查询和重打取酒单"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product-withdraw/batch-add [post]
func (controller *ProductWithdrawController) BatchAddProductWithdraw(ctx *gin.Context) {
	// 读取请求体内容
	bodyBytes, err := ioutil.ReadAll(ctx.Request.Body)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "读取请求体失败")
		return
	}

	// 将请求体内容解析为map，用于检查字段
	var rawRequest map[string]interface{}
	if err := json.Unmarshal(bodyBytes, &rawRequest); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "请求格式错误")
		return
	}

	// 直接从map构建请求DTO
	reqDto := &req.BatchAddProductWithdrawReqDto{}

	// 处理基本字段
	if v, ok := rawRequest["venueId"].(string); ok {
		reqDto.VenueId = &v
	}
	if v, ok := rawRequest["withdrawTime"].(float64); ok { // JSON numbers are float64
		t := int64(v)
		reqDto.WithdrawTime = &t
	}

	if v, ok := rawRequest["remark"].(string); ok {
		reqDto.Remark = &v
	}
	// 使用 delivery 字段
	if v, ok := rawRequest["deliveryRoomId"].(string); ok {
		reqDto.DeliveryRoomId = &v
	}
	if v, ok := rawRequest["deliveryRoomName"].(string); ok {
		reqDto.DeliveryRoomName = &v
	}
	// 处理操作人信息
	if v, ok := rawRequest["operatorId"].(string); ok {
		reqDto.OperatorId = &v
	}
	if v, ok := rawRequest["operatorName"].(string); ok {
		reqDto.OperatorName = &v
	}
	// 处理订单号
	if v, ok := rawRequest["orderNo"].(string); ok {
		reqDto.OrderNo = &v
	}
	// 处理发送短信字段
	if v, ok := rawRequest["sendSms"].(bool); ok {
		reqDto.SendSms = &v
	}

	// 处理withdrawItems
	if items, ok := rawRequest["withdrawItems"].([]interface{}); ok {
		for _, item := range items {
			if itemMap, ok := item.(map[string]interface{}); ok {
				withdrawItem := req.BatchWithdrawItem{}

				if v, ok := itemMap["storageId"].(string); ok {
					withdrawItem.StorageId = &v
				}
				if v, ok := itemMap["storageOrderNo"].(string); ok {
					withdrawItem.StorageOrderNo = &v
				}
				if v, ok := itemMap["quantity"].(float64); ok {
					q := int(v)
					withdrawItem.Quantity = &q
				}
				if v, ok := itemMap["productUnit"].(string); ok {
					withdrawItem.ProductUnit = &v
				}
				if v, ok := itemMap["productSpec"].(string); ok {
					withdrawItem.ProductSpec = &v
				}

				reqDto.WithdrawItems = append(reqDto.WithdrawItems, withdrawItem)
			}
		}
	}

	// 参数校验
	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "场所ID不能为空")
		return
	}

	if reqDto.WithdrawItems == nil || len(reqDto.WithdrawItems) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "提取项列表不能为空")
		return
	}

	// 调用服务层执行批量提取
	result, err := productWithdrawService.BatchAddProductWithdraw(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}

// @Summary 查询客户可取商品列表
// @Description 根据客户信息（手机号/姓名/ID）查询可取商品列表
// @Tags 商品存储
// @Accept json
// @Produce json
// @Param body body req.QueryWithdrawableItemsReqDto true "请求体"
// @Success 200 {object} Result[vo.WithdrawableItemsVO] "成功"
// @Failure 500 {object} Result[any] "失败"
// @Router /api/product-withdraw/query-withdrawable-items [post]
func (controller *ProductWithdrawController) QueryWithdrawableItems(ctx *gin.Context) {
	reqDto := req.QueryWithdrawableItemsReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 参数校验
	if reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	if reqDto.SearchValue == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "搜索值不能为空")
		return
	}

	// 设置默认值
	if reqDto.ExpireDays == 0 {
		reqDto.ExpireDays = 30 // 默认30天内视为即将到期
	}

	// 调用服务层查询可取商品列表
	result, err := productWithdrawService.FindWithdrawableItems(ctx, &reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, result)
}
