package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
)

type SmsVerifyConfigController struct{}

var (
	smsVerifyConfigService = impl.SmsVerifyConfigService{}
)

// @Summary 获取短信验证配置
// @Description 获取短信验证配置，支持获取单个配置或所有配置
// @Tags 短信验证配置
// @Accept json
// @Produce json
// @Param body body req.SmsVerifyConfigGetReqDto true "请求参数"
// @Success 200 {object} Result[vo.SmsVerifyConfigListVO] "成功返回配置列表"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/smsVerifyConfig/get [post]
func (controller *SmsVerifyConfigController) GetSmsVerifyConfig(ctx *gin.Context) {
	reqDto := req.SmsVerifyConfigGetReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	var configs []*po.SmsVerifyConfig
	var err2 error

	if reqDto.ConfigKey != nil && *reqDto.ConfigKey != "" {
		// 获取单个配置
		config, err := smsVerifyConfigService.GetConfigByKey(ctx, *reqDto.VenueId, *reqDto.ConfigKey)
		if err != nil {
			if err.Error() == "record not found" {
				// 配置不存在，返回空列表
				listVO := vo.SmsVerifyConfigListVO{
					List: []vo.SmsVerifyConfigVO{},
				}
				Result_success[any](ctx, listVO)
				return
			}
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
			return
		}
		configs = []*po.SmsVerifyConfig{config}
	} else {
		// 获取所有配置
		configs, err2 = smsVerifyConfigService.GetConfigsByVenue(ctx, *reqDto.VenueId)
		if err2 != nil {
			Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err2.Error())
			return
		}
	}

	configVOs := make([]vo.SmsVerifyConfigVO, 0)
	for _, config := range configs {
		configVO := vo.SmsVerifyConfigVO{
			Id:          lo.FromPtrOr(config.Id, ""),
			VenueId:     lo.FromPtrOr(config.VenueId, ""),
			ConfigKey:   lo.FromPtrOr(config.ConfigKey, ""),
			Enabled:     lo.FromPtrOr(config.Enabled, 0),
			Description: lo.FromPtrOr(config.Description, ""),
			Remark:      lo.FromPtrOr(config.Remark, ""),
			Ctime:       lo.FromPtrOr(config.Ctime, int64(0)),
			Utime:       lo.FromPtrOr(config.Utime, int64(0)),
			State:       lo.FromPtrOr(config.State, 0),
			Version:     lo.FromPtrOr(config.Version, 0),
		}
		configVOs = append(configVOs, configVO)
	}

	listVO := vo.SmsVerifyConfigListVO{
		List: configVOs,
	}

	Result_success[any](ctx, listVO)
}

// @Summary 更新短信验证配置
// @Description 更新短信验证配置，支持创建新配置或更新现有配置
// @Tags 短信验证配置
// @Accept json
// @Produce json
// @Param body body req.SmsVerifyConfigUpdateReqDto true "请求参数"
// @Success 200 {object} Result[string] "成功返回成功消息"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/smsVerifyConfig/update [post]
func (controller *SmsVerifyConfigController) UpdateSmsVerifyConfig(ctx *gin.Context) {
	reqDto := req.SmsVerifyConfigUpdateReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.VenueId == nil || *reqDto.VenueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}
	if reqDto.ConfigKey == nil || *reqDto.ConfigKey == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "配置键不能为空")
		return
	}

	config := &po.SmsVerifyConfig{
		VenueId:   reqDto.VenueId,
		ConfigKey: reqDto.ConfigKey,
	}

	if reqDto.Enabled != nil {
		config.Enabled = reqDto.Enabled
	}
	if reqDto.Description != nil {
		config.Description = reqDto.Description
	}
	if reqDto.Remark != nil {
		config.Remark = reqDto.Remark
	}

	err = smsVerifyConfigService.CreateOrUpdateConfig(ctx, config)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, "配置更新成功")
}

// @Summary 批量更新短信验证配置
// @Description 批量更新短信验证配置，支持同时创建或更新多个配置
// @Tags 短信验证配置
// @Accept json
// @Produce json
// @Param body body req.SmsVerifyConfigBatchUpdateReqDto true "请求参数"
// @Success 200 {object} Result[string] "成功返回成功消息"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/smsVerifyConfig/batchUpdate [post]
func (controller *SmsVerifyConfigController) BatchUpdateSmsVerifyConfig(ctx *gin.Context) {
	reqDto := req.SmsVerifyConfigBatchUpdateReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if len(reqDto.Configs) == 0 {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "配置列表不能为空")
		return
	}

	// 验证所有配置的门店ID是否一致
	venueId := reqDto.Configs[0].VenueId
	if venueId == nil || *venueId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
		return
	}

	configs := make([]*po.SmsVerifyConfig, 0, len(reqDto.Configs))
	for _, configReq := range reqDto.Configs {
		if configReq.ConfigKey == nil || *configReq.ConfigKey == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "配置键不能为空")
			return
		}
		if configReq.VenueId == nil || *configReq.VenueId == "" {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "门店ID不能为空")
			return
		}
		if *configReq.VenueId != *venueId {
			Result_fail[any](ctx, GeneralCodes.ParamError.Code, "批量更新时所有配置的门店ID必须一致")
			return
		}

		config := &po.SmsVerifyConfig{
			VenueId:   configReq.VenueId,
			ConfigKey: configReq.ConfigKey,
		}

		if configReq.Enabled != nil {
			config.Enabled = configReq.Enabled
		}
		if configReq.Description != nil {
			config.Description = configReq.Description
		}
		if configReq.Remark != nil {
			config.Remark = configReq.Remark
		}

		configs = append(configs, config)
	}

	err = smsVerifyConfigService.BatchCreateOrUpdateConfigs(ctx, configs)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, "批量配置更新成功")
}

// @Summary 删除短信验证配置
// @Description 删除指定的短信验证配置
// @Tags 短信验证配置
// @Accept json
// @Produce json
// @Param body body req.SmsVerifyConfigDeleteReqDto true "请求参数"
// @Success 200 {object} Result[string] "成功返回成功消息"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器内部错误"
// @Router /api/smsVerifyConfig/delete [post]
func (controller *SmsVerifyConfigController) DeleteSmsVerifyConfig(ctx *gin.Context) {
	reqDto := req.SmsVerifyConfigDeleteReqDto{}
	err := ctx.ShouldBindJSON(&reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	if reqDto.ConfigId == nil || *reqDto.ConfigId == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "配置ID不能为空")
		return
	}

	err = smsVerifyConfigService.DeleteConfig(ctx, *reqDto.ConfigId)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, "短信验证配置删除成功")
}
