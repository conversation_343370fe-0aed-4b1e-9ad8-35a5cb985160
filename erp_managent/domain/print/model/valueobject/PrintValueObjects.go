package valueobject

import (
	_const "voderpltvv/const"
)

// ProductInfo 商品信息值对象
type ProductInfo struct {
	ProductName string  `json:"productName"` // 商品名称
	Flavors     string  `json:"flavors"`     // 口味，可选
	Price       float64 `json:"price"`       // 单价
	PayPrice    float64 `json:"payPrice"`    // 支付价/现价
	Quantity    int     `json:"quantity"`    // 数量
	Unit        string  `json:"unit"`        // 单位
	TotalAmount float64 `json:"totalAmount"` // 小计（元）
}

// PackageSubProductInfo 套餐子商品信息值对象
type PackageSubProductInfo struct {
	ProductName string `json:"productName"` // 商品名称
	Quantity    int    `json:"quantity"`    // 数量
	Unit        string `json:"unit"`        // 单位
}

// ExtendedProductInfo 扩展商品信息值对象
type ExtendedProductInfo struct {
	ProductInfo
	SubProducts []PackageSubProductInfo `json:"subProducts,omitempty"` // 套餐子商品
}

// ProductionOrderItem 出品单商品信息值对象
type ProductionOrderItem struct {
	ExtendedProductInfo // 继承扩展商品信息
	// 可以添加出品单特有的商品属性
}

// RoomInfo 房间信息值对象
type RoomInfo struct {
	Name string `json:"name,omitempty"` // 房间名称
}

// ReservationInfo 预订信息值对象
type ReservationInfo struct {
	MemberCardNo      string `json:"memberCardNo,omitempty"`      // 预订会员卡号
	MemberName        string `json:"memberName,omitempty"`        // 预定会员名称
	MemberPhone       string `json:"memberPhone,omitempty"`       // 预定会员手机号
	ProxyOrderer      string `json:"proxyOrderer,omitempty"`      // 代订人
	ProxyOrdererPhone string `json:"proxyOrdererPhone,omitempty"` // 代订人手机号
}

// RoomPackageInfo 房间方案信息值对象
type RoomPackageInfo struct {
	PlanName      string  `json:"planName"`      // 方案名称
	Duration      int     `json:"duration"`      // 时长（分钟）
	OriginalPrice float64 `json:"originalPrice"` // 原价
	ActualPrice   float64 `json:"actualPrice"`   // 现价
}

// GiftProductInfo 赠品信息值对象
type GiftProductInfo struct {
	ProductName string                  `json:"productName"`           // 商品名称
	Quantity    int                     `json:"quantity"`              // 数量
	Unit        string                  `json:"unit"`                  // 单位
	Price       float64                 `json:"price"`                 // 原价
	Remark      string                  `json:"remark,omitempty"`      // 备注，例如"赠送"
	SubProducts []PackageSubProductInfo `json:"subProducts,omitempty"` // 套餐子商品
}

// PaymentDetail 支付方式明细值对象
type PaymentDetail struct {
	Method string  `json:"method"` // 支付方式名称
	Amount float64 `json:"amount"` // 金额
}

// PrintType 打印类型值对象
type PrintType string

const (
	PrintTypeOpenTable       PrintType = _const.PRINT_TYPE_OPEN_TABLE        // 开台单
	PrintTypeCancelOpenTable PrintType = _const.PRINT_TYPE_CANCEL_OPEN_TABLE // 取消开台单
	PrintTypeRoomExtension   PrintType = _const.PRINT_TYPE_ROOM_EXTENSION    // 续房单
	PrintTypeProductOut      PrintType = _const.PRINT_TYPE_PRODUCT_OUT       // 出品单
	PrintTypeCheckout        PrintType = _const.PRINT_TYPE_CHECKOUT          // 结账单
	PrintTypeShiftChange     PrintType = _const.PRINT_TYPE_SHIFT_CHANGE      // 交班单
	PrintTypeRefund          PrintType = _const.PRINT_TYPE_REFUND            // 退款单
	// 会员卡相关打印类型
	PrintTypeRecharge    PrintType = _const.PRINT_TYPE_RECHARGE     // 充值单
	PrintTypeOpenCard    PrintType = _const.PRINT_TYPE_OPEN_CARD    // 开卡单
	PrintTypeReplaceCard PrintType = _const.PRINT_TYPE_REPLACE_CARD // 补卡单
	PrintTypeRenewCard   PrintType = _const.PRINT_TYPE_RENEW_CARD   // 续卡单
	// 存取酒相关打印类型
	PrintTypeWineStorage  PrintType = _const.PRINT_TYPE_WINE_STORAGE  // 存酒单
	PrintTypeWineWithdraw PrintType = _const.PRINT_TYPE_WINE_WITHDRAW // 取酒单
	PrintTypeWineRenewal  PrintType = _const.PRINT_TYPE_WINE_RENEWAL  // 存酒续存单
)

// PrintStatus 打印状态值对象
type PrintStatus int

const (
	PrintStatusSuccess PrintStatus = _const.PRINT_STATUS_SUCCESS // 成功
	PrintStatusFailed  PrintStatus = _const.PRINT_STATUS_FAILED  // 失败
)

// BusinessSummary 营业数据汇总信息值对象
type BusinessSummary struct {
	Receivable               *float64 `json:"receivable,omitempty"`               // 营业应收
	ActualReceived           *float64 `json:"actualReceived,omitempty"`           // 营业实收
	NetProfit                *float64 `json:"netProfit,omitempty"`                // 营业净收
	MerchantDiscount         *float64 `json:"merchantDiscount,omitempty"`         // 商家优惠
	MemberDiscount           *float64 `json:"memberDiscount,omitempty"`           // 会员优惠
	RoomActualReceived       *float64 `json:"roomActualReceived,omitempty"`       // 包厢实收
	ProductActualReceived    *float64 `json:"productActualReceived,omitempty"`    // 商品实收
	CardCount                *int     `json:"cardCount,omitempty"`                // 办卡张数
	CardAmount               *float64 `json:"cardAmount,omitempty"`               // 办卡金额
	RechargeAmount           *float64 `json:"rechargeAmount,omitempty"`           // 充值金额
	RechargeBonusAmount      *float64 `json:"rechargeBonusAmount,omitempty"`      // 充值赠送
	CardReplacementAmount    *float64 `json:"cardReplacementAmount,omitempty"`    // 补卡金额
	RenewalFee               *float64 `json:"renewalFee,omitempty"`               // 续期费
	ChargeOffReceivable      *float64 `json:"chargeOffReceivable,omitempty"`      // 冲账应收
	ChargeOffActualReceived  *float64 `json:"chargeOffActualReceived,omitempty"`  // 冲账实收
	BillDeductionForProducts *float64 `json:"billDeductionForProducts,omitempty"` // 计费抵商品
	ProductDeductionForRoom  *float64 `json:"productDeductionForRoom,omitempty"`  // 商品抵房费
	CouponDiscount           *float64 `json:"couponDiscount,omitempty"`           // 优惠券优惠
	RoundingAmount           *float64 `json:"roundingAmount,omitempty"`           // 抹零金额
	EmployeeProductGift      *float64 `json:"employeeProductGift,omitempty"`      // 员工商品赠送
}

// AbnormalPaymentData 异常支付数据值对象
type AbnormalPaymentData struct {
	Amount *float64 `json:"amount,omitempty"` // 异常支付流水金额
	Count  *int     `json:"count,omitempty"`  // 异常支付流水数
}

// MemberInfo 客户信息值对象（包括会员和非会员客户）
// 对于非会员客户，CardNo和LevelName字段可能为空
type MemberInfo struct {
	MemberID    string `json:"memberID,omitempty"`    // 客户ID（统一标识，会员和非会员都有）
	MemberName  string `json:"memberName,omitempty"`  // 客户姓名
	MemberPhone string `json:"memberPhone,omitempty"` // 客户手机号
	CardNo      string `json:"cardNo,omitempty"`      // 会员卡号（非会员时为空）
	LevelName   string `json:"levelName,omitempty"`   // 会员等级名称（非会员时为空）
}

// WineStorageItem 存酒项目值对象
type WineStorageItem struct {
	ProductID     string `json:"productID"`     // 商品ID
	ProductName   string `json:"productName"`   // 商品名称
	Brand         string `json:"brand"`         // 品牌
	Specification string `json:"specification"` // 规格
	Quantity      int    `json:"quantity"`      // 存放数量
	Unit          string `json:"unit"`          // 单位
	ValidityDate  string `json:"validityDate"`  // 有效期（YYYY-MM-DD格式）
	StorageDate   string `json:"storageDate"`   // 存放日期（YYYY-MM-DD格式）
	Remark        string `json:"remark"`        // 备注
}

// WineWithdrawalItem 取酒项目值对象
type WineWithdrawalItem struct {
	ProductID      string `json:"productID"`      // 商品ID
	ProductName    string `json:"productName"`    // 商品名称
	Brand          string `json:"brand"`          // 品牌
	Specification  string `json:"specification"`  // 规格
	Quantity       int    `json:"quantity"`       // 取出数量
	Unit           string `json:"unit"`           // 单位
	StorageDate    string `json:"storageDate"`    // 原存放日期（YYYY-MM-DD格式）
	WithdrawalDate string `json:"withdrawalDate"` // 取出日期（YYYY-MM-DD格式）
	RemainQuantity int    `json:"remainQuantity"` // 剩余数量
	Remark         string `json:"remark"`         // 备注
}

// WineRenewalItem 续存项目值对象
type WineRenewalItem struct {
	ProductID          string `json:"productID"`          // 商品ID
	ProductName        string `json:"productName"`        // 商品名称
	Brand              string `json:"brand"`              // 品牌
	Specification      string `json:"specification"`      // 规格
	Quantity           int    `json:"quantity"`           // 寄存数量
	Unit               string `json:"unit"`               // 单位
	OriginalExpireDate string `json:"originalExpireDate"` // 原过期日期（YYYY-MM-DD格式）
	NewExpireDate      string `json:"newExpireDate"`      // 新过期日期（YYYY-MM-DD格式）
	ExtendedDays       int    `json:"extendedDays"`       // 延长天数
	Remark             string `json:"remark"`             // 备注
}
