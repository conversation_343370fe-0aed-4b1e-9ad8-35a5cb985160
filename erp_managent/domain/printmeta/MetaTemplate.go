package printmeta

import (
	"encoding/json"
	_const "voderpltvv/const"

	"github.com/gin-gonic/gin"
)

// TemplateCode 模板编码类型
type TemplateCode string

// 支持的打印类型常量 - 使用统一的常量定义
const (
	TemplateCodeShiftChange  TemplateCode = _const.PRINT_TYPE_SHIFT_CHANGE  // 交班单
	TemplateCodeCheckout     TemplateCode = _const.PRINT_TYPE_CHECKOUT      // 消费结账单
	TemplateCodeOpenTable    TemplateCode = _const.PRINT_TYPE_OPEN_TABLE    // 开台单
	TemplateCodeRefund       TemplateCode = _const.PRINT_TYPE_REFUND        // 退款单
	TemplateCodeProductOut   TemplateCode = _const.PRINT_TYPE_PRODUCT_OUT   // 出品单
	TemplateCodeWineStorage  TemplateCode = _const.PRINT_TYPE_WINE_STORAGE  // 存酒单
	TemplateCodeWineWithdraw TemplateCode = _const.PRINT_TYPE_WINE_WITHDRAW // 取酒单
)

// ComponentType UI组件类型
type ComponentType string

const (
	ComponentTypeSwitch        ComponentType = "switch"        // 开关
	ComponentTypeTextInput     ComponentType = "textInput"     // 单行文本输入框
	ComponentTypeTextArea      ComponentType = "textArea"      // 多行文本域
	ComponentTypeNumberInput   ComponentType = "numberInput"   // 数字输入框
	ComponentTypeSelect        ComponentType = "select"        // 下拉选择器
	ComponentTypeRadioGroup    ComponentType = "radioGroup"    // 单选按钮组
	ComponentTypeCheckboxGroup ComponentType = "checkboxGroup" // 多选框组
	ComponentTypeDatePicker    ComponentType = "datePicker"    // 日期选择器
	ComponentTypeTimePicker    ComponentType = "timePicker"    // 时间选择器
)

// DataType 数据类型
type DataType string

const (
	DataTypeBoolean DataType = "boolean" // 布尔值
	DataTypeString  DataType = "string"  // 字符串
	DataTypeNumber  DataType = "number"  // 数字
	DataTypeArray   DataType = "array"   // 数组
	DataTypeDate    DataType = "date"    // 日期字符串
	DataTypeTime    DataType = "time"    // 时间字符串
)

// ValidationType 验证类型
type ValidationType string

const (
	ValidationTypeRequired  ValidationType = "required"  // 必填
	ValidationTypeMinLength ValidationType = "minLength" // 最小长度
	ValidationTypeMaxLength ValidationType = "maxLength" // 最大长度
	ValidationTypeMin       ValidationType = "min"       // 最小值
	ValidationTypeMax       ValidationType = "max"       // 最大值
	ValidationTypePattern   ValidationType = "pattern"   // 正则表达式
)

// OptionItem 选项项
type OptionItem struct {
	Label string      `json:"label"` // 显示文本
	Value interface{} `json:"value"` // 选项值
}

// ValidationRule 验证规则
type ValidationRule struct {
	Type    ValidationType `json:"type"`            // 验证规则类型
	Value   interface{}    `json:"value,omitempty"` // 验证规则的值（如长度、最大值等）
	Message string         `json:"message"`         // 验证失败时的提示信息
}

// ConfigOption 配置项（符合动态配置规范）
type ConfigOption struct {
	Key           string           `json:"key"`                   // 配置项的唯一标识符
	Label         string           `json:"label"`                 // 在UI上显示的标签文字
	Description   string           `json:"description,omitempty"` // 详细说明
	ComponentType ComponentType    `json:"componentType"`         // UI组件类型
	DataType      DataType         `json:"dataType"`              // 数据类型
	DefaultValue  interface{}      `json:"defaultValue"`          // 默认值
	Options       []OptionItem     `json:"options,omitempty"`     // 选择类组件的选项列表
	Validations   []ValidationRule `json:"validations,omitempty"` // 校验规则列表
}

// DataComponent 数据组件
type DataComponent struct {
	Key         string `json:"key"`         // 组件键
	Name        string `json:"name"`        // 显示名称
	Description string `json:"description"` // 描述
	DataType    string `json:"dataType"`    // 数据类型 (string, number, array, object)
}

// PrintDocument 标准打印文档结构
type PrintDocument struct {
	TemplateCode string                 `json:"templateCode"` // 模板编码
	VenueId      string                 `json:"venueId"`      // 门店ID
	BusinessId   string                 `json:"businessId"`   // 业务ID
	Data         map[string]interface{} `json:"data"`         // 打印数据
	Timestamp    int64                  `json:"timestamp"`    // 生成时间戳
}

// DataFetcherFunc 数据获取函数类型
type DataFetcherFunc func(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*PrintDocument, error)

// MetaTemplate 元模板定义
type MetaTemplate struct {
	TemplateCode         TemplateCode    `json:"templateCode"`         // 模板编码
	Name                 string          `json:"name"`                 // 显示名称
	Description          string          `json:"description"`          // 描述
	ConfigOptions        []ConfigOption  `json:"configOptions"`        // 动态配置项
	AvailableComponents  []DataComponent `json:"availableComponents"`  // 可用数据组件
	DefaultLayoutContent string          `json:"defaultLayoutContent"` // 默认布局内容JSON
	DataFetcher          DataFetcherFunc `json:"-"`                    // 数据获取函数（不序列化）
}

// GetDefaultBusinessConfig 获取默认业务配置
func (mt *MetaTemplate) GetDefaultBusinessConfig() map[string]interface{} {
	config := make(map[string]interface{})
	for _, option := range mt.ConfigOptions {
		config[option.Key] = option.DefaultValue
	}
	return config
}

// ValidateBusinessConfig 验证业务配置
func (mt *MetaTemplate) ValidateBusinessConfig(config map[string]interface{}) error {
	for _, option := range mt.ConfigOptions {
		value, exists := config[option.Key]

		// 执行各种验证规则
		for _, rule := range option.Validations {
			switch rule.Type {
			case ValidationTypeRequired:
				if !exists || value == nil {
					return &ValidationError{
						Field:   option.Key,
						Message: rule.Message,
					}
				}
			case ValidationTypeMinLength:
				if str, ok := value.(string); ok {
					if minLen, ok := rule.Value.(float64); ok && len(str) < int(minLen) {
						return &ValidationError{
							Field:   option.Key,
							Message: rule.Message,
						}
					}
				}
			case ValidationTypeMaxLength:
				if str, ok := value.(string); ok {
					if maxLen, ok := rule.Value.(float64); ok && len(str) > int(maxLen) {
						return &ValidationError{
							Field:   option.Key,
							Message: rule.Message,
						}
					}
				}
			case ValidationTypeMin:
				if num, ok := value.(float64); ok {
					if min, ok := rule.Value.(float64); ok && num < min {
						return &ValidationError{
							Field:   option.Key,
							Message: rule.Message,
						}
					}
				}
			case ValidationTypeMax:
				if num, ok := value.(float64); ok {
					if max, ok := rule.Value.(float64); ok && num > max {
						return &ValidationError{
							Field:   option.Key,
							Message: rule.Message,
						}
					}
				}
				// pattern验证可以后续实现
			}
		}
	}
	return nil
}

// ToJSON 转换为JSON（用于API响应）
func (mt *MetaTemplate) ToJSON() ([]byte, error) {
	return json.Marshal(mt)
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string `json:"field"`
	Message string `json:"message"`
}

func (e *ValidationError) Error() string {
	return e.Field + ": " + e.Message
}
