package templates

import (
	"fmt"
	"voderpltvv/erp_managent/domain/printmeta"

	"github.com/gin-gonic/gin"
)

// MetaTemplateRegistry 元模板注册表
// 硬编码定义系统支持的所有打印类型及其能力
var MetaTemplateRegistry = map[printmeta.TemplateCode]*printmeta.MetaTemplate{
	// 交班单模板
	printmeta.TemplateCodeShiftChange: {
		TemplateCode: printmeta.TemplateCodeShiftChange,
		Name:         "交班单",
		Description:  "门店交班时的营业数据汇总单据",
		ConfigOptions: []printmeta.ConfigOption{
			{
				Key:           "split_by_type",
				Label:         "按类型拆分",
				Description:   "是否将交班单按商品类型拆分打印",
				ComponentType: printmeta.ComponentTypeSwitch,
				DataType:      printmeta.DataTypeBoolean,
				DefaultValue:  false,
			},
			{
				Key:           "include_payment_details",
				Label:         "包含支付明细",
				Description:   "是否在交班单中显示详细的支付方式统计",
				ComponentType: printmeta.ComponentTypeSwitch,
				DataType:      printmeta.DataTypeBoolean,
				DefaultValue:  true,
			},
			{
				Key:           "show_operator_summary",
				Label:         "显示操作员汇总",
				Description:   "是否显示各操作员的业绩汇总",
				ComponentType: printmeta.ComponentTypeSwitch,
				DataType:      printmeta.DataTypeBoolean,
				DefaultValue:  true,
			},
		},
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称、地址等基本信息", DataType: "object"},
			{Key: "shift_summary", Name: "交班汇总", Description: "营业额、订单数等汇总数据", DataType: "object"},
			{Key: "payment_details", Name: "支付明细", Description: "各种支付方式的明细统计", DataType: "array"},
			{Key: "operator_summary", Name: "操作员汇总", Description: "各操作员的业绩统计", DataType: "array"},
			{Key: "time_range", Name: "时间范围", Description: "交班的起止时间", DataType: "object"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "summary", "details", "footer"]}`,
		DataFetcher:          fetchShiftChangeData,
	},

	// 消费结账单模板
	printmeta.TemplateCodeCheckout: {
		TemplateCode:  printmeta.TemplateCodeCheckout,
		Name:          "消费结账单",
		Description:   "顾客消费结账时的账单凭证",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称、地址、电话等", DataType: "object"},
			{Key: "order_info", Name: "订单信息", Description: "订单号、台号、时间等", DataType: "object"},
			{Key: "product_list", Name: "商品清单", Description: "消费的商品列表及价格", DataType: "array"},
			{Key: "payment_info", Name: "支付信息", Description: "支付方式、金额、找零等", DataType: "object"},
			{Key: "member_info", Name: "会员信息", Description: "会员卡信息、积分等", DataType: "object"},
			{Key: "discount_info", Name: "优惠信息", Description: "折扣、优惠券等优惠明细", DataType: "array"},
		},
		DefaultLayoutContent: `{"layout": "58mm", "components": ["header", "order_info", "product_list", "payment_info", "footer"]}`,
		DataFetcher:          fetchCheckoutData,
	},

	// 开台单模板
	printmeta.TemplateCodeOpenTable: {
		TemplateCode:  printmeta.TemplateCodeOpenTable,
		Name:          "开台单",
		Description:   "顾客开台时的凭证单据",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称、地址等", DataType: "object"},
			{Key: "room_info", Name: "包厢信息", Description: "包厢号、类型、容量等", DataType: "object"},
			{Key: "customer_info", Name: "客户信息", Description: "客户姓名、电话等", DataType: "object"},
			{Key: "service_info", Name: "服务信息", Description: "服务员、开台时间等", DataType: "object"},
			{Key: "qr_code", Name: "二维码", Description: "包厢专属二维码", DataType: "string"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "room_info", "customer_info", "footer"]}`,
		DataFetcher:          fetchOpenTableData,
	},

	// 退款单模板
	printmeta.TemplateCodeRefund: {
		TemplateCode:  printmeta.TemplateCodeRefund,
		Name:          "退款单",
		Description:   "商品退款时的凭证单据",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称、地址等", DataType: "object"},
			{Key: "refund_info", Name: "退款信息", Description: "退款金额、原因、时间等", DataType: "object"},
			{Key: "original_order", Name: "原订单信息", Description: "原始订单的详细信息", DataType: "object"},
			{Key: "operator_info", Name: "操作员信息", Description: "处理退款的操作员信息", DataType: "object"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "refund_info", "original_order", "footer"]}`,
		DataFetcher:          fetchRefundData,
	},

	// 出品单模板
	printmeta.TemplateCodeProductOut: {
		TemplateCode:  printmeta.TemplateCodeProductOut,
		Name:          "出品单",
		Description:   "厨房出品时的制作单据",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称等", DataType: "object"},
			{Key: "order_info", Name: "订单信息", Description: "订单号、台号、时间等", DataType: "object"},
			{Key: "product_list", Name: "商品清单", Description: "需要制作的商品列表", DataType: "array"},
			{Key: "special_requirements", Name: "特殊要求", Description: "客户的特殊制作要求", DataType: "array"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "order_info", "product_list", "footer"]}`,
		DataFetcher:          fetchProductOutData,
	},

	// 存酒单模板
	printmeta.TemplateCodeWineStorage: {
		TemplateCode:  printmeta.TemplateCodeWineStorage,
		Name:          "存酒单",
		Description:   "客户存酒时的凭证单据",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称等", DataType: "object"},
			{Key: "wine_info", Name: "酒水信息", Description: "酒水名称、规格、数量等", DataType: "object"},
			{Key: "storage_info", Name: "存储信息", Description: "存储位置、存储时间等", DataType: "object"},
			{Key: "customer_info", Name: "客户信息", Description: "存酒客户的信息", DataType: "object"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "wine_info", "storage_info", "footer"]}`,
		DataFetcher:          fetchWineStorageData,
	},

	// 取酒单模板
	printmeta.TemplateCodeWineWithdraw: {
		TemplateCode:  printmeta.TemplateCodeWineWithdraw,
		Name:          "取酒单",
		Description:   "客户取酒时的凭证单据",
		ConfigOptions: []printmeta.ConfigOption{}, // 使用公共配置，自定义配置为空
		AvailableComponents: []printmeta.DataComponent{
			{Key: "venue_info", Name: "门店信息", Description: "门店名称等", DataType: "object"},
			{Key: "wine_info", Name: "酒水信息", Description: "酒水名称、规格等", DataType: "object"},
			{Key: "withdraw_info", Name: "取酒信息", Description: "取酒数量、时间等", DataType: "object"},
			{Key: "customer_info", Name: "客户信息", Description: "取酒客户的信息", DataType: "object"},
		},
		DefaultLayoutContent: `{"layout": "standard", "components": ["header", "wine_info", "withdraw_info", "footer"]}`,
		DataFetcher:          fetchWineWithdrawData,
	},
}

// GetAllMetaTemplates 获取所有元模板
func GetAllMetaTemplates() []*printmeta.MetaTemplate {
	templates := make([]*printmeta.MetaTemplate, 0, len(MetaTemplateRegistry))
	for _, template := range MetaTemplateRegistry {
		templates = append(templates, template)
	}
	return templates
}

// GetMetaTemplateByCode 根据编码获取元模板
func GetMetaTemplateByCode(code printmeta.TemplateCode) (*printmeta.MetaTemplate, error) {
	template, exists := MetaTemplateRegistry[code]
	if !exists {
		return nil, fmt.Errorf("unsupported template code: %s", code)
	}
	return template, nil
}

// GetSupportedTemplateCodes 获取所有支持的模板编码
func GetSupportedTemplateCodes() []printmeta.TemplateCode {
	codes := make([]printmeta.TemplateCode, 0, len(MetaTemplateRegistry))
	for code := range MetaTemplateRegistry {
		codes = append(codes, code)
	}
	return codes
}

// ========================== 数据获取函数实现 ==========================
// 这些函数将在实际业务中实现具体的数据获取逻辑

func fetchShiftChangeData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现交班单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeShiftChange),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":       map[string]interface{}{"name": "示例门店", "address": "示例地址"},
			"shift_summary":    map[string]interface{}{"total_amount": 10000, "order_count": 50},
			"payment_details":  []interface{}{},
			"operator_summary": []interface{}{},
			"time_range":       map[string]interface{}{"start": "2024-01-01 09:00", "end": "2024-01-01 18:00"},
		},
		Timestamp: 1640995200, // 示例时间戳
	}, nil
}

func fetchCheckoutData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现消费结账单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeCheckout),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":    map[string]interface{}{"name": "示例门店"},
			"order_info":    map[string]interface{}{"order_no": businessId, "table_no": "A01"},
			"product_list":  []interface{}{},
			"payment_info":  map[string]interface{}{"total": 299.00, "paid": 300.00, "change": 1.00},
			"member_info":   map[string]interface{}{},
			"discount_info": []interface{}{},
		},
		Timestamp: 1640995200,
	}, nil
}

func fetchOpenTableData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现开台单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeOpenTable),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":    map[string]interface{}{"name": "示例门店"},
			"room_info":     map[string]interface{}{"room_no": "A01", "capacity": 8},
			"customer_info": map[string]interface{}{"name": "张先生", "phone": "138****8888"},
			"service_info":  map[string]interface{}{"waiter": "小李", "open_time": "2024-01-01 19:30"},
			"qr_code":       "https://example.com/room/A01",
		},
		Timestamp: 1640995200,
	}, nil
}

func fetchRefundData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现退款单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeRefund),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":     map[string]interface{}{"name": "示例门店"},
			"refund_info":    map[string]interface{}{"amount": 100.00, "reason": "商品质量问题"},
			"original_order": map[string]interface{}{"order_no": "ORD001", "total": 299.00},
			"operator_info":  map[string]interface{}{"name": "经理", "id": "MGR001"},
		},
		Timestamp: 1640995200,
	}, nil
}

func fetchProductOutData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现出品单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeProductOut),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":           map[string]interface{}{"name": "示例门店"},
			"order_info":           map[string]interface{}{"order_no": businessId, "table_no": "A01"},
			"product_list":         []interface{}{},
			"special_requirements": []interface{}{},
		},
		Timestamp: 1640995200,
	}, nil
}

func fetchWineStorageData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现存酒单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeWineStorage),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":    map[string]interface{}{"name": "示例门店"},
			"wine_info":     map[string]interface{}{"name": "茅台酒", "quantity": 2},
			"storage_info":  map[string]interface{}{"location": "A区01号", "date": "2024-01-01"},
			"customer_info": map[string]interface{}{"name": "张先生", "phone": "138****8888"},
		},
		Timestamp: 1640995200,
	}, nil
}

func fetchWineWithdrawData(ctx *gin.Context, venueId string, businessId string, businessConfig map[string]interface{}) (*printmeta.PrintDocument, error) {
	// TODO: 实现取酒单数据获取逻辑
	return &printmeta.PrintDocument{
		TemplateCode: string(printmeta.TemplateCodeWineWithdraw),
		VenueId:      venueId,
		BusinessId:   businessId,
		Data: map[string]interface{}{
			"venue_info":    map[string]interface{}{"name": "示例门店"},
			"wine_info":     map[string]interface{}{"name": "茅台酒", "withdraw_quantity": 1},
			"withdraw_info": map[string]interface{}{"date": "2024-01-01", "remaining": 1},
			"customer_info": map[string]interface{}{"name": "张先生", "phone": "138****8888"},
		},
		Timestamp: 1640995200,
	}, nil
}
