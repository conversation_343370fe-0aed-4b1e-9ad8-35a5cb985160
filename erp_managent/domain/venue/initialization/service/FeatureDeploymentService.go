package service

import "github.com/gin-gonic/gin"

// FeatureDeploymentService 功能部署服务接口
// 负责处理新功能上线时的自动检测和批量初始化
type FeatureDeploymentService interface {
	// DetectNewFeatures 检测新功能
	// 对比已注册的初始化器与数据库中的功能版本记录，找出新功能
	DetectNewFeatures(ctx *gin.Context) ([]string, error)

	// TriggerBatchInitialization 触发批量初始化
	// 为指定功能触发批量初始化任务
	TriggerBatchInitialization(ctx *gin.Context, featureName string) (string, error)

	// AutoDetectAndInitialize 自动检测并初始化
	// 检测新功能并自动触发批量初始化（应用启动时调用）
	AutoDetectAndInitialize(ctx *gin.Context) error

	// GetRegisteredFeatures 获取已注册的功能列表
	// 返回所有已注册的初始化器功能名称和版本
	GetRegisteredFeatures(ctx *gin.Context) (map[string]string, error)

	// CheckFeatureExists 检查功能是否存在于数据库
	// 检查指定功能和版本是否已在system_feature_versions表中存在
	CheckFeatureExists(ctx *gin.Context, featureName, version string) (bool, error)
}
