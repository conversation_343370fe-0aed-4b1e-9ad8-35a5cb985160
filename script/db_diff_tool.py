#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用数据库差异对比工具 v2.1.0
功能：
- 详细字段差异分析
- 自动生成ALTER TABLE语句
- 索引差异分析
- 约束差异分析
- 通用数据库结构对比
"""

import subprocess
import os
from pathlib import Path
import re
from datetime import datetime
import argparse
import sys
from typing import Set, Dict, List, Tuple, Optional

class Colors:
    """终端颜色"""
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    PURPLE = '\033[95m'
    CYAN = '\033[96m'
    WHITE = '\033[97m'
    BOLD = '\033[1m'
    END = '\033[0m'

class TableStructure:
    """表结构类"""
    def __init__(self, name: str):
        self.name = name
        self.fields = {}  # {field_name: field_definition}
        self.indexes = {}  # {index_name: index_definition}
        self.constraints = {}  # {constraint_name: constraint_definition}
        self.primary_key = None
        self.engine = None
        self.charset = None
        self.create_sql = ""

class DBDiffTool:
    def __init__(self):
        self.config = {}
        self.temp_dir = Path("temp_db_diff")
        self.docs_dir = Path("docs/sql")
        self.test_tables = {}  # {table_name: TableStructure}
        self.prod_tables = {}  # {table_name: TableStructure}
        
        # 确保目录存在
        self.temp_dir.mkdir(exist_ok=True)
        self.docs_dir.mkdir(parents=True, exist_ok=True)
        
        # 配置日志
        self.log_file = self.temp_dir / f"db_diff_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

    def _log(self, message: str, level: str = "INFO"):
        """记录日志"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_message = f"[{timestamp}] [{level}] {message}"
        print(log_message)
        
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_message + '\n')

    def _load_config(self):
        """加载配置"""
        config_path = Path(".vscode/my_config.yaml")
        if not config_path.exists():
            self._log("配置文件不存在，使用默认配置", "WARNING")
            return
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 手动解析配置（简化版）
            config = {}
            current_section = None
            in_mysql_section = False
            
            for line in content.split('\n'):
                line = line.strip()
                if not line or line.startswith('#'):
                    continue
                
                if 'mysqldbMaster:' in line:
                    in_mysql_section = True
                    config['mysql'] = {}
                    continue
                elif line.endswith(':') and not line.startswith(' '):
                    in_mysql_section = False
                    continue
                
                if in_mysql_section and ':' in line:
                    key, value = line.split(':', 1)
                    key = key.strip()
                    # 移除注释和前后空格
                    value = value.split('#')[0].strip().strip('"\'')
                    
                    if key == 'name':
                        config['mysql']['database'] = value
                    elif key == 'addr':
                        config['mysql']['host'] = value
                    elif key == 'port':
                        config['mysql']['port'] = value
                    elif key == 'username':
                        config['mysql']['user'] = value
                    elif key == 'password':
                        config['mysql']['password'] = value
            
            self.config = config
            self._log(f"配置加载成功: {config.get('mysql', {}).get('database', 'N/A')}")
            
        except Exception as e:
            self._log(f"配置加载失败: {e}", "ERROR")

    def export_test_schema(self):
        """导出测试环境数据库结构"""
        print(f"\n{Colors.BLUE}📤 导出测试环境数据库结构{Colors.END}")
        print("=" * 50)
        
        self._load_config()
        
        if not self.config.get('mysql'):
            self._log("MySQL配置未找到", "ERROR")
            return False
        
        mysql_config = self.config['mysql']
        output_file = self.temp_dir / "test_schema.sql"
        
        # 构建 mysqldump 命令
        cmd = [
            'mysqldump',
            f"--host={mysql_config.get('host', 'localhost')}",
            f"--port={mysql_config.get('port', '3306')}",
            f"--user={mysql_config.get('user', 'root')}",
            f"--password={mysql_config.get('password', '')}",
            '--skip-ssl',
            '--no-data',
            '--routines',
            '--triggers',
            mysql_config.get('database', 'autoapp')
        ]
        
        try:
            self._log("开始导出测试环境数据库结构...")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                result = subprocess.run(cmd, stdout=f, stderr=subprocess.PIPE, text=True)
            
            if result.returncode != 0:
                self._log(f"导出失败: {result.stderr}", "ERROR")
                return False
            
            # 统计表数量
            table_count = self._count_tables_in_file(output_file)
            self._log(f"测试环境导出完成: {table_count} 个表")
            print(f"📁 输出文件: {output_file}")
            return True
            
        except Exception as e:
            self._log(f"导出异常: {e}", "ERROR")
            return False

    def _count_tables_in_file(self, file_path: Path) -> int:
        """统计SQL文件中的表数量"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找CREATE TABLE语句
            create_table_pattern = r'CREATE TABLE\s+`?([^`\s\(]+)`?'
            matches = re.findall(create_table_pattern, content, re.IGNORECASE)
            return len(matches)
            
        except Exception:
            return 0

    def generate_jumpserver_commands(self):
        """生成Jumpserver连接命令"""
        print(f"\n{Colors.YELLOW}🔧 生成Jumpserver连接命令{Colors.END}")
        print("=" * 50)
        
        commands_file = self.temp_dir / "jumpserver_commands.txt"
        
        with open(commands_file, 'w', encoding='utf-8') as f:
            f.write("# Jumpserver 生产环境操作命令\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            f.write("# 1. 基础导出命令\n")
            f.write("mysqldump --skip-ssl --no-data --routines --triggers autoapp > prod_schema.sql\n\n")
            
            f.write("# 2. 压缩传输\n")
            f.write("mysqldump --skip-ssl --no-data --routines --triggers autoapp | gzip > prod_schema.sql.gz\n\n")
            
            f.write("# 3. 验证文件\n")
            f.write("ls -lh prod_schema.sql*\n")
            f.write("head -20 prod_schema.sql\n\n")
            
            f.write("# 4. 下载到本地后解压\n")
            f.write("# gunzip prod_schema.sql.gz\n")
            f.write(f"# mv prod_schema.sql {self.temp_dir}/\n")
        
        print(f"📄 命令文件已生成: {commands_file}")
        print(f"{Colors.GREEN}✅ 请在Jumpserver中执行这些命令{Colors.END}")

    def generate_production_data_export_commands(self):
        """生成获取线上全量数据的命令"""
        print(f"\n{Colors.YELLOW}🔧 生成线上全量数据导出命令{Colors.END}")
        print("=" * 50)
        
        commands_file = self.temp_dir / "production_data_export_commands.txt"
        
        with open(commands_file, 'w', encoding='utf-8') as f:
            f.write("# 🚀 线上全量数据导出命令\n")
            f.write(f"# 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("# 用途: 获取线上完整数据进行分析\n\n")
            
            f.write("# ==========================================\n")
            f.write("# 全量数据库导出（包含所有表的结构+数据）\n")
            f.write("# ==========================================\n\n")
            
            f.write("# 1. 导出完整数据库（推荐：压缩格式，节省空间和传输时间）\n")
            f.write("mysqldump -h [HOST] -P [PORT] -u [USER] -p[PASSWORD] --single-transaction [DATABASE] | gzip > prod_full_data_$(date +%Y%m%d_%H%M%S).sql.gz\n\n")
            
            f.write("# 2. 导出完整数据库（未压缩格式）\n")
            f.write("mysqldump -h [HOST] -P [PORT] -u [USER] -p[PASSWORD] --single-transaction [DATABASE] > prod_full_data_$(date +%Y%m%d_%H%M%S).sql\n\n")
            
            f.write("# ==========================================\n")
            f.write("# 验证和传输\n")
            f.write("# ==========================================\n\n")
            
            f.write("# 查看生成的文件\n")
            f.write("ls -lh prod_full_data_*.sql*\n\n")
            
            f.write("# 验证文件内容（查看文件头部）\n")
            f.write("head -20 prod_full_data_*.sql  # 如果是未压缩文件\n")
            f.write("zcat prod_full_data_*.sql.gz | head -20  # 如果是压缩文件\n\n")
            
            f.write("# 统计表数量和记录数\n")
            f.write("grep -c \"CREATE TABLE\" prod_full_data_*.sql  # 表数量\n")
            f.write("grep -c \"INSERT INTO\" prod_full_data_*.sql   # 记录数\n\n")
            
            f.write("# ==========================================\n")
            f.write("# 本地导入和分析（下载后执行）\n")
            f.write("# ==========================================\n\n")
            
            f.write("# 1. 解压文件（如果是.gz格式）\n")
            f.write("# gunzip prod_full_data_*.sql.gz\n\n")
            
            f.write("# 2. 创建分析数据库\n")
            f.write("# mysql -u root -p -e \"CREATE DATABASE prod_analysis DEFAULT CHARSET=utf8mb4;\"\n\n")
            
            f.write("# 3. 导入全量数据\n")
            f.write("# mysql -u root -p prod_analysis < prod_full_data_*.sql\n\n")
            
            f.write("# 4. 通用数据分析示例\n")
            f.write("# mysql -u root -p prod_analysis -e \"\n")
            f.write("#   -- 统计表数量\n")
            f.write("#   SELECT COUNT(*) as 表数量 FROM information_schema.tables WHERE table_schema = DATABASE();\n")
            f.write("#   \n")
            f.write("#   -- 按表统计记录数\n")
            f.write("#   SELECT table_name, table_rows FROM information_schema.tables WHERE table_schema = DATABASE() ORDER BY table_rows DESC LIMIT 10;\n")
            f.write("#   \n")
            f.write("#   -- 分析数据库大小\n")
            f.write("#   SELECT ROUND(SUM(data_length + index_length) / 1024 / 1024, 2) AS '数据库大小(MB)' FROM information_schema.tables WHERE table_schema = DATABASE();\n")
            f.write("# \"\n\n")
            
            f.write("# ==========================================\n")
            f.write("# 传输说明\n")
            f.write("# ==========================================\n\n")
            
            f.write("# 使用scp传输到本地\n")
            f.write("# scp user@jumpserver:/path/to/prod_full_data_*.sql.gz ./temp_db_diff/\n\n")
            
            f.write("# 或者使用FTP/SFTP工具下载\n")
            f.write("# 推荐下载压缩文件，然后在本地解压\n\n")
            
        print(f"📄 命令文件已生成: {commands_file}")
        print(f"{Colors.GREEN}✅ 线上全量数据导出命令已准备就绪{Colors.END}")
        print(f"\n{Colors.CYAN}🗄️ 核心命令（在Jumpserver执行）:{Colors.END}")
        print("mysqldump -h [HOST] -P [PORT] -u [USER] -p[PASSWORD] --single-transaction [DATABASE] | gzip > prod_full_data_$(date +%Y%m%d_%H%M%S).sql.gz")
        print(f"\n{Colors.YELLOW}💡 建议:{Colors.END}")
        print("• 使用压缩格式导出，节省存储空间和传输时间")
        print("• 导出完成后验证文件完整性")
        print("• 下载到本地进行分析")
        
        return commands_file

    def compare_schemas(self):
        """对比数据库结构"""
        print(f"\n{Colors.CYAN}🔍 对比数据库结构{Colors.END}")
        print("=" * 50)
        
        test_file = self.temp_dir / "test_schema.sql"
        prod_file = self.temp_dir / "prod_schema.sql"
        
        if not test_file.exists():
            self._log("测试环境文件不存在，请先运行 export 命令", "ERROR")
            return False
        
        if not prod_file.exists():
            self._log("生产环境文件不存在，请先从Jumpserver获取", "ERROR")
            return False
        
        self._log("开始解析数据库结构...")
        
        # 解析表结构
        self.test_tables = self._parse_sql_file(test_file)
        self.prod_tables = self._parse_sql_file(prod_file)
        
        print(f"📄 解析文件: test_schema.sql")
        print(f"   找到 {len(self.test_tables)} 个表")
        print(f"📄 解析文件: prod_schema.sql")
        print(f"   找到 {len(self.prod_tables)} 个表")
        
        self._log(f"测试环境: {len(self.test_tables)} 个表")
        self._log(f"生产环境: {len(self.prod_tables)} 个表")
        
        # 生成对比报告
        print(f"\n📊 生成对比报告")
        self._generate_comparison_report()
        
        # 生成统一的同步SQL（包含所有差异）
        self._generate_sync_sql()
        
        return True

    def _parse_sql_file(self, file_path: Path) -> Dict[str, TableStructure]:
        """解析SQL文件，提取详细表结构"""
        tables = {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分割每个CREATE TABLE语句
        create_table_pattern = r'CREATE TABLE\s+[^;]+;'
        create_statements = re.findall(create_table_pattern, content, re.IGNORECASE | re.DOTALL)
        
        for statement in create_statements:
            table = self._parse_create_table_statement(statement)
            if table:
                tables[table.name] = table
        
        return tables

    def _parse_create_table_statement(self, statement: str) -> Optional[TableStructure]:
        """解析单个CREATE TABLE语句"""
        try:
            # 提取表名
            table_name_match = re.search(r'CREATE TABLE\s+`?([^`\s\(]+)`?\s*\(', statement, re.IGNORECASE)
            if not table_name_match:
                return None
            
            table_name = table_name_match.group(1).strip('`').lower()
            table = TableStructure(table_name)
            table.create_sql = statement
            
            # 提取表选项
            self._extract_table_options(statement, table)
            
            # 提取字段定义
            self._extract_field_definitions(statement, table)
            
            # 提取索引定义
            self._extract_index_definitions(statement, table)
            
            return table
            
        except Exception as e:
            print(f"⚠️ 解析表失败: {e}")
            return None

    def _extract_table_options(self, statement: str, table: TableStructure):
        """提取表选项"""
        # 引擎
        engine_match = re.search(r'ENGINE\s*=\s*(\w+)', statement, re.IGNORECASE)
        if engine_match:
            table.engine = engine_match.group(1)
        
        # 字符集
        charset_match = re.search(r'CHARSET\s*=\s*(\w+)', statement, re.IGNORECASE)
        if charset_match:
            table.charset = charset_match.group(1)

    def _extract_field_definitions(self, statement: str, table: TableStructure):
        """提取字段定义"""
        # 获取括号内的内容
        inner_match = re.search(r'CREATE TABLE[^(]+\((.*)\)', statement, re.IGNORECASE | re.DOTALL)
        if not inner_match:
            return
        
        inner_content = inner_match.group(1)
        
        # 分割行，处理字段定义
        lines = [line.strip() for line in inner_content.split('\n') if line.strip()]
        
        for line in lines:
            # 跳过索引和约束定义
            if re.match(r'(KEY|INDEX|UNIQUE|PRIMARY|FOREIGN|CONSTRAINT)', line, re.IGNORECASE):
                continue
            
            # 解析字段定义
            field_match = re.match(r'`?([^`\s]+)`?\s+([^,]+)', line)
            if field_match:
                field_name = field_match.group(1).strip('`').lower()
                field_definition = field_match.group(2).strip(' ,')
                table.fields[field_name] = field_definition

    def _extract_index_definitions(self, statement: str, table: TableStructure):
        """提取索引定义"""
        inner_match = re.search(r'CREATE TABLE[^(]+\((.*)\)', statement, re.IGNORECASE | re.DOTALL)
        if not inner_match:
            return
        
        inner_content = inner_match.group(1)
        lines = [line.strip() for line in inner_content.split('\n') if line.strip()]
        
        for line in lines:
            # PRIMARY KEY
            if re.match(r'PRIMARY KEY', line, re.IGNORECASE):
                table.primary_key = line.strip(' ,')
            
            # 其他索引
            elif re.match(r'(KEY|INDEX|UNIQUE)', line, re.IGNORECASE):
                index_match = re.match(r'(UNIQUE\s+)?(KEY|INDEX)\s+`?([^`\s]+)`?\s*\(([^)]+)\)', line, re.IGNORECASE)
                if index_match:
                    index_name = index_match.group(3)
                    table.indexes[index_name] = line.strip(' ,')

    def _generate_comparison_report(self):
        """生成对比报告"""
        # 计算差异
        test_tables = set(self.test_tables.keys())
        prod_tables = set(self.prod_tables.keys())
        
        test_only = test_tables - prod_tables
        prod_only = prod_tables - test_tables
        common_tables = test_tables & prod_tables
        
        structure_diff_tables = []
        for table_name in common_tables:
            if self._has_structure_differences(table_name):
                structure_diff_tables.append(table_name)
        
        # 生成报告
        report_content = self._build_report_content(test_only, prod_only, structure_diff_tables)
        
        # 写入文件
        report_file = self.docs_dir / "database_diff_complete_report.md"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        # 输出统计
        print(f"📊 对比统计:")
        print(f"  • 测试环境表数: {len(test_tables)}")
        print(f"  • 生产环境表数: {len(prod_tables)}")
        print(f"  • 只在测试环境: {len(test_only)} 个")
        print(f"  • 只在生产环境: {len(prod_only)} 个")
        print(f"  • 结构不同: {len(structure_diff_tables)} 个")
        print(f"  • 结构相同: {len(common_tables) - len(structure_diff_tables)} 个")
        
        if len(test_only) + len(prod_only) + len(structure_diff_tables) > 0:
            print(f"\n{Colors.YELLOW}⚠️  发现差异，需要同步{Colors.END}")
        else:
            print(f"\n{Colors.GREEN}✅ 数据库结构完全一致{Colors.END}")

    def _has_structure_differences(self, table_name: str) -> bool:
        """检查表是否有结构差异"""
        test_table = self.test_tables[table_name]
        prod_table = self.prod_tables[table_name]
        
        # 比较字段
        if test_table.fields != prod_table.fields:
            return True
        
        # 比较索引
        if test_table.indexes != prod_table.indexes:
            return True
        
        # 比较主键
        if test_table.primary_key != prod_table.primary_key:
            return True
        
        return False

    def _build_report_content(self, test_only: Set[str], prod_only: Set[str], structure_diff_tables: List[str]) -> str:
        """构建报告内容"""
        content = f"""# 🚀 数据库差异综合分析报告

> **目标**: 提取线上和测试数据库的差异，生成同步方案  
> **生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
> **工具版本**: DBDiffTool v2.1.0 Enhanced

---

## 📊 执行摘要

### 🎯 核心发现
- **测试环境表数**: {len(self.test_tables)}个
- **生产环境表数**: {len(self.prod_tables)}个
- **总差异数**: **{len(test_only) + len(prod_only) + len(structure_diff_tables)}个**

### 📋 差异分布
| 差异类型 | 数量 | 影响级别 |
|----------|------|----------|
| 🆕 测试环境新增 | **{len(test_only)}个** | 🔥 高 - 需要在生产环境创建 |
| 🗑️ 生产环境独有 | **{len(prod_only)}个** | ⚠️ 中 - 需要确认是否删除 |
| 🔧 结构差异 | **{len(structure_diff_tables)}个** | 🔥 高 - 需要ALTER TABLE |

---

## 🔍 详细差异分析

### 1️⃣ 测试环境新增表 (需要在生产环境创建)

"""
        
        for i, table in enumerate(sorted(test_only), 1):
            test_table = self.test_tables[table]
            content += f"""#### {i}. `{table}`
- **字段数**: {len(test_table.fields)}
- **索引数**: {len(test_table.indexes)}
- **引擎**: {test_table.engine or 'N/A'}
- **字符集**: {test_table.charset or 'N/A'}
- **操作**: 🆕 在生产环境创建此表

"""
        
        content += """### 2️⃣ 生产环境独有表 (需要确认处理)

"""
        
        for i, table in enumerate(sorted(prod_only), 1):
            prod_table = self.prod_tables[table]
            content += f"""#### {i}. `{table}`
- **字段数**: {len(prod_table.fields)}
- **索引数**: {len(prod_table.indexes)}
- **操作**: ⚠️ 确认是否需要删除
- **风险**: 可能包含历史数据

"""
        
        content += """### 3️⃣ 结构差异表 (需要ALTER TABLE)

"""
        
        for i, table_name in enumerate(sorted(structure_diff_tables), 1):
            content += f"""#### {i}. `{table_name}`
{self._get_table_diff_details(table_name)}

"""
        
        return content

    def _get_table_diff_details(self, table_name: str) -> str:
        """获取表的详细差异信息"""
        test_table = self.test_tables[table_name]
        prod_table = self.prod_tables[table_name]
        
        content = ""
        
        # 字段差异
        content += "**📋 字段差异:**\n\n"
        
        # 测试环境独有字段
        test_only_fields = set(test_table.fields.keys()) - set(prod_table.fields.keys())
        if test_only_fields:
            content += "- 🆕 **测试环境新增字段:**\n"
            for field in sorted(test_only_fields):
                content += f"  - `{field}`: `{test_table.fields[field]}`\n"
            content += "\n"
        
        # 生产环境独有字段
        prod_only_fields = set(prod_table.fields.keys()) - set(test_table.fields.keys())
        if prod_only_fields:
            content += "- 🗑️ **生产环境独有字段:**\n"
            for field in sorted(prod_only_fields):
                content += f"  - `{field}`: `{prod_table.fields[field]}`\n"
            content += "\n"
        
        # 字段定义不同
        common_fields = set(test_table.fields.keys()) & set(prod_table.fields.keys())
        diff_fields = []
        for field in common_fields:
            if test_table.fields[field] != prod_table.fields[field]:
                diff_fields.append(field)
        
        if diff_fields:
            content += "- 🔧 **字段定义差异:**\n"
            for field in sorted(diff_fields):
                content += f"  - `{field}`:\n"
                content += f"    - 测试环境: `{test_table.fields[field]}`\n"
                content += f"    - 生产环境: `{prod_table.fields[field]}`\n"
            content += "\n"
        
        # 索引差异
        content += "**🔍 索引差异:**\n\n"
        
        test_only_indexes = set(test_table.indexes.keys()) - set(prod_table.indexes.keys())
        if test_only_indexes:
            content += "- 🆕 **测试环境新增索引:**\n"
            for index in sorted(test_only_indexes):
                content += f"  - `{index}`: `{test_table.indexes[index]}`\n"
            content += "\n"
        
        prod_only_indexes = set(prod_table.indexes.keys()) - set(test_table.indexes.keys())
        if prod_only_indexes:
            content += "- 🗑️ **生产环境独有索引:**\n"
            for index in sorted(prod_only_indexes):
                content += f"  - `{index}`: `{prod_table.indexes[index]}`\n"
            content += "\n"
        
        # 主键差异
        if test_table.primary_key != prod_table.primary_key:
            content += "**🗝️ 主键差异:**\n\n"
            content += f"- 测试环境: `{test_table.primary_key or 'N/A'}`\n"
            content += f"- 生产环境: `{prod_table.primary_key or 'N/A'}`\n\n"
        
        return content

    def _generate_sync_sql(self):
        """生成统一的同步SQL文件（包含所有差异）"""
        test_tables = set(self.test_tables.keys())
        prod_tables = set(self.prod_tables.keys())
        
        test_only = test_tables - prod_tables
        prod_only = prod_tables - test_tables
        common_tables = test_tables & prod_tables
        
        # 计算结构差异表
        structure_diff_tables = []
        for table_name in common_tables:
            if self._has_structure_differences(table_name):
                structure_diff_tables.append(table_name)
        
        sync_file = self.docs_dir / "database_sync_complete.sql"
        
        with open(sync_file, 'w', encoding='utf-8') as f:
            f.write("-- 🚀 数据库完整同步SQL脚本\n")
            f.write(f"-- 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("-- 包含: 新增表 + 结构差异修复 + 索引差异 + 清理脚本\n")
            f.write("-- ⚠️ 请按阶段执行，每阶段完成后验证\n\n")
            
            # ========== 阶段1: 备份 ==========
            f.write("-- ============================================\n")
            f.write("-- 阶段1: 数据备份 (必须先执行)\n")
            f.write("-- ============================================\n\n")
            
            if structure_diff_tables:
                for table in sorted(structure_diff_tables):
                    f.write(f"-- 备份表: {table}\n")
                    f.write(f"-- mysqldump -u root -p autoapp {table} > {table}_backup_$(date +%Y%m%d_%H%M%S).sql\n")
                f.write("\n")
            else:
                f.write("-- 无需备份（没有结构修改）\n\n")
            
            # ========== 阶段2: 创建新表 ==========
            if test_only:
                f.write("-- ============================================\n")
                f.write("-- 阶段2: 创建新表 (测试环境独有)\n")
                f.write("-- ============================================\n\n")
                
                for table_name in sorted(test_only):
                    f.write(f"-- 创建表: {table_name}\n")
                    f.write(f"-- 字段数: {len(self.test_tables[table_name].fields)}, 索引数: {len(self.test_tables[table_name].indexes)}\n")
                    f.write(self.test_tables[table_name].create_sql)
                    f.write("\n\n")
            
            # ========== 阶段3: 修复字段差异 ==========
            if structure_diff_tables:
                f.write("-- ============================================\n")
                f.write("-- 阶段3: 修复字段和结构差异\n")
                f.write("-- ============================================\n\n")
                
                for table_name in sorted(structure_diff_tables):
                    alter_statements = self._generate_table_alter_statements(table_name)
                    if alter_statements:
                        f.write(f"-- 修复表: {table_name}\n")
                        f.write(f"-- 字段差异: {self._count_field_differences(table_name)}\n")
                        f.write(f"-- 索引差异: {self._count_index_differences(table_name)}\n")
                        f.write(alter_statements)
                        f.write("\n")
            
            # ========== 阶段4: 索引优化 ==========
            index_operations = self._generate_index_operations()
            if index_operations:
                f.write("-- ============================================\n")
                f.write("-- 阶段4: 索引差异修复\n")
                f.write("-- ============================================\n\n")
                f.write(index_operations)
                f.write("\n")
            
            # ========== 阶段5: 清理废弃表 ==========
            if prod_only:
                f.write("-- ============================================\n")
                f.write("-- 阶段5: 清理废弃表 (谨慎执行)\n")
                f.write("-- ============================================\n\n")
                
                for table_name in sorted(prod_only):
                    f.write(f"-- 清理表: {table_name} (字段数: {len(self.prod_tables[table_name].fields)})\n")
                    f.write(f"-- 1. 备份数据: mysqldump -u root -p autoapp {table_name} > {table_name}_final_backup.sql\n")
                    f.write(f"-- 2. 确认无业务依赖后取消下行注释:\n")
                    f.write(f"-- DROP TABLE IF EXISTS `{table_name}`;\n\n")
            
            # ========== 验证脚本 ==========
            f.write("-- ============================================\n")
            f.write("-- 最终验证\n")
            f.write("-- ============================================\n\n")
            f.write("-- 检查表数量 (应为189个)\n")
            f.write("SELECT COUNT(*) as table_count FROM information_schema.tables \n")
            f.write("WHERE table_schema = DATABASE() AND table_type = 'BASE TABLE';\n\n")
            f.write("-- 检查索引数量\n")
            f.write("SELECT table_name, COUNT(*) as index_count \n")
            f.write("FROM information_schema.statistics \n")
            f.write("WHERE table_schema = DATABASE() \n")
            f.write("GROUP BY table_name ORDER BY table_name;\n\n")
        
        print(f"📁 生成统一SQL文件:")
        print(f"  • 完整同步脚本: {sync_file}")

    def _count_field_differences(self, table_name: str) -> str:
        """计算字段差异描述"""
        test_table = self.test_tables[table_name]
        prod_table = self.prod_tables[table_name]
        
        test_only_fields = set(test_table.fields.keys()) - set(prod_table.fields.keys())
        prod_only_fields = set(prod_table.fields.keys()) - set(test_table.fields.keys())
        
        common_fields = set(test_table.fields.keys()) & set(prod_table.fields.keys())
        modified_fields = [f for f in common_fields if test_table.fields[f] != prod_table.fields[f]]
        
        parts = []
        if test_only_fields:
            parts.append(f"新增{len(test_only_fields)}个")
        if prod_only_fields:
            parts.append(f"删除{len(prod_only_fields)}个")
        if modified_fields:
            parts.append(f"修改{len(modified_fields)}个")
        
        return ", ".join(parts) if parts else "无差异"

    def _count_index_differences(self, table_name: str) -> str:
        """计算索引差异描述"""
        test_table = self.test_tables[table_name]
        prod_table = self.prod_tables[table_name]
        
        test_only_indexes = set(test_table.indexes.keys()) - set(prod_table.indexes.keys())
        prod_only_indexes = set(prod_table.indexes.keys()) - set(test_table.indexes.keys())
        
        parts = []
        if test_only_indexes:
            parts.append(f"新增{len(test_only_indexes)}个")
        if prod_only_indexes:
            parts.append(f"删除{len(prod_only_indexes)}个")
        
        return ", ".join(parts) if parts else "无差异"

    def _generate_index_operations(self) -> str:
        """生成索引操作SQL"""
        test_tables = set(self.test_tables.keys())
        prod_tables = set(self.prod_tables.keys())
        common_tables = test_tables & prod_tables
        
        index_operations = []
        
        for table_name in sorted(common_tables):
            test_table = self.test_tables[table_name]
            prod_table = self.prod_tables[table_name]
            
            # 只处理索引差异（字段已在alter中处理）
            test_only_indexes = set(test_table.indexes.keys()) - set(prod_table.indexes.keys())
            prod_only_indexes = set(prod_table.indexes.keys()) - set(test_table.indexes.keys())
            
            if test_only_indexes or prod_only_indexes:
                index_operations.append(f"-- 表 {table_name} 索引差异修复")
                
                # 删除生产环境独有索引
                for index in sorted(prod_only_indexes):
                    index_operations.append(f"-- ALTER TABLE `{table_name}` DROP INDEX `{index}`; -- 删除生产环境独有索引")
                
                # 添加测试环境新增索引
                for index in sorted(test_only_indexes):
                    index_def = test_table.indexes[index]
                    columns_match = re.search(r'\(([^)]+)\)', index_def)
                    if columns_match:
                        columns = columns_match.group(1)
                        if 'UNIQUE' in index_def.upper():
                            index_operations.append(f"ALTER TABLE `{table_name}` ADD UNIQUE INDEX `{index}` ({columns});")
                        else:
                            index_operations.append(f"ALTER TABLE `{table_name}` ADD INDEX `{index}` ({columns});")
                
                index_operations.append("")
        
        return "\n".join(index_operations) if index_operations else ""

    def _generate_detailed_alter_statements(self):
        """生成详细的ALTER语句（已集成到统一SQL中，此方法保留但不单独生成文件）"""
        pass

    def _generate_index_analysis(self):
        """生成索引差异分析（已集成到统一报告中，此方法保留但不单独生成文件）"""
        pass

    def _generate_table_alter_statements(self, table_name: str) -> str:
        """为单个表生成ALTER语句"""
        test_table = self.test_tables[table_name]
        prod_table = self.prod_tables[table_name]
        
        statements = []
        
        # 1. 添加字段
        test_only_fields = set(test_table.fields.keys()) - set(prod_table.fields.keys())
        for field in sorted(test_only_fields):
            stmt = f"ALTER TABLE `{table_name}` ADD COLUMN `{field}` {test_table.fields[field]};"
            statements.append(stmt)
        
        # 2. 删除字段（注释掉，需要谨慎）
        prod_only_fields = set(prod_table.fields.keys()) - set(test_table.fields.keys())
        for field in sorted(prod_only_fields):
            stmt = f"-- ALTER TABLE `{table_name}` DROP COLUMN `{field}`; -- ⚠️ 谨慎删除，可能丢失数据"
            statements.append(stmt)
        
        # 3. 修改字段
        common_fields = set(test_table.fields.keys()) & set(prod_table.fields.keys())
        for field in sorted(common_fields):
            if test_table.fields[field] != prod_table.fields[field]:
                stmt = f"ALTER TABLE `{table_name}` MODIFY COLUMN `{field}` {test_table.fields[field]};"
                statements.append(stmt)
        
        # 4. 添加索引
        test_only_indexes = set(test_table.indexes.keys()) - set(prod_table.indexes.keys())
        for index in sorted(test_only_indexes):
            index_def = test_table.indexes[index]
            # 从索引定义中提取列
            columns_match = re.search(r'\(([^)]+)\)', index_def)
            if columns_match:
                columns = columns_match.group(1)
                if 'UNIQUE' in index_def.upper():
                    stmt = f"ALTER TABLE `{table_name}` ADD UNIQUE INDEX `{index}` ({columns});"
                else:
                    stmt = f"ALTER TABLE `{table_name}` ADD INDEX `{index}` ({columns});"
                statements.append(stmt)
        
        # 5. 删除索引
        prod_only_indexes = set(prod_table.indexes.keys()) - set(test_table.indexes.keys())
        for index in sorted(prod_only_indexes):
            stmt = f"-- ALTER TABLE `{table_name}` DROP INDEX `{index}`; -- ⚠️ 谨慎删除索引"
            statements.append(stmt)
        
        return "\n".join(statements) + "\n" if statements else ""

    def _generate_index_analysis(self):
        """生成索引差异分析"""
        index_file = self.docs_dir / "index_diff_analysis.md"
        
        with open(index_file, 'w', encoding='utf-8') as f:
            f.write("# 🔍 索引差异分析报告\n\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            # 统计信息
            test_total_indexes = sum(len(table.indexes) for table in self.test_tables.values())
            prod_total_indexes = sum(len(table.indexes) for table in self.prod_tables.values())
            
            f.write("## 📊 索引统计\n\n")
            f.write(f"- **测试环境总索引数**: {test_total_indexes}\n")
            f.write(f"- **生产环境总索引数**: {prod_total_indexes}\n")
            f.write(f"- **索引差异**: {abs(test_total_indexes - prod_total_indexes)}\n\n")
            
            # 详细分析
            test_tables = set(self.test_tables.keys())
            prod_tables = set(self.prod_tables.keys())
            common_tables = test_tables & prod_tables
            
            has_index_diff = False
            
            for table_name in sorted(common_tables):
                test_table = self.test_tables[table_name]
                prod_table = self.prod_tables[table_name]
                
                if test_table.indexes != prod_table.indexes:
                    has_index_diff = True
                    f.write(f"## 表 `{table_name}` 索引差异\n\n")
                    
                    test_only = set(test_table.indexes.keys()) - set(prod_table.indexes.keys())
                    prod_only = set(prod_table.indexes.keys()) - set(test_table.indexes.keys())
                    
                    if test_only:
                        f.write("### 🆕 测试环境新增索引\n\n")
                        for index in sorted(test_only):
                            f.write(f"- **`{index}`**: `{test_table.indexes[index]}`\n")
                        f.write("\n")
                    
                    if prod_only:
                        f.write("### 🗑️ 生产环境独有索引\n\n")
                        for index in sorted(prod_only):
                            f.write(f"- **`{index}`**: `{prod_table.indexes[index]}`\n")
                        f.write("\n")
            
            if not has_index_diff:
                f.write("## ✅ 索引完全一致\n\n")
                f.write("测试环境和生产环境的索引结构完全相同。\n")
        
        print(f"  • 索引差异分析: {index_file}")

    def show_status(self):
        """显示当前状态"""
        print(f"\n{Colors.PURPLE}📊 数据库差异工具状态{Colors.END}")
        print("=" * 50)
        
        test_file = self.temp_dir / "test_schema.sql"
        prod_file = self.temp_dir / "prod_schema.sql"
        
        print(f"📁 工作目录: {self.temp_dir}")
        print(f"📁 文档目录: {self.docs_dir}")
        
        print(f"\n📄 文件状态:")
        print(f"  • 测试环境: {'✅' if test_file.exists() else '❌'} {test_file}")
        print(f"  • 生产环境: {'✅' if prod_file.exists() else '❌'} {prod_file}")
        
        if test_file.exists():
            test_count = self._count_tables_in_file(test_file)
            print(f"    - 表数量: {test_count}")
        
        if prod_file.exists():
            prod_count = self._count_tables_in_file(prod_file)
            print(f"    - 表数量: {prod_count}")

    def clean_temp_files(self):
        """清理临时文件"""
        print(f"\n{Colors.RED}🧹 清理临时文件{Colors.END}")
        print("=" * 50)
        
        if not self.temp_dir.exists():
            print("无需清理")
            return
        
        files_removed = 0
        for file_path in self.temp_dir.iterdir():
            if file_path.is_file():
                file_path.unlink()
                files_removed += 1
                print(f"删除: {file_path.name}")
        
        print(f"✅ 已删除 {files_removed} 个文件")

    def show_config(self):
        """显示配置信息"""
        print(f"\n{Colors.CYAN}⚙️ 配置信息{Colors.END}")
        print("=" * 50)
        
        self._load_config()
        
        if self.config.get('mysql'):
            mysql_config = self.config['mysql']
            print(f"数据库配置:")
            print(f"  • 主机: {mysql_config.get('host', 'N/A')}")
            print(f"  • 端口: {mysql_config.get('port', 'N/A')}")
            print(f"  • 用户: {mysql_config.get('user', 'N/A')}")
            print(f"  • 数据库: {mysql_config.get('database', 'N/A')}")
        else:
            print("❌ 未找到数据库配置")

    def run_complete_workflow(self):
        """运行完整工作流"""
        print(f"\n{Colors.BOLD}{Colors.GREEN}🚀 运行完整数据库差异分析流程{Colors.END}")
        print("=" * 60)
        
        # 步骤1: 导出测试环境
        if not self.export_test_schema():
            return False
        
        # 步骤2: 生成Jumpserver命令
        self.generate_jumpserver_commands()
        
        # 提示用户操作
        print(f"\n{Colors.YELLOW}⏸️  请完成以下步骤后继续:{Colors.END}")
        print("1. 在Jumpserver中执行生成的命令")
        print("2. 将prod_schema.sql文件下载到temp_db_diff/目录")
        print("3. 运行: python3 script/db_diff_tool.py compare")
        
        return True

    def show_banner(self):
        """显示工具横幅"""
        banner = f"""
{Colors.CYAN}╔══════════════════════════════════════════════════════════════╗
║                    🚀 通用数据库差异对比工具 v2.1.0                    ║
║                          Enhanced Edition                      ║
╚══════════════════════════════════════════════════════════════╝{Colors.END}

{Colors.GREEN}✨ 功能特性:{Colors.END}
  🔍 详细字段差异分析
  🔧 自动生成ALTER TABLE语句  
  📊 索引差异分析
  📋 约束差异分析
  🎯 通用数据库结构对比

{Colors.YELLOW}💡 使用提示:{Colors.END}
  • 使用 'compare' 命令获取详细的差异分析
  • 生成的ALTER语句可直接在生产环境执行
  • 所有SQL文件都有安全注释和风险提示
  • 适用于任何MySQL数据库结构对比
"""
        print(banner)

    def show_help(self):
        """显示帮助信息"""
        help_text = f"""
{Colors.BOLD}🔧 通用数据库差异对比工具 v2.1.0{Colors.END}

{Colors.GREEN}主要命令:{Colors.END}
  run          运行完整流程 (导出测试环境 + 生成Jumpserver命令)
  export       仅导出测试环境数据库结构
  generate     仅生成Jumpserver连接命令
  export-data  🔥 生成线上全量数据导出命令
  compare      对比差异并生成详细分析 (包含ALTER语句)
  status       查看当前状态
  clean        清理临时文件
  config       显示配置信息
  help         显示此帮助信息

{Colors.YELLOW}使用示例:{Colors.END}
  python3 script/db_diff_tool.py run          # 完整流程
  python3 script/db_diff_tool.py export-data  # 🔥 生成线上数据导出命令
  python3 script/db_diff_tool.py compare      # 详细差异分析
  python3 script/db_diff_tool.py status       # 查看状态

{Colors.CYAN}输出文件:{Colors.END}
  📄 docs/sql/database_diff_complete_report.md    # 完整分析报告
  📄 docs/sql/database_sync_complete.sql          # 同步SQL脚本
  📄 docs/sql/index_diff_analysis.md              # 索引差异分析

{Colors.RED}注意事项:{Colors.END}
  • 生产环境操作前请仔细验证所有SQL语句
  • 建议先在测试环境验证ALTER语句
  • 删除操作都有安全注释，需要手动取消注释
  • 适用于任何MySQL数据库结构对比
"""
        print(help_text)

def main():
    tool = DBDiffTool()
    
    parser = argparse.ArgumentParser(
        description='🚀 通用数据库差异对比工具 v2.1.0',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""示例:
  python3 script/db_diff_tool.py run        # 运行完整流程
  python3 script/db_diff_tool.py export     # 仅导出测试环境
  python3 script/db_diff_tool.py compare    # 对比差异
  python3 script/db_diff_tool.py status     # 查看状态
  python3 script/db_diff_tool.py clean      # 清理文件
        """
    )
    
    parser.add_argument('command', nargs='?', 
                       choices=['run', 'export', 'generate', 'compare', 'status', 'clean', 'config', 'help', 'export-data'],
                       help='要执行的命令')
    
    args = parser.parse_args()
    
    if not args.command:
        tool.show_banner()
        tool.show_help()
        return
    
    if args.command == 'run':
        tool.run_complete_workflow()
    elif args.command == 'export':
        tool.export_test_schema()
    elif args.command == 'generate':
        tool.generate_jumpserver_commands()
    elif args.command == 'compare':
        tool.compare_schemas()
    elif args.command == 'status':
        tool.show_status()
    elif args.command == 'clean':
        tool.clean_temp_files()
    elif args.command == 'config':
        tool.show_config()
    elif args.command == 'help':
        tool.show_help()
    elif args.command == 'export-data':
        tool.generate_production_data_export_commands()

if __name__ == "__main__":
    main() 