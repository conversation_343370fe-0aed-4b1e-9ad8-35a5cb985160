/*
*
* 作用：生成数据库查询代码
* 具体使用参考gorm.io/gen，https://gorm.io/gen/
 */
package main

import (
	"flag"
	"log"
	"voderpltvv/config"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gen"
)

var cfgFile string

type BaseQuerier interface {

	// DELETE FROM @@table WHERE id = @id
	DeleteByID(id string) error

	// SELECT * FROM @@table WHERE id = @id LIMIT 1
	QueryOneByID(id string) (*gen.T, error)

	// SELECT * FROM @@table WHERE @field LIKE @value
	DazzyQueryByAny(field string, value string) ([]*gen.T, error)

	// SELECT * FROM @@table WHERE venue_id = @venueId
	QueryByVenueId(venueId string) ([]*gen.T, error)
}

func init() {
	flag.StringVar(&cfgFile, "c", "../.vscode/my_config.yaml", "config file path")
	flag.Parse()

	c := config.Config{
		Name: cfgFile,
	}
	if err := c.InitConfig(); err != nil {
		log.Fatalf("Load config error: %v", err)
	}
	c.InitLog()
	logrus.Info("Config loaded successfully from: ", cfgFile)
}

func main() {
	model.InitDB()

	db := model.DBMaster.Self
	if db == nil {
		log.Fatal("database connection is nil")
	}

	g := gen.NewGenerator(gen.Config{
		// 输出路径
		OutPath: "../erp_managent/service/dal",
		// 生成模式
		Mode: gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		// 是否生成单元测试
		WithUnitTest: false,
		// 数据库字段可为 null 值时的模型结构体字段类型
		FieldNullable: true,
		// 生成 mysql 数据库字段的数据类型(注释)
		FieldWithTypeTag: true,
		// 生成 gorm 标签的字段索引属性
		FieldWithIndexTag: true,
	})

	g.UseDB(db)

	basicModels := []interface{}{
		po.Member{},
		po.Venue{},
		po.VenueAndMember{},
		po.MemberCardLevel{},
		po.MemberCardOperation{},
		po.MemberOrder{},
		po.MemberDay{},
		po.PointsGoods{},
		po.MemberPoints{},
		po.PointsExchangeRecord{},
		po.PayBill{},
		po.MemberTransferMoney{},
		po.MemberRechargePackage{},
		po.MemberConsumption{},
		po.VenueAuthCode{},
		po.PayBill{},
		po.TimeCard{},
		po.TimeCardVerify{},
		po.TimeCardVerify{},
		po.BusinessStaff{},
		po.SystemOperationRecord{},
		// 新增酒水存取相关实体
		po.ProductStorage{},
		po.ProductWithdraw{},
		po.ProductStorageOrder{},
		po.ProductWithdrawOrder{},
		po.ProductStorageOperationLog{},
		po.WineStorageSetting{},
		// 权限系统相关实体
		po.PermissionRole{},
		po.RolePermissionConfig{},
		po.PermissionResource{},
		po.RolePermission{},
		po.EmployeeRoleAssignment{},
		po.RolePermissionTemplate{},
		po.SmsVerifyConfig{},
	}
	// 生成内置基础查询代码
	g.ApplyBasic(basicModels...)

	// 生成自定义基础查询代码
	g.ApplyInterface(func(BaseQuerier) {},
		basicModels...,
	)

	// 生成model特有的自定义查询代码
	// g.ApplyInterface(func(MemberQuerier) {}, po.Member{})

	g.Execute()
}
