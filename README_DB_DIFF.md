# 📊 数据库差异分析工具

> **一个脚本，两个文件，搞定所有数据库差异分析需求**

## 🚀 快速使用

### 核心工具
```bash
# 🔥 获取线上全量数据（解决存取统计问题）
python3 script/db_diff_tool.py export-data

# 一键对比数据库差异并生成完整同步方案  
python3 script/db_diff_tool.py compare
```

### 🔥 线上全量数据导出
生成获取线上完整数据的命令：
```bash
python3 script/db_diff_tool.py export-data
```
核心命令（在Jumpserver执行）：
```bash
mysqldump --skip-ssl --single-transaction --routines --triggers --events --complete-insert --hex-blob autoapp | gzip > prod_full_data_$(date +%Y%m%d_%H%M%S).sql.gz
```

### 生成的文件 (只有2个！)
| 文件 | 用途 | 说明 |
|------|------|------|
| 📄 `docs/sql/database_diff_complete_report.md` | **完整分析报告** | 差异分析+字段详情+索引分析+执行指南 |
| 📄 `docs/sql/database_sync_complete.sql` | **完整同步脚本** | CREATE+ALTER+INDEX+DROP+验证，一个SQL搞定 |

## 📋 使用步骤

### 1. 准备环境
确保生产环境的SQL文件已放入 `temp_db_diff/prod_schema.sql`

### 2. 执行分析
```bash
python3 script/db_diff_tool.py compare
```

### 3. 查看结果
- **📊 查看分析**: `docs/sql/database_diff_complete_report.md`
- **🔧 执行同步**: `docs/sql/database_sync_complete.sql`

## 🎯 核心功能

✅ **详细字段差异** - 新增/删除/修改字段的具体对比  
✅ **完整ALTER语句** - 可直接执行的ALTER TABLE语句  
✅ **索引差异分析** - 索引新增/删除的具体操作  
✅ **分阶段执行** - 备份→创建→修复→优化→验证  
✅ **安全保护** - 所有高危操作都有备份和注释保护  

## 📊 SQL文件结构

`database_sync_complete.sql` 包含完整的5个阶段：

```sql
-- 阶段1: 数据备份 (必须先执行)
-- mysqldump命令...

-- 阶段2: 创建新表 (测试环境独有)
CREATE TABLE `discount_gift_report` (...);
CREATE TABLE `member_card_level_del` (...);
CREATE TABLE `permission_audit_log` (...);

-- 阶段3: 修复字段和结构差异
ALTER TABLE `member_card_level` ADD COLUMN `balance_usage_scope` text;
ALTER TABLE `member_card_level` MODIFY COLUMN `level` bigint(20) DEFAULT NULL;

-- 阶段4: 索引差异修复
ALTER TABLE `product_storage` ADD INDEX `idx_venue_customer_state_time` (...);
ALTER TABLE `inventory_record` DROP INDEX `idx_inventory_record_status`;

-- 阶段5: 清理废弃表 (谨慎执行)
-- DROP TABLE IF EXISTS `hj_membercardinfo`; -- 需要手动取消注释

-- 最终验证
SELECT COUNT(*) as table_count FROM information_schema.tables...
```

## 🗄️ 文档位置

数据库相关文档统一存放在 `docs/sql/` 目录：
- `member_tables.md` - 会员系统表结构说明
- `database_diff_complete_report.md` - **完整差异分析报告**  
- `database_sync_complete.sql` - **完整数据库同步脚本**

## 🎊 极简高效

- ✅ **一个脚本** - `script/db_diff_tool.py`
- ✅ **两个文件** - 一个报告 + 一个SQL
- ✅ **全部差异** - 字段+索引+约束一次性分析
- ✅ **安全执行** - 分阶段+备份+验证

---

**工具位置**: `script/db_diff_tool.py`  
**结果目录**: `docs/sql/`  
**主要报告**: `docs/sql/database_diff_complete_report.md`  
**执行脚本**: `docs/sql/database_sync_complete.sql` 