{"name": "erp-saas-admin", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:stage": "vite build --mode stage", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "autoprefixer": "^10.4.20", "axios": "^1.7.9", "element-plus": "^2.9.4", "pinia": "^2.3.1", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.15", "vue": "^3.5.12", "vue-router": "^4.5.0"}, "devDependencies": {"@types/node": "^22.13.1", "@vitejs/plugin-vue": "^5.1.4", "sass": "^1.84.0", "typescript": "^5.7.3", "vite": "^5.4.10", "vue-tsc": "^2.2.0"}}