# 门店审核模块开发笔记

## 模块概述
### 核心功能
- 多维度筛选（时间/状态/区域/关键词）
- 卡片式信息展示（关键信息一眼可视）
- 审核留痕（操作记录不可删除）

### 业务规则
```mermaid
graph TD
    A[新申请] --> B{商务审核}
    B -->|通过| C[生成待激活授权]
    B -->|驳回| D[状态留痕]
    C --> H[门店激活]
```

## UI布局规范
### 全局布局
```txt
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台               |  |  +-------------------------+  |
|  | 门店审核 ← 当前      |  |  | 筛选操作栏              |  |
|  | 授权管理             |  |  | [时间] [状态] [搜索]    |  |
|  | 系统管理             |  |  |                         |  |
|  +----------------------+  |  | 卡片列表（3列自适应）   |  |
|                            |  | +-----+ +-----+ +-----+ |  |
|                            |  | |门店1| |门店2| |门店3| |  |
|                            |  | +-----+ +-----+ +-----+ |  |
|                            |  |                         |  |
|                            |  | 分页控制区域            |  |
|                            |  | [页码] [数量] [总数]    |  |
+----------------------------+--+-------------------------+--+
```

### 响应式规则
| 屏幕宽度 | 卡片列数 | 侧边栏状态 |
|----------|----------|------------|
| ≥1280px  | 3列      | 展开       |
| <=1279   | 2列      | 折叠       |

## 全局布局设计（新增章节）

### 全局组件规划
```bash
src/shared/components/layout/
├── AppLayout.vue         # 根布局组件
├── SideNavigation.vue    # 侧边导航组件
├── GlobalHeader.vue      # 顶部操作栏
└── ResponsiveHandler.vue # 响应式处理逻辑
```

### 布局状态管理
```typescript
// shared/types/layout.ts
interface LayoutState {
  sidebar: {
    collapsed: boolean
    visible: boolean
  }
  screenSize: 'desktop' | 'tablet'
}
```

### 开发阶段规划
1. 全局基础布局开发
   - [ ] 实现响应式容器组件
   - [ ] 侧边栏折叠/展开动效
   - [ ] 顶部操作栏用户信息模块
   - [ ] 全局加载状态指示器

2. 门店审核模块集成
   - [ ] 布局样式适配（间距/边距调整）
   - [ ] 响应式断点测试
   - [ ] 全局状态联动（侧边栏折叠时内容区宽度调整）

## 响应式实现方案
```typescript
// 在 presenter.ts 中实现
const updateLayoutState = () => {
  const { width } = useWindowSize()
  
  if (width.value >= 1280) {
    layoutState.value = {
      sidebar: { collapsed: false, visible: true },
      screenSize: 'desktop'
    }
  } else {
    layoutState.value = {
      sidebar: { collapsed: true, visible: true },
      screenSize: 'tablet'
    }
  }
}
```

## 开发任务优先级调整
阶段 | 任务 | 优先级 | 依赖项
--- | --- | --- | ---
1 | 全局布局基础框架 | P0 | 无
2 | 响应式断点测试 | P1 | 阶段1完成
3 | 门店审核模块布局集成 | P0 | 阶段1完成

## 工程结构
```bash
src/modules/store/audit/
├── list/                 # 审核列表功能
│   ├── components/       # 私有组件
│   │   ├── AuditCard.vue  # 审核卡片组件
│   │   └── FilterBar.vue  # 筛选栏组件
│   ├── index.vue         # View层
│   ├── viewmodel.ts      # UI状态/计算属性定义
│   ├── presenter.ts      # 协调层
│   ├── interactor.ts     # 业务交互（对接API）
│   └── converter.ts      # 数据转换
└── detail/              # 审核详情功能
    ├── components/
    │   └── HistoryTimeline.vue # 时间轴组件
    ├── index.vue
    └── ... # VIPER结构文件
```

## 核心交互逻辑
### 批量审核流程
```mermaid
sequenceDiagram
    participant V as View
    participant P as Presenter
    participant I as Interactor
    
    V->>P: 用户选择多个卡片
    P->>I: 获取批量审核模板
    I-->>P: 返回模板列表
    P->>V: 显示模板选择弹窗
    V->>P: 用户选择模板+备注
    P->>I: 提交批量审核请求
    I->>API: 发送批量审核API
    API-->>I: 返回结果
    I-->>P: 转换数据格式
    P->>V: 更新UI状态
```

## 状态管理设计
### UI状态类型
```typescript
interface AuditState {
  list: AuditItem[] // 审核列表数据
  filters: {
    status: 'pending' | 'approved' | 'rejected'
    dateRange: [Date, Date]
    searchKey: string
  }
  pagination: {
    current: number
    pageSize: 10 | 20 | 50
    total: number
  }
  selectedIds: string[] // 多选ID
}
```

### 实体类型
```typescript
// 审核状态枚举
enum AuditStatus {
  PENDING = 0,   // 待审核
  APPROVED = 1,  // 已通过
  REJECTED = 2   // 已拒绝
}

// 操作状态枚举
enum OperationStatus {
  PASS = 1,      // 通过
  REJECT = 2     // 拒绝
}
```

## 接口定义（更新版）

### API接口规范（适配服务端路由）
1. **单条审核操作**：
```typescript
POST /api/venue/auth/audit
Body: {
  venue_id: string      // 门店ID（对应服务端VenueVO.id）
  status: OperationStatus // 使用数字枚举（0-待审 1-通过 2-拒绝）
  remark?: string       // 审核备注
}
```

2. **审核状态查询**：
```typescript
POST /api/venue/auth/status
Body: {
  venue_id: string
}
Response: {
  auditStatus: AuditStatus
  auditRecords: AuditHistory[]
}
```

### 新增数据结构
```typescript
// 审核历史记录实体
interface AuditHistory {
  operatorId: string    // 操作人ID
  operateTime: number   // 操作时间戳
  prevStatus: AuditStatus // 变更前状态
  currentStatus: AuditStatus // 变更后状态
  remark: string        // 审核意见
  attachments: string[] // 附件URL（如有）
}
```

## 服务端数据结构比对
需要补充字段：
1. VenueVO 需要增加关联字段：
```go
AuditRecords []AuditHistory `json:"auditRecords"` // 审核历史记录
OperatorID   string         `json:"operatorId"`   // 最后操作人ID
```

2. 需要新增审核历史表结构：
```go
type AuditHistory struct {
    ID           string // 记录ID
    VenueID      string // 门店ID
    OperatorID   string // 操作人ID
    OperateTime  int64  // 操作时间戳
    PrevStatus   int    // 变更前状态
    CurrentStatus int   // 变更后状态
    Remark       string // 审核意见
    Attachments  string // JSON数组存储附件URL
}
```

## 开发任务分解
1. 基础UI搭建
   - [ ] 实现响应式卡片布局
   - [ ] 筛选栏组件开发
   - [ ] 分页组件集成

2. 状态管理实现
   - [ ] 定义Viewmodel接口
   - [ ] 实现Presenter状态管理
   - [ ] 数据转换器开发

3. 业务逻辑实现
   - [ ] 筛选条件组合查询
   - [ ] WebSocket消息监听

4. 增强功能
   - [ ] 虚拟滚动优化
   - [ ] 筛选条件持久化
   - [ ] 操作撤销功能

## 待明确事项
1. 筛选条件：
   - 时间选择器是否需要限制可选范围？
   - 地区筛选使用级联选择还是独立下拉？

2. 权限控制：
   - 驳回操作是否需要二级审批？

3. 性能优化：
   - 卡片列表是否需要虚拟滚动？
   - 图片懒加载实现方案？