# 库存导入功能产品说明文档

## 📋 产品概述

### 功能定位
商务后台库存管理系统的核心功能，支持通过Excel模板批量更新门店商品库存数量，提供增量更新和全量同步两种模式，支持指定库存有效时间便于后续数据校准。

### 目标用户
- 门店管理员：更新门店库存数据
- 商务运营：批量调整多门店库存
- 仓库管理员：盘点后更新系统库存

### 核心价值
- **效率提升**：批量处理替代逐个录入，提升10倍操作效率
- **数据准确**：VLOOKUP智能匹配，减少人工错误
- **风险控制**：多层验证机制，确保数据安全
- **数据溯源**：支持指定库存时间，便于历史数据校准

## 🎯 功能流程

### 整体流程图
```
选择门店 → 配置参数 → 下载模板 → 填写数据 → 上传文件 → 数据校验 → 预览确认 → 执行导入 → 查看结果
```

### 详细步骤

#### 步骤1：选择门店
**操作**：从下拉列表选择要更新库存的目标门店
- 支持门店搜索和筛选
- 显示门店基本信息（名称、地址、状态）
- 权限控制：仅显示用户有权限的门店

#### 步骤2：导入参数配置
**操作**：配置导入参数和下载模板

**导入参数设置**：
1. **库存有效时间**：
   - 选择库存数据的生效时间（默认为当前时间）
   - 用于后续库存校准和销售记录对比分析
   - 支持选择历史时间进行补录
   - 时间格式：YYYY-MM-DD HH:mm:ss

2. **导入模式选择**：
   - **增量更新模式**（默认）：仅更新上传文件中包含的商品库存
   - **全量同步模式**：上传文件中未包含的商品库存重置为0
   
   ```
   💡 模式说明：
   增量更新：适用于部分商品库存调整
   全量同步：适用于盘点后的完整库存快照导入
   ```

**模板下载**：
- **模板结构**：
  ```
  | 商品名称 | 库存数量 |
  |---------|---------|
  | 茅台酒  | 100     |
  | 五粮液  | 50      |
  ```
- **填写说明**：
  - 商品名称：必须与系统中商品名称完全一致
  - 库存数量：正整数，表示更新后的库存数量
  - 不支持新增商品，仅更新现有商品库存

#### 步骤3：上传文件
**操作**：上传填写完成的Excel文件
- **支持格式**：Excel (.xlsx, .xls) 和 CSV (.csv)
- **文件限制**：最大10MB，最多10,000行记录
- **基础验证**：
  - 文件格式检查
  - 表头格式验证
  - 必填字段检查
  - 数据类型验证

#### 步骤4：数据校验与匹配
**系统自动处理**：
1. **获取商品列表**：调用API获取选中门店的所有商品信息
2. **VLOOKUP匹配**：根据商品名称匹配商品ID和当前库存
3. **模式处理**：根据选择的导入模式处理数据
4. **数据验证**：调用后端API进行业务规则校验
5. **生成预览**：整理匹配结果和错误信息

**匹配逻辑**：
```typescript
// 匹配规则：商品名称完全一致
const matchProduct = (uploadName: string, productList: ProductItem[]) => {
  return productList.find(product => product.productName === uploadName)
}

// 全量同步模式处理
const processFullSyncMode = (uploadData: any[], allProducts: ProductItem[]) => {
  const uploadProductNames = new Set(uploadData.map(row => row['商品名称']))
  const resetProducts = allProducts.filter(product => 
    !uploadProductNames.has(product.productName)
  )
  return resetProducts.map(product => ({
    productId: product.productId,
    productName: product.productName,
    currentStock: product.currentStock,
    newStock: 0,
    isReset: true
  }))
}
```

#### 步骤5：预览确认
**展示内容**：

**导入参数确认**：
```
📅 库存有效时间：2024-01-15 14:30:00
🔄 导入模式：全量同步模式
🏪 目标门店：北京朝阳店
```

**统计摘要**：
```
📊 导入预览
总计：100条记录
✅ 可导入：85条
❌ 错误记录：10条  
⚠️ 警告提醒：5条
🔄 重置为0：150条（仅全量同步模式）
```

**可导入商品列表**：
```
┌─────────────────────────────────────────┐
│ 商品名称    │ 当前库存 │ 新库存 │ 变动   │
│ 茅台酒     │ 50      │ 100   │ +50   │
│ 五粮液     │ 30      │ 25    │ -5    │
│ 剑南春     │ 20      │ 50    │ +30   │
└─────────────────────────────────────────┘
```

**全量同步模式特有展示**：
```
🔄 将重置为0的商品（150条）
┌─────────────────────────────────────────┐
│ 商品名称        │ 当前库存 │ 新库存 │ 说明   │
│ 其他酒品A      │ 20      │ 0     │ 重置   │
│ 其他酒品B      │ 15      │ 0     │ 重置   │
│ 其他酒品C      │ 30      │ 0     │ 重置   │
└─────────────────────────────────────────┘
```

**错误商品列表**：
```
❌ 不存在的商品（10条）- 将被跳过
┌─────────────────────────────────────────┐
│ 商品名称        │ 库存数量 │ 错误原因      │
│ 不存在的酒品A   │ 20      │ 商品不存在    │
│ 错误商品名B     │ 30      │ 商品不存在    │
└─────────────────────────────────────────┘
```

**操作选项**：
- **[确认导入]**：执行库存更新（包括重置操作）
  - 如果存在不存在的商品，会弹窗警告需要用户确认
- **[下载预览报告]**：下载完整的预览结果
- **[下载错误报告]**：下载不存在商品的详细清单
- **[重新配置]**：返回修改导入参数
- **[重新上传]**：修正错误后重新上传文件

**确认导入弹窗**：
当存在不存在的商品时，系统会弹出确认对话框：
```
⚠️ 导入确认

发现 10 个商品不存在，这些商品将被跳过：
• 不存在的酒品A
• 错误商品名B
• 已下架商品C
...

仅导入 85 个有效商品，是否继续？

[查看详细清单] [取消导入] [确认导入]
```

#### 步骤6：执行导入
**操作**：确认后执行库存更新
- 调用后端API批量更新库存
- 记录库存有效时间到数据库
- 实时显示导入进度
- 支持大批量数据分批处理
- 异常中断时保护数据完整性

**全量同步模式执行**：
1. 先更新上传文件中的商品库存
2. 再将未包含的商品库存重置为0
3. 确保操作的原子性

#### 步骤7：查看结果
**结果展示**：
```
🎉 导入完成

📊 导入统计
- 成功更新：85条记录
- 重置为0：150条记录（全量同步）
- 跳过错误：10条记录
- 库存时间：2024-01-15 14:30:00
- 处理时间：3.5秒
- 导入时间：2024-01-15 14:35:25

📋 详细结果
✅ 成功更新的商品：
- 茅台酒：50 → 100 (+50)
- 五粮液：30 → 25 (-5)
- 剑南春：20 → 50 (+30)
...

🔄 重置的商品（全量同步）：
- 其他酒品A：20 → 0 (重置)
- 其他酒品B：15 → 0 (重置)
...

❌ 跳过的错误记录：
- 不存在的酒品A（商品不存在）
- 错误商品名B（商品不存在）
...
```

**后续操作**：
- **[查看库存]**：跳转到库存列表查看更新结果
- **[下载导入报告]**：下载完整的导入结果报告
- **[继续导入]**：开始新的导入任务

## 🔧 技术规格

### API接口
1. **商品查询**：`POST /api/business/product/query`
2. **预览导入**：`POST /api/business/inventory/import/preview`
3. **执行导入**：`POST /api/business/inventory/import/execute`
4. **库存查询**：`POST /api/business/inventory/stock/list`

### 数据结构
**Excel模板格式**：
```
Row 1: 商品名称, 库存数量
Row 2: 茅台酒, 100
Row 3: 五粮液, 50
...
```

**API请求格式**：
```json
{
  "venueId": "store_001",
  "fileContent": "base64_encoded_file_content",
  "fileName": "inventory_import.xlsx",
  "importType": "stock_update",
  "effectiveTime": "2024-01-15 14:30:00",
  "syncMode": "full_sync",
  "skipErrors": true
}
```

**API响应格式**：
```json
{
  "taskId": "task_123456",
  "status": "SUCCESS",
  "totalCount": 100,
  "successCount": 85,
  "resetCount": 150,
  "failureCount": 10,
  "effectiveTime": "2024-01-15 14:30:00",
  "message": "导入成功"
}
```

### 验证规则
**前端验证**：
- 文件格式：仅支持Excel和CSV
- 文件大小：最大10MB
- 记录数量：最多10,000行
- 必填字段：商品名称、库存数量
- 数据类型：库存数量必须为非负整数
- 时间格式：库存有效时间格式验证

**后端验证**：
- 商品存在性：商品名称必须在系统中存在
- 权限验证：用户必须有门店库存管理权限
- 时间合理性：库存有效时间不能超过当前时间太多
- 业务规则：符合库存管理业务逻辑
- 并发控制：避免同时修改同一商品库存

### 错误处理
**错误分类**：
1. **阻断性错误**：文件格式错误、权限不足、系统异常
2. **跳过性错误**：商品不存在、数据格式错误
3. **警告提醒**：库存清零、大幅变动、异常数值

**处理策略**：
- **阻断性错误**：停止处理，要求用户修正
- **跳过性错误**：过滤错误记录，但需要用户确认后才能继续处理有效数据
- **警告提醒**：显示警告信息，允许用户选择继续

**商品不存在确认机制**：
1. **检测阶段**：在数据校验时识别不存在的商品
2. **展示阶段**：在预览页面明确列出错误商品清单
3. **确认阶段**：点击"确认导入"时弹窗警告，需要用户明确确认
4. **执行阶段**：仅处理有效商品，跳过不存在的商品
5. **结果阶段**：在导入结果中详细记录跳过的商品

## 📊 业务规则

### 库存更新规则
1. **覆盖更新**：新库存数量完全替换当前库存
2. **商品匹配**：基于商品名称精确匹配，不支持模糊匹配
3. **权限控制**：仅能更新有权限门店的商品库存
4. **并发保护**：同一商品同时更新时，后提交的操作失败
5. **时间记录**：记录库存有效时间，便于后续数据校准

### 导入模式规则
**增量更新模式**：
- 仅更新上传文件中包含的商品
- 其他商品库存保持不变
- 适用于部分商品调整场景

**全量同步模式**：
- 更新上传文件中包含的商品
- 未包含的商品库存重置为0
- 适用于盘点后完整库存导入
- 操作前会显示重置商品清单供确认

### 数据校验规则
1. **商品存在性**：商品必须在选中门店中存在
2. **库存范围**：库存数量 ≥ 0，≤ 999,999
3. **重复处理**：同一文件中重复商品取最后一条记录
4. **特殊值处理**：空值视为0，非数字值报错
5. **时间合理性**：库存有效时间在合理范围内

### 异常警告规则
1. **库存清零**：当前库存 > 0，新库存 = 0
2. **大幅增长**：新库存 > 当前库存 × 3
3. **大幅减少**：新库存 < 当前库存 × 0.3
4. **异常数值**：库存数量 > 10,000
5. **批量重置**：全量同步模式下大量商品重置为0

## 🚀 性能指标

### 处理能力
- **文件大小**：支持最大10MB文件
- **记录数量**：单次最多处理10,000条记录
- **处理速度**：平均每秒处理500条记录
- **并发支持**：支持10个用户同时导入

### 响应时间
- **文件上传**：< 3秒（10MB文件）
- **数据验证**：< 5秒（1000条记录）
- **预览生成**：< 3秒（包含全量同步计算）
- **导入执行**：< 15秒（1000条记录 + 重置操作）

## 🔒 安全控制

### 权限管理
- **门店权限**：仅能操作有权限的门店
- **功能权限**：需要"库存管理"权限
- **数据权限**：按照用户角色限制可见数据范围
- **操作权限**：全量同步模式需要更高级别权限

### 操作审计
- **导入记录**：记录每次导入的详细信息
- **变更日志**：记录库存变更的前后值和有效时间
- **操作追踪**：记录操作人、操作时间、操作内容、导入模式
- **数据溯源**：支持根据库存有效时间追溯历史变更

### 数据保护
- **文件安全**：上传文件临时存储，处理完成后自动删除
- **传输加密**：API通信使用HTTPS加密
- **访问控制**：基于JWT token的身份验证
- **操作确认**：全量同步模式和增量同步都需要二次确认

## 📈 监控指标

### 业务指标
- **导入成功率**：成功导入记录数 / 总记录数
- **错误率**：错误记录数 / 总记录数  
- **使用频率**：每日/每周导入次数
- **数据质量**：商品匹配率、数据准确率
- **模式使用**：增量更新 vs 全量同步使用比例

### 技术指标
- **系统响应时间**：各API接口响应时间
- **错误率**：系统错误、网络错误频率
- **资源使用**：CPU、内存、存储使用情况
- **并发处理**：同时在线用户数、并发请求数

## 🔄 后续优化

### 短期优化（1-2个月）
1. **智能匹配**：支持商品编码、条形码等多字段匹配
2. **模板增强**：支持更多字段（成本价、售价、安全库存）
3. **批量优化**：大文件分批处理，提升处理速度
4. **时间选择**：更灵活的时间选择器和历史时间补录

### 中期优化（3-6个月）
1. **历史记录**：导入历史查询、数据回滚功能
2. **模板定制**：支持自定义模板字段和验证规则
3. **API增强**：支持增量更新、差异比较
4. **数据校准**：基于库存时间的销售数据校准功能

### 长期优化（6-12个月）
1. **智能分析**：库存变动趋势分析、异常检测
2. **自动化**：定时导入、API对接、系统集成
3. **多门店**：跨门店库存调拨、统一管理
4. **AI辅助**：智能库存建议、预测补货

---

**文档版本**：v2.0  
**创建时间**：2024-01-15  
**更新时间**：2024-01-15  
**维护人员**：产品团队  
**审核状态**：待审核 