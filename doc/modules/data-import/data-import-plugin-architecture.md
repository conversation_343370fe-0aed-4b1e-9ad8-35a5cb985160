# 数据导入功能说明文档

## 📋 功能概述

数据导入模块是商务后台ERP系统的核心功能之一，采用插件化架构设计，支持多种类型数据的批量导入。系统提供统一的导入流程和用户界面，同时支持针对不同数据类型的定制化功能。

### 核心特性
- **插件化架构**：模块化设计，易于扩展和维护
- **统一流程**：标准化的导入步骤和用户体验
- **类型安全**：TypeScript确保代码质量和类型安全
- **自定义组件**：支持插件自定义验证和预览界面
- **实时反馈**：导入进度实时显示，错误信息详细提示

### 支持的导入类型
1. **库存导入** ✅：商品库存数量批量更新，支持增量和全量同步模式
2. **存酒导入** 🧪：会员存酒记录管理，支持酒品信息导入（测试中）
3. **包厢导入** 🚧：包厢基础信息和配置管理（规划中）
4. **商品导入** 🚧：商品信息和价格管理（规划中）
5. **会员导入** 🚧：会员基础信息管理（规划中）

## 🏗️ 架构设计

### 核心组件

#### 1. 插件接口定义 (`core/types.ts`)
- **DataImportPlugin**: 插件主接口，定义插件的基本信息和功能组件
- **TemplateGenerator**: 模板生成器接口，负责生成导入模板
- **DataValidator**: 数据验证器接口，负责数据验证逻辑
- **DataPreviewer**: 数据预览器接口，负责数据预览和格式化
- **DataImporter**: 数据导入器接口，负责实际的数据导入操作

#### 2. 基础类 (`core/BasePlugin.ts`)
- **BaseDataImportPlugin**: 插件抽象基类，提供默认实现
- **BaseTemplateGenerator**: 模板生成器基类
- **BaseDataValidator**: 数据验证器基类
- **BaseDataPreviewer**: 数据预览器基类
- **BaseDataImporter**: 数据导入器基类

#### 3. 插件管理器 (`core/PluginManager.ts`)
- **DataImportPluginManager**: 插件管理器，负责插件的注册、启用、禁用和生命周期管理
- 提供插件状态管理和错误处理机制

#### 4. 插件注册系统 (`core/PluginRegistry.ts`)
- **PluginRegistry**: 插件注册表，自动发现和注册所有插件
- 提供插件查询和管理功能

### 插件目录结构

```
src/modules/store/data-import/
├── core/                           # 核心框架
│   ├── types.ts                   # 类型定义和接口
│   ├── BasePlugin.ts              # 基础类实现
│   ├── PluginManager.ts           # 插件管理器
│   └── PluginRegistry.ts          # 插件注册系统
├── plugins/                       # 插件目录
│   ├── inventory/                 # 库存导入插件 ✅
│   │   ├── index.ts              # 插件主入口
│   │   ├── TemplateGenerator.ts  # 模板生成器
│   │   ├── DataValidator.ts      # 数据验证器
│   │   ├── DataPreviewer.ts      # 数据预览器
│   │   ├── DataImporter.ts       # 数据导入器
│   │   └── components/           # 自定义组件
│   │       ├── InventoryValidationPanel.vue
│   │       └── InventoryPreviewPanel.vue
│   ├── wine-storage/              # 存酒导入插件 🧪
│   │   ├── index.ts              # 插件主入口
│   │   ├── TemplateGenerator.ts  # 模板生成器
│   │   ├── DataValidator.ts      # 数据验证器
│   │   ├── DataPreviewer.ts      # 数据预览器
│   │   ├── DataImporter.ts       # 数据导入器
│   │   └── components/           # 自定义组件
│   ├── rooms/                     # 包厢导入插件 🚧
│   ├── products/                  # 商品导入插件 🚧
│   └── members/                   # 会员导入插件 🚧
├── components/                    # 共享组件
│   ├── steps/                    # 导入步骤组件
│   │   ├── StepSelectStore.vue   # 选择门店
│   │   ├── StepDownloadTemplate.vue # 下载模板
│   │   ├── StepUploadFile.vue    # 上传文件
│   │   ├── StepValidatePreview.vue # 验证预览
│   │   └── StepImportResult.vue  # 导入结果
│   └── common/                   # 通用组件
├── views/                         # 页面视图
│   ├── index.vue                 # 插件列表页
│   └── import-wizard.vue         # 导入向导页
└── index.ts                       # 模块主入口
```

## 🔄 导入流程

### 标准导入流程

所有插件都遵循统一的5步导入流程：

```
1. 选择门店 → 2. 下载模板 → 3. 上传文件 → 4. 验证预览 → 5. 导入结果
```

#### 步骤详解

1. **选择门店** (`StepSelectStore`)
   - 选择要导入数据的目标门店
   - 权限验证，仅显示有权限的门店
   - 支持门店搜索和筛选

2. **下载模板** (`StepDownloadTemplate`)
   - 根据插件生成对应的导入模板
   - 显示字段说明和填写要求
   - 提供示例数据参考

3. **上传文件** (`StepUploadFile`)
   - 支持Excel (.xlsx, .xls) 和 CSV (.csv) 格式
   - 文件大小限制：最大10MB
   - 实时解析和基础验证

4. **验证预览** (`StepValidatePreview`)
   - 数据格式验证和业务规则检查
   - 错误和警告信息详细展示
   - 支持插件自定义验证界面

5. **导入结果** (`StepImportResult`)
   - 显示导入统计和详细结果
   - 错误记录和处理建议
   - 支持结果导出和后续操作

### 插件生命周期

1. **安装** (`onInstall`): 插件首次注册时执行
2. **启用** (`onEnable`): 插件启用时执行
3. **禁用** (`onDisable`): 插件禁用时执行
4. **卸载** (`onUninstall`): 插件卸载时执行
5. **导入前** (`beforeImport`): 数据导入前执行
6. **导入后** (`afterImport`): 数据导入后执行

## 📦 插件详情

### 1. 库存导入插件 (`inventory`) ✅ **已完成**

#### 功能特性
- **批量更新**：支持商品库存数量批量更新，提升操作效率
- **双模式支持**：增量更新和全量同步两种模式，适应不同业务场景
- **智能匹配**：基于商品名称精确匹配，支持VLOOKUP智能识别
- **时间记录**：支持指定库存有效时间，便于历史数据校准和溯源
- **多层验证**：前端和后端双重验证机制，确保数据准确性和安全性
- **实时预览**：导入前提供详细预览，包含匹配结果和错误提示
- **异常警告**：智能识别异常数据，如库存清零、大幅变动等

#### 导入字段
```
商品名称    | 必填 | 与系统商品名称完全一致，支持中文名称
库存数量    | 必填 | 非负整数，表示更新后的库存数量
```

#### 导入模式
- **增量更新模式**（默认）：仅更新上传文件中包含的商品库存，其他商品保持不变
- **全量同步模式**：更新文件中的商品，未包含的商品库存重置为0，适用于盘点场景

#### 业务规则
- 商品必须在选中门店中存在，不支持新增商品
- 库存数量范围：0 ≤ 数量 ≤ 999,999
- 同一文件中重复商品取最后一条记录
- 支持库存有效时间设置，便于后续数据校准
- 权限控制：仅能更新有权限门店的商品库存

#### 技术规格
- **文件支持**：Excel (.xlsx, .xls) 和 CSV (.csv) 格式
- **文件限制**：最大10MB，最多10,000条记录
- **处理性能**：平均每秒处理500条记录
- **并发支持**：支持10个用户同时导入

#### API接口
- **商品查询**：`POST /api/business/product/query`
- **预览导入**：`POST /api/business/inventory/import/preview`
- **执行导入**：`POST /api/business/inventory/import/execute`
- **库存查询**：`POST /api/business/inventory/stock/list`

### 2. 存酒导入插件 (`wine-storage`) 🧪 **测试中**

#### 功能特性
- **会员存酒**：支持会员存酒记录批量导入
- **酒品管理**：酒品信息和存储数量管理
- **数据验证**：会员和酒品存在性验证
- **智能分析**：存酒统计分析和价值评估

#### 导入字段
```
会员编号    | 必填 | 存酒会员的唯一标识
酒品名称    | 必填 | 存储酒品的名称
存储数量    | 可选 | 存储的酒品数量（瓶）
```

#### 开发状态
- **前端界面**：✅ 已完成，包含自定义验证和预览组件
- **数据验证**：✅ 已完成，支持业务规则验证
- **API接口**：🚧 开发中，当前使用模拟数据
- **后端逻辑**：🚧 开发中，待完善存酒业务逻辑

### 3. 包厢导入插件 (`rooms`) 🚧 **规划中**

- **状态**: 规划中
- **功能**: 包厢基础信息批量导入，包厢类型、容量、价格等配置管理
- **计划字段**: 包厢编号、名称、类型、容量、面积、时价、描述

### 4. 商品导入插件 (`products`) 🚧 **规划中**

- **状态**: 规划中
- **功能**: 商品信息、价格、分类等数据导入

### 5. 会员导入插件 (`members`) 🚧 **规划中**

- **状态**: 规划中
- **功能**: 会员基础信息、等级、联系方式等数据导入

## 🔧 技术特性

### 1. 类型安全
- **TypeScript支持**：完整的类型定义和接口约束
- **编译时检查**：在开发阶段发现类型错误
- **智能提示**：IDE提供完整的代码提示和自动补全

### 2. 扩展性
- **插件化架构**：易于添加新的导入类型
- **标准化接口**：统一的插件开发规范
- **热插拔支持**：无需重启系统即可启用/禁用插件
- **自定义组件**：支持插件自定义验证和预览界面

### 3. 可维护性
- **职责分离**：每个插件独立维护，互不影响
- **统一错误处理**：标准化的错误处理和日志记录
- **清晰架构**：模块化设计，代码结构清晰
- **完整文档**：详细的开发文档和使用说明

### 4. 用户体验
- **统一界面**：所有插件共享统一的用户界面
- **实时反馈**：导入进度实时显示，状态及时更新
- **详细提示**：错误信息详细，提供修复建议
- **操作简便**：5步导入流程，操作简单直观

### 5. 性能优化
- **分批处理**：大文件分批处理，避免内存溢出
- **进度控制**：支持导入过程中的暂停和取消
- **异步处理**：非阻塞式导入，不影响其他操作
- **错误恢复**：支持错误恢复和重试机制

## 🛠️ 库存导入功能详解 ✅ **已完成开发**

### 核心功能特性

#### 1. 双模式导入
**增量更新模式**（默认）
- 仅更新上传文件中包含的商品库存
- 其他商品库存保持不变
- 适用于部分商品库存调整场景

**全量同步模式**
- 更新上传文件中包含的商品库存
- 未包含的商品库存重置为0
- 适用于盘点后完整库存导入
- 操作前会显示重置商品清单供确认

#### 2. 智能数据处理
- **VLOOKUP匹配**：根据商品名称智能匹配商品ID
- **重复处理**：同一文件中重复商品取最后一条记录
- **特殊值处理**：空值视为0，非数字值报错
- **商品验证**：验证商品在门店中的存在性

#### 3. 多层验证机制
**前端验证**
- 文件格式：仅支持Excel和CSV
- 文件大小：最大10MB
- 记录数量：最多10,000行
- 必填字段：商品名称、库存数量
- 数据类型：库存数量必须为非负整数

**后端验证**
- 商品存在性：商品名称必须在系统中存在
- 权限验证：用户必须有门店库存管理权限
- 业务规则：符合库存管理业务逻辑
- 并发控制：避免同时修改同一商品库存

#### 4. 异常警告系统
- **库存清零**：当前库存 > 0，新库存 = 0
- **大幅增长**：新库存 > 当前库存 × 3
- **大幅减少**：新库存 < 当前库存 × 0.3
- **异常数值**：库存数量 > 10,000
- **批量重置**：全量同步模式下大量商品重置为0

### 导入流程详解

#### 步骤1：选择门店和配置参数
- 选择要更新库存的目标门店
- 配置库存有效时间（默认当前时间）
- 选择导入模式（增量更新/全量同步）

#### 步骤2：下载和填写模板
```
模板格式：
| 商品名称 | 库存数量 |
|---------|---------|
| 茅台酒  | 100     |
| 五粮液  | 50      |
```

#### 步骤3：数据验证和预览
- 自动匹配商品信息
- 显示匹配结果和错误信息
- 全量同步模式显示重置商品清单
- 提供详细的预览报告

#### 步骤4：确认导入
- 显示导入统计摘要
- 错误商品确认机制
- 支持下载预览报告和错误清单

#### 步骤5：执行导入和查看结果
- 实时显示导入进度
- 详细的导入结果统计
- 支持下载导入报告
- 提供后续操作选项

## 🚀 开发指南

### 创建新插件

#### 1. 基础结构
```typescript
// plugins/your-plugin/index.ts
export const yourPlugin: DataImportPlugin = {
  id: 'your-plugin',
  name: 'your-plugin',
  displayName: '您的插件名称',
  description: '插件功能描述',
  version: '1.0.0',
  author: 'Your Name',
  icon: 'YourIcon',
  enabled: true,
  order: 10,

  templateGenerator: new YourTemplateGenerator(),
  dataValidator: new YourDataValidator(),
  dataPreviewer: new YourDataPreviewer(),
  dataImporter: new YourDataImporter(),
}
```

#### 2. 实现核心组件
- **TemplateGenerator**: 继承 `BaseTemplateGenerator`
- **DataValidator**: 继承 `BaseDataValidator`
- **DataPreviewer**: 继承 `BaseDataPreviewer`
- **DataImporter**: 继承 `BaseDataImporter`

#### 3. 注册插件
在 `PluginRegistry.ts` 中添加新插件到注册列表

### 插件开发最佳实践

1. **遵循接口规范**：严格按照接口定义实现功能
2. **完整错误处理**：提供详细的错误信息和处理建议
3. **数据验证完整**：实现前端和后端双重验证
4. **用户体验优先**：提供清晰的操作指引和反馈
5. **性能优化**：处理大量数据时考虑性能影响
6. **自定义组件**：根据需要提供自定义验证和预览界面

## 📊 性能指标

### 库存导入性能
- **文件大小**：支持最大10MB文件
- **记录数量**：单次最多处理10,000条记录
- **处理速度**：平均每秒处理500条记录
- **并发支持**：支持10个用户同时导入

### 响应时间
- **文件上传**：< 3秒（10MB文件）
- **数据验证**：< 5秒（1000条记录）
- **预览生成**：< 3秒（包含全量同步计算）
- **导入执行**：< 15秒（1000条记录 + 重置操作）

## 🔒 安全控制

### 权限管理
- **门店权限**：仅能操作有权限的门店
- **功能权限**：需要相应的数据导入权限
- **数据权限**：按照用户角色限制可见数据范围

### 数据保护
- **文件安全**：上传文件临时存储，处理完成后自动删除
- **传输加密**：API通信使用HTTPS加密
- **访问控制**：基于JWT token的身份验证
- **操作审计**：记录所有导入操作的详细日志

## 🔄 未来规划

### 短期优化（1-2个月）
1. **智能匹配增强**：支持商品编码、条形码等多字段匹配
2. **模板功能扩展**：支持更多字段和自定义模板
3. **批量处理优化**：大文件分批处理，提升处理速度
4. **插件配置界面**：提供可视化的插件配置管理

### 中期优化（3-6个月）
1. **导入历史管理**：完整的导入历史记录和回滚功能
2. **数据映射功能**：支持字段映射和数据转换
3. **API集成支持**：支持从外部系统直接导入数据
4. **插件市场**：支持第三方插件开发和分发

### 长期规划（6-12个月）
1. **智能分析**：数据变动趋势分析、异常检测
2. **自动化导入**：定时导入、API对接、系统集成
3. **多租户支持**：支持多租户环境下的插件管理
4. **AI辅助功能**：智能数据建议、预测分析

## 📋 版本信息

- **当前版本**：v2.0.0
- **最后更新**：2024年12月
- **维护状态**：积极开发中
- **技术栈**：Vue 3 + TypeScript + Element Plus

---

**文档维护**：产品团队 & 开发团队
**更新频率**：随功能迭代持续更新
**反馈渠道**：技术文档反馈群