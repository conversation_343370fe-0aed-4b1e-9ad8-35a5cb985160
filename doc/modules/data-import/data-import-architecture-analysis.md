# 数据导入架构分析与改进建议

## 📋 当前架构分析

### 现有数据流转机制

#### 1. 原始架构问题
```mermaid
graph TD
    A[Step 1: 选择门店] --> B[Step 2: 下载模板]
    B --> C[Step 3: 上传文件]
    C --> D[Step 4: 验证预览]
    D --> E[Step 5: 导入结果]
    
    subgraph "数据传递问题"
        F[每个Step独立状态]
        G[数据通过Props传递]
        H[无持久化机制]
        I[无法跨Step共享]
    end
    
    A -.-> F
    B -.-> G
    C -.-> H
    D -.-> I
```

**主要问题：**
1. **数据孤岛**：每个step组件内部管理自己的状态，缺乏统一的数据管理
2. **传递复杂**：门店信息等关键数据需要在每个step之间手动传递
3. **无持久化**：刷新页面或意外关闭会丢失所有进度
4. **状态不一致**：同一数据在不同组件中可能出现不一致
5. **难以扩展**：新增step或修改数据结构需要修改多个组件

#### 2. 当前数据传递方式
```typescript
// import-wizard.vue 中的数据传递
const stepData = ref<Record<string, any>>({})

const handleStepDataUpdate = (data: Record<string, any>) => {
  stepData.value = { ...stepData.value, ...data }
}

// 每个step组件需要手动处理
<component 
  :is="currentStepComponent" 
  v-bind="currentStepProps"
  @update:step-data="handleStepDataUpdate"
/>
```

### 各Step的数据需求分析

#### Step 1: 选择门店 (StepSelectStore)
**输入数据：** 无
**输出数据：**
- `selectedStoreId`: 选中的门店ID
- `selectedStore`: 完整的门店信息对象

**需要持续传递的数据：**
- 门店信息需要在后续所有步骤中使用
- 用于API调用、显示确认信息、导入操作

#### Step 2: 下载模板 (StepDownloadTemplate)
**输入数据：**
- `selectedStoreId`: 用于生成特定门店的模板
- `plugin`: 插件信息用于生成对应的模板格式

**输出数据：** 无（纯展示和下载功能）

#### Step 3: 上传文件 (StepUploadFile)
**输入数据：**
- `plugin`: 用于验证文件格式和解析数据

**输出数据：**
- `uploadedFile`: 上传的文件对象
- `parsedData`: 解析后的数据结构

**需要持续传递的数据：**
- 文件数据用于后续验证和导入

#### Step 4: 验证预览 (StepValidatePreview)
**输入数据：**
- `selectedStoreId`: 门店ID用于获取商品列表进行VLOOKUP
- `parsedData`: 上传的数据用于验证
- `plugin`: 插件的验证器

**输出数据：**
- `validationResult`: 验证结果
- `validationErrors`: 错误列表
- `validationWarnings`: 警告列表

#### Step 5: 导入结果 (StepImportResult)
**输入数据：**
- `selectedStoreId`: 执行导入操作
- `parsedData`: 要导入的数据
- `validationResult`: 过滤错误数据

**输出数据：**
- `importResult`: 导入执行结果

## 🔧 改进架构设计

### 1. Pinia Store 中心化状态管理

#### 核心设计理念
```typescript
/**
 * 数据导入全局状态管理Store
 * 
 * 职责：
 * 1. 管理跨步骤的共享数据（门店信息、文件数据等）
 * 2. 提供会话级别的状态管理
 * 3. 缓存常用数据（门店列表等）
 * 4. 支持导入会话的恢复和持久化
 */
export const useDataImportStore = defineStore('dataImport', {
  // 状态定义
  // Getters计算属性
  // Actions方法
  // 持久化配置
})
```

#### 会话管理机制
```typescript
export interface ImportSession {
  // 会话标识
  sessionId: string
  pluginId: string
  plugin: DataImportPlugin | null
  
  // 当前步骤
  currentStep: number
  stepKey: ImportStep
  
  // 核心共享数据
  selectedStoreId: string
  selectedStore: VenueVO | null
  
  // 文件相关
  uploadedFile: File | null
  parsedData: ParsedData | null
  
  // 验证相关
  validationResult: ValidationResult | null
  
  // 导入相关
  importResult: ImportResult | null
  
  // 插件特定配置
  pluginConfig: Record<string, any>
}
```

### 2. 改进后的数据流转

```mermaid
graph TD
    subgraph "Pinia Store (中心化状态)"
        S1[ImportSession]
        S2[门店缓存]
        S3[文件数据]
        S4[验证结果]
        S5[导入结果]
    end
    
    A[Step 1: 选择门店] --> S1
    A --> S2
    
    B[Step 2: 下载模板] --> S1
    
    C[Step 3: 上传文件] --> S3
    C --> S1
    
    D[Step 4: 验证预览] --> S4
    D --> S1
    
    E[Step 5: 导入结果] --> S5
    E --> S1
    
    S1 --> A
    S1 --> B
    S1 --> C
    S1 --> D
    S1 --> E
    
    subgraph "持久化"
        P1[SessionStorage]
        P2[会话恢复]
    end
    
    S1 --> P1
    P1 --> P2
```

### 3. 具体改进措施

#### A. Store集成到Wizard主控制器
```typescript
// import-wizard.vue
const dataImportStore = useDataImportStore()

// 创建会话
const loadPlugin = () => {
  const pluginId = route.params.pluginId as string
  currentPlugin.value = getPlugin(pluginId)
  
  // 初始化Store并创建新会话
  dataImportStore.initialize()
  const sessionId = dataImportStore.createSession(pluginId, currentPlugin.value)
}

// 数据同步
const handleStepDataUpdate = (data: Record<string, any>) => {
  stepData.value = { ...stepData.value, ...data }
  dataImportStore.updateStepData(data) // 同步到Store
}
```

#### B. Store集成到组件层
```typescript
// store-selector/presenter.ts
export class StoreSelectorPresenter {
  private dataImportStore = useDataImportStore()
  
  selectStore: (storeId: string): void => {
    // 本地状态更新
    this.pageState.setSelectedStoreId(storeId)
    
    // 同步到全局Store
    const selectedStore = this.computed.selectedStore.value
    this.dataImportStore.setStoreInfo(storeId, selectedStore)
  }
  
  loadStores: async (): Promise<void> => {
    // 首先尝试使用缓存
    const cachedStores = this.dataImportStore.getCachedStores()
    if (cachedStores.length > 0) {
      this.pageState.setStores(cachedStores)
      return
    }
    // 否则从API加载并缓存
  }
}
```

#### C. 插件层集成
```typescript
// 库存验证组件集成Store
export class InventoryValidationPresenter {
  private dataImportStore = useDataImportStore()
  
  // 从Store获取门店信息
  private getStoreInfo() {
    return this.dataImportStore.selectedStoreInfo
  }
  
  // 验证完成后更新Store
  private updateValidationResult(result: ValidationResult) {
    this.dataImportStore.setValidationResult(result)
  }
}
```

### 4. 核心优势

#### A. 数据一致性
- **单一数据源**：所有共享数据都存储在Store中
- **自动同步**：组件状态变化自动同步到Store
- **响应式更新**：Store变化自动反映到所有相关组件

#### B. 持久化和恢复
```typescript
// 会话持久化
persist: {
  key: 'data-import-store',
  storage: sessionStorage // 浏览器会话级别持久化
}

// 会话恢复
onMounted(() => {
  if (dataImportStore.hasActiveSession) {
    // 恢复现有会话
    stepData.value = dataImportStore.getStepData()
    currentStep.value = dataImportStore.currentSession.currentStep
  }
})
```

#### C. 缓存机制
```typescript
// 门店列表缓存
cacheStores(stores: VenueVO[], expireMinutes: number = 30) {
  this.cachedStores = stores
  this.cacheExpiry = Date.now() + (expireMinutes * 60 * 1000)
}

// 智能缓存使用
if (cachedStores.length > 0 && !searchKeyword) {
  return cachedStores // 使用缓存，减少API调用
}
```

#### D. 扩展性
- **插件配置**：每个插件可以在Store中存储特定配置
- **自定义步骤**：支持插件定义自定义导入步骤
- **数据转换**：统一的数据格式转换机制

## 🚀 实施建议

### 阶段1：基础架构搭建（已完成）
- [x] 创建DataImportStore
- [x] 定义ImportSession接口
- [x] 实现基础的状态管理方法

### 阶段2：向下兼容集成
- [x] 修改import-wizard.vue集成Store
- [x] 保持现有API不变，逐步迁移
- [x] 添加Store同步机制

### 阶段3：组件层优化
- [x] store-selector组件集成缓存机制
- [ ] 各Step组件逐步集成Store
- [ ] 移除冗余的Props传递

### 阶段4：插件层深度集成
- [ ] 库存验证组件完全集成Store
- [ ] 其他插件组件跟进
- [ ] 统一数据访问接口

### 阶段5：高级功能
- [ ] 导入历史记录
- [ ] 会话分享功能
- [ ] 批量导入支持
- [ ] 实时进度同步

## 📊 性能优化

### 1. 缓存策略
- **门店列表缓存**：30分钟过期，减少重复API调用
- **插件配置缓存**：会话级别缓存
- **文件解析缓存**：避免重复解析同一文件

### 2. 内存管理
- **会话清理**：最多保持10个历史会话
- **大文件处理**：超过10MB的文件使用流式处理
- **垃圾回收**：及时清理无用的会话数据

### 3. 响应式优化
- **计算属性缓存**：合理使用computed避免重复计算
- **选择性更新**：只更新变化的数据部分
- **异步处理**：大数据量处理使用Worker

## 🔍 监控和调试

### 1. 开发工具支持
```typescript
// 开发环境下的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log(`DataImportStore: 创建新会话 ${sessionId}`)
  console.log(`DataImportStore: 更新步骤到 ${step}`)
}
```

### 2. 状态追踪
- **会话生命周期追踪**：记录会话创建、更新、销毁
- **数据变更日志**：记录关键数据的变更历史
- **错误监控**：捕获和记录Store操作错误

### 3. 性能监控
- **缓存命中率**：监控门店列表缓存的使用效果
- **API调用统计**：追踪API调用频率和响应时间
- **内存使用监控**：防止内存泄漏

## 📝 最佳实践

### 1. 数据访问模式
```typescript
// ✅ 推荐：通过Store访问共享数据
const storeInfo = dataImportStore.selectedStoreInfo
const fileInfo = dataImportStore.fileInfo

// ❌ 避免：直接访问组件内部状态
const storeId = someComponent.selectedStoreId
```

### 2. 状态更新模式
```typescript
// ✅ 推荐：使用Store的专用方法
dataImportStore.setStoreInfo(storeId, store)
dataImportStore.updateStepData(data)

// ❌ 避免：直接修改Store状态
dataImportStore.currentSession.selectedStoreId = storeId
```

### 3. 错误处理模式
```typescript
// ✅ 推荐：统一的错误处理
try {
  await dataImportStore.setValidationResult(result)
} catch (error) {
  console.error('Store更新失败:', error)
  // 回滚或重试逻辑
}
```

## 🎯 总结

通过引入Pinia Store中心化状态管理，我们解决了数据导入架构中的核心问题：

1. **数据一致性**：统一的数据源确保所有组件看到相同的数据
2. **持久化能力**：会话级别的数据持久化，提升用户体验  
3. **缓存优化**：智能缓存机制减少不必要的API调用
4. **扩展性**：插件化架构支持灵活的功能扩展
5. **可维护性**：清晰的数据流转和状态管理

这个改进架构为数据导入功能提供了坚实的基础，支持未来的功能扩展和性能优化。 