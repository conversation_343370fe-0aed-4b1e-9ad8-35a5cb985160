# 授权管理模块说明文档

## 模块布局
```txt
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台               |  |  +-------------------------+  |
|  | 门店审核             |  |  | 筛选操作区              |  |
|  | 授权管理 ← 当前      |  |  | [状态筛选] [门店搜索]   |  |
|  | 系统管理             |  |  +-------------------------+  |
|  +----------------------+  |  |                         |  |
|                            |  | 授权列表区（左60%）     |  |
|                            |  | +--------------------------------+ |  |
|                            |  | | 门店名称 | 类型 | 状态 | 操作 | |  |
|                            |  | +--------------------------------+ |  |
|                            |  | | 到期时间 | 剩余天数 | 合同信息 | |  |
|                            |  | +--------------------------------+ |  |
|                            |  |                         |  |
|                            |  | 操作区（右40%）         |  |
|                            |  | +---------------------+ |  |
|                            |  | | 授权码生成表单       | |  |
|                            |  | | 有效期天数*          | |  |
|                            |  | | 合同信息（可选）      | |  |
|                            |  | | [生成][强制失效]      | |  |
|                            |  | +---------------------+ |  |
+----------------------------+--+-------------------------+--+
```

## 核心功能表调整
| 功能分类       | 功能点                  | 业务规则                                                                 |
|----------------|-------------------------|--------------------------------------------------------------------------|
| 信息展示       | 核心状态展示            | 按以下顺序优先展示：<br>1. 已过期（红色角标）<br>2. 7天内到期（橙色闪烁）<br>3. 15天内到期（橙色）<br>4. 有效（正常展示） |
|                | 快速筛选                | 默认展示全部，支持按状态（有效/即将到期/已过期）和关键词筛选             |
| 授权操作       | 单店生成                | 每次仅针对单个门店生成新授权码                                           |
|                | 强制失效                | 立即终止当前有效授权（需二次验证）                                       |
|                | 合同关联                | 生成时可选填写合同信息，支持后期补充                                     |

## 状态管理表
| 状态         | 视觉表现                | 允许操作                                                                 | 后端状态映射规则                     |
|--------------|-------------------------|--------------------------------------------------------------------------|---------------------------------------|
| 有效         | 绿色背景+✔️图标         | 查看详情、导出PDF                                                      | status=1 AND expire_time > NOW()     |
| 即将到期     | 橙色背景+⏳图标         | 通知客户、导出清单                                                     | status=1 AND DATEDIFF(expire_time, NOW()) <= 15 |
| 已过期       | 灰色背景+⛔图标         | 重新生成授权                                                           | status=2 OR expire_time <= NOW()     |

## 业务流程
### 1. 授权生成流程（最终版）
```mermaid
graph TD
    A[选择门店] --> B{权限校验}
    B -->|商务权限| C[输入有效期天数]
    B -->|无权限| D[阻断流程]
    C --> E[填写合同信息（可选）]
    E --> F[生成预览]
    F --> G{二次验证}
    G -->|通过| H[生成授权码]
    G -->|拒绝| I[返回编辑]
```

## 需要补充的实现细节

1. **默认查询逻辑**  
   初始化时查询所有门店的最近授权状态：
   ```typescript
   // 在 viewmodel.ts 中
   const initQuery = async () => {
     const res = await queryVenues({ 
       auditStatus: 1, // 仅显示已审核通过的门店
       pageSize: 100   // 默认加载前100条
     });
     venueList.value = res.data.map(item => ({
       ...item,
       authStatus: calculateAuthStatus(item) // 根据到期时间计算显示状态
     }));
   }
   ```

2. **操作区联动逻辑**  
   点击列表项时加载详细授权信息：
   ```vue
   <el-table @row-click="handleRowClick">
     // ... 表格列定义 ...
   </el-table>

   <script setup>
   const currentVenue = ref();
   const handleRowClick = (row) => {
     currentVenue.value = row;
     loadAuthDetails(row.id); // 调用接口获取详细授权信息
   }
   </script>
   ```

3. **生成授权码表单**  
   动态显示有效期建议值：
   ```vue
   <el-form-item label="有效期天数">
     <el-input-number 
       v-model="days" 
       :min="7" 
       :max="365"
       :placeholder="`推荐 ${recommendDays} 天`"
     />
   </el-form-item>
   ```

4. **优先级排序逻辑**  
   在查询接口增加排序参数：
   ```typescript
   // 在 viewmodel.ts 中
   const getPriorityLevel = (item: VenueAuthStatusVO) => {
     if (item.auth_status === 3) return 1; // 已过期
     if (item.remain_days <= 7) return 2; // 7天内到期
     if (item.remain_days <= 15) return 3; // 15天内到期
     return 4; // 有效
   };

   // 接口调用时添加排序参数
   const fetchData = async () => {
     const res = await getAuthList({
       sortField: 'priority',
       sortOrder: 'asc',
       auditStatus: 1
     });
   };
   ```

5. **视觉强化方案**  
   在表格中增加状态标识：
   ```vue
   <el-table-column label="紧急程度" width="100">
     <template #default="{row}">
       <div v-if="row.auth_status === 3" class="priority-alert">
         <el-icon><Warning /></el-icon>需处理
       </div>
       <div v-else-if="row.remain_days <= 7" class="priority-warning">
         <el-icon><Clock /></el-icon>即将到期
       </div>
     </template>
   </el-table-column>
   ```

6. **智能筛选规则**  
   默认加载规则：
   ```typescript
   // 默认筛选条件
   const defaultFilter = {
     status: ['expired', 'expiring'], // 默认显示已过期和即将到期
     sort: 'remain_days_asc',         // 按剩余天数升序
     pageSize: 50                     // 首屏加载50条高优先级
   };
   ```

## 权限控制表
| 用户角色     | 生成授权 | 修改授权 | 导出数据 | 查看数据 |
|--------------|----------|----------|----------|----------|
| 商务人员（权限为1）     | ✓        | ✓        | ✓        | ✓        |
| 商务人员（权限为0）     | ×        | ×        | ×        | ✓        |

## 安全机制
| 安全层级     | 实施措施                          | 触发条件                     |
|--------------|-----------------------------------|------------------------------|
| 基础防护     | HTTPS传输加密                    | 所有数据请求                 |
| 操作防护     | 敏感操作二次验证                  | 生成/修改/删除操作           |
| 数据防护     | 前端数据脱敏                      | 列表展示敏感字段             |
| 审计防护     | 操作过程录像（仅记录元数据）      | 所有状态变更操作             |