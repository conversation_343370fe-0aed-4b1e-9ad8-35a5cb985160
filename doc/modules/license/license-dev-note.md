# 授权管理模块开发笔记

## 开发顺序与功能清单（VIPER架构实现）

### 一、视图层（View层）
1. [✓] 授权列表展示  
`src/modules/store/license/generate/index.vue`  
- [✓] 表格结构实现（60%宽度区域）
- [✓] 状态图标集成（角标/呼吸灯效果）
- [✓] 行点击高亮交互

2. [✓] 操作区表单布局  
`src/modules/store/license/generate/index.vue`  
- [✓] 授权码生成表单结构
- [✓] 有效期天数输入组件
- [✓] 合同信息附件上传组件

### 二、视图模型层（ViewModel层）
1. [✓] 列表状态管理  
`src/modules/store/license/generate/viewmodel.ts`  
- [✓] 当前选中门店状态
- [✓] 筛选条件响应式管理
- [✓] 排序规则维护
- [✓] 视图专用数据类型定义（VenueView/AuthStatusView）
- [✓] 辅助方法转换为计算属性

2. [✓] 表单状态管理  
`src/modules/store/license/generate/viewmodel.ts`  
- [✓] 有效期天数推荐逻辑
- [✓] 合同信息验证规则
- [✓] 操作按钮状态联动
- [✓] 表单数据类型定义（IFormData）

### 三、业务交互层（Interactor层）
1. [✓] 授权状态查询  
`src/modules/store/license/generate/interactor.ts`  
- [✓] 对接API：getAuthStatus（返回VenueAuthStatusVO）
- [✓] 处理状态映射规则：
  - [✓] 已过期：auth_status=3 || expire_time <= now
  - [✓] 7天到期：auth_status=1 && remain_days <=7
  - [✓] 15天到期：auth_status=1 && remain_days <=15

2. [✓] 授权码生成  
`src/modules/store/license/generate/interactor.ts`  
- [✓] 使用GenerateAuthReq实体作为请求参数
- [✓] 校验规则：
  - [✓] venue_ids需通过auditStatus=1的门店
  - [✓] status需符合权限等级

### 四、数据转换层（Converter层）
1. [✓] 优先级排序规则  
`src/modules/store/license/generate/converter.ts`  
- [✓] 转换规则：
  ```ts
  // 根据VenueAuthStatusVO计算优先级
  function getPriority(item: VenueAuthStatusVO): number {
    if (item.auth_status === 3) return 1
    if (item.remain_days <= 7) return 2
    if (item.remain_days <= 15) return 3
    return 4
  }
  ```

### 五、协调层（Presenter层）
1. [✓] 业务协调逻辑  
`src/modules/store/license/generate/presenter.ts`  
- [✓] 生成/失效操作的二次验证
- [✓] 生命周期管理（数据初始化/清理）
- [✓] 实现所有计算属性
- [✓] 实现所有动作方法

## 开发过程中的注意事项

### 1. VIPER架构规范
1. View层规范：
   - ✅ 避免在View中定义工具方法和辅助函数
   - ✅ 所有用户交互通过vm.actions处理
   - ✅ 所有状态访问通过vm.state和vm.computes
   - ✅ 模板中避免复杂的条件判断和计算

2. ViewModel层规范：
   - ✅ 使用视图专用的数据类型（VenueView/AuthStatusView）
   - ✅ 将View中的工具方法转换为计算属性
   - ✅ 为所有计算属性提供明确的返回类型
   - ✅ 完整定义所有状态和动作接口

3. Presenter层规范（待实现）：
   - [ ] 实现所有计算属性
   - [ ] 实现所有动作方法
   - [ ] 处理生命周期
   - [ ] 通过Converter转换数据
   - [ ] 委托业务逻辑给Interactor

### 2. 待优化项
1. 性能优化：
   - [ ] 大列表性能优化（虚拟滚动）
   - [ ] 计算属性缓存优化
   - [ ] 异步组件加载

2. 用户体验：
   - [ ] 添加操作反馈（加载状态/成功提示）
   - [ ] 优化表单验证提示
   - [ ] 添加快捷键支持

3. 代码质量：
   - [ ] 添加单元测试
   - [ ] 添加错误边界处理
   - [ ] 优化类型定义
   - [ ] 添加代码注释

## 下一步计划
1. [✓] 实现 Interactor 层的业务逻辑
2. [✓] 实现 Converter 层的数据转换
3. [✓] 实现 Presenter 层的计算属性和动作方法
4. [ ] 补充单元测试和文档

## 检查清单

### 核心功能验证
- [ ] 列表默认展示优先级排序正确（已过期置顶）
- [ ] 7/15天到期计算准确（含闰年处理）
- [ ] 生成授权码时合同信息可选填
- [ ] 强制失效操作需要短信验证
- [ ] 商务权限为0时操作按钮禁用

### 交互验证
- [ ] 点击列表行右侧操作区同步更新
- [ ] 有效期天数输入限制7-365天
- [ ] 表单未提交时切换门店弹出提示
- [ ] 批量导出时包含脱敏后的授权码

### 视觉验收
- [ ] 状态图标符合规范（颜色/图标/动画）
- [ ] 呼吸灯效果在7天到期时自动触发
- [ ] 移动端适配良好（操作区浮动布局）
- [ ] 高优先级条目有红色边框强调

### 安全验收
- [ ] 生成操作记录审计日志（含IP信息）
- [ ] 敏感接口有CSRF Token防护
- [ ] 列表数据返回时自动脱敏
- [ ] 操作失败时不清空表单数据

---

## 文件结构核对
```bash
modules/store/license/
├── generate/
│   ├── index.vue         # 主视图
│   ├── viewmodel.ts      # 状态/计算属性/actions定义
│   ├── presenter.ts      # 业务协调/生命周期
│   ├── interactor.ts     # API对接/业务规则
│   └── converter.ts      # 实体转换(entity<->viewmodel.state)
└── history/              # 历史记录模块
    └── ...               # 相同结构
```

注：conveter.ts中viewmodel.state更新为局部更新，转换方法都有带state参数。