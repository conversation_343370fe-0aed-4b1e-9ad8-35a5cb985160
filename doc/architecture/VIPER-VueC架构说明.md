# VIPER-VueC架构说明

## 架构示意说明

```
架构名称：VIPER-VueC (VIPER with Vue and Convert)
View（视图层，一个文件，index.vue）
    - Template (UI结构)
        - databinding: @UIForms
    - ComponentLogic (组件逻辑)
        - @Presenter(vue3的composable)
    - @IViewModel (UI表单模型, 单独文件)

IViewModel (视图层，IViewModel模块，状态、计算属性和actions定义，一个View对应一个文件，viewmodel.ts)
    - UI states
    - UI computed properties
    - UI actions

Presenter（协调层，继承IViewModel，一个View对应一个文件，presenter.ts）
    - @IViewModel(UI actions的实现，UI states数据的处理)
        - StateManager (状态管理)
            - reactive data（响应式数据）
            - computed properties（计算属性）
        - EventHandlers (事件处理)
    - DataConvert（数据转换）
        - @ViewModelConverter
        - @Interactor
        - @View.IViewModel
        - @Entity
    - RouterManager
        - @Router
    - LifecycleManager（生命周期管理）
        - Vue生命周期（onMounted, onUnmounted等）
        - UniApp页面生命周期（onLoad, onShow, onHide, onUnload等）
        - 数据监听（watch）
        - 页面间通信（useNavigationBackData, acceptPageData等）

ViewModelConverter (协调层，视图-模型转换模块，一个View对应一个文件，converter.ts)
    - ConversionLogic(转换逻辑)
      - createInitialState: 创建视图初始状态
      - toViewState: Entity -> ViewModel 转换
      - toEntityData: ViewModel -> Entity 转换
    - Dependencies
        - @Entity.DataModels
        - @View.IViewModel

Interactor（业务交互层，一个View对应一个文件，interactor.ts）
    - UseCases (业务用例)
    - BusinessLogic (业务逻辑)
    - @Entity.DataModels

Router（协调层，路由模块-页面切换）
    - NavigationLogic (导航逻辑)
        - 数据传递
        - 导航返回，数据回调

Entity（实体层，全局管理）
    - DataModels (数据模型)
    - ModelRelationships (模型关系)
```

## 核心架构层级

| 架构层 | 子层级 | 职责 | 依赖关系 | 文件位置 |
|--------|--------|------|----------|-----------|
| **View层** | **View** | UI展示层 | @Presenter<br>@IViewModel | `*.vue` |
| | | • Template: UI结构<br>• ComponentLogic: 组件逻辑<br>• @IViewModel: UI状态和行为定义(接口) | | |
| | **IViewModel** | UI状态和行为定义 | - | `viewModel.ts` |
| | | • UI states: UI状态定义<br>• UI computed properties: UI计算属性定义<br>• UI actions: UI动作定义<br>• 一个View对应一个IViewModel文件 | | |
| **Presenter层** | **Presenter** | 协调层（继承IViewModel） | @IViewModel<br>@ViewModelConverter<br>@Interactor<br>@Router | `presenter.ts` |
| | | • @IViewModel(UI actions的实现，UI states数据的处理)<br>• StateManager: 响应式数据管理<br>• EventHandlers: 事件处理<br>• DataConvert: 数据转换<br>• RouterManager: 路由管理<br>• LifecycleManager: 生命周期管理 | | |
| | **ViewModelConverter** | 视图-模型转换层 | @Entity<br>@View.IViewModel | `converter.ts` |
| | | • ConversionLogic: 转换逻辑 | | |
| | **Router** | 路由管理层 | - | `router/*.ts` |
| | | • NavigationLogic: 导航逻辑<br> | | |
| **Interactor层** | **Interactor** | 业务交互层 | @Entity.DataModels | `interactor.ts` |
| | | • UseCases: 业务用例<br>• BusinessLogic: 业务逻辑 | | |
| **Entity层** | **Entity** | 实体层 | - | `entity/*.ts` |
| | | • DataModels: 数据模型<br>• ModelRelationships: 模型关系 | |

## 数据流向

### 数据流向图

```mermaid
graph TD
    %% 视图层交互
    View <--> |UI事件/状态更新| Presenter
    
    %% Presenter层交互
    Presenter <--> |数据转换| ViewModelConverter
    Presenter <--> |业务操作| Interactor
    Presenter <--> |路由控制| Router
    
    %% 数据层交互
    ViewModelConverter <--> |模型转换| Entity
    Interactor <--> |模型访问| Entity
```

### 数据流向说明

1. **View层数据流**:
   - View → Presenter: 发送用户操作事件
   - Presenter → View: 返回更新后的UI状态
   - View ↔ IViewModel: 通过UI states进行数据绑定
   
2. **Presenter层数据流**:
   - Presenter → IViewModel: 实现UI actions，处理UI states
   - Presenter → ViewModelConverter: 请求数据转换
   - ViewModelConverter → Presenter: 返回转换后的视图数据
   - Presenter → Interactor: 请求业务操作
   - Interactor → Presenter: 返回业务处理结果
   - Presenter → Router: 请求路由跳转
   - Router → Presenter: 返回路由结果

3. **实体层数据流**:
   - ViewModelConverter ↔ Entity: 模型数据转换

## 目录结构

```
src/
├── pages/                              # 业务页面模块
│   └── [business_module1]/                 # 业务模块
│       ├── [page_name1]/                       # 页面1功能
│       │   ├── index.vue                           # View层
│       │   ├── viewmodel.ts                        # UI状态和行为定义(接口)
│       │   ├── presenter.ts                        # Presenter层
│       │   ├── converter.ts                        # ViewModel转换层
│       │   └── interactor.ts                       # 业务交互层
│       └── [page_name2]/                       # 页面2功能
├── router/                                 # 路由管理
├── api                                     # 通用api接口
│   ├── api1.ts                                 # 模块api接口1
│   ├── api2.ts                                 # 模块api接口2
│   └── index.ts                                # 统一导出
├── entity/                                 # 通用实体模型
│   ├── [business_module1]                      # 业务模块实体1
│   │   ├── model1.ts                               # 实体1
│   │   ├── model2.ts                               # 实体2
│   ├── model1.ts                               # 通用实体1
│   ├── model2.ts                               # 通用实体2
└── utils/                                  # 工具集
    ├── utils.ts                                # 工具
    └── navigation.ts                           # 导航封装
```

## 分层结构图

```mermaid
graph TD
    %% 定义大层级样式
    classDef majorLayer fill:#e6f3ff,stroke:#3182bd,stroke-width:2px
    classDef subLayer fill:#fff7dc,stroke:#fd8d3c,stroke-width:1px
    classDef implementation fill:#f7fcf5,stroke:#74c476,stroke-width:1px

    %% View层
    subgraph ViewLayer["View Layer"]
        View["View<br>(*.vue)"]
        IViewModel["IViewModel<br>(UI states & actions)"]
        
        View --> IViewModel
    end
    class ViewLayer majorLayer

    %% Presenter层
    subgraph PresenterLayer["Presenter Layer"]
        Presenter["Presenter<br>(继承IViewModel)"]
        VMConverter["ViewModelConverter"]
        Router["Router"]
        
        Presenter --> VMConverter
        Presenter --> Router
    end
    class PresenterLayer majorLayer

    %% Interactor层
    subgraph InteractorLayer["Interactor Layer"]
        Interactor["Interactor"]
        
    end
    class InteractorLayer majorLayer

    %% Entity层
    subgraph EntityLayer["Entity Layer"]
        Entity["Entity<br>(DataModels & Relationships)"]
    end
    class EntityLayer majorLayer

    %% 层级间的关系
    ViewLayer --> PresenterLayer
    PresenterLayer --> InteractorLayer
    PresenterLayer --> EntityLayer
    InteractorLayer --> EntityLayer

    %% 为子层级应用样式
    class View,IViewModel subLayer
    class Presenter,VMConverter,Router subLayer
    class Interactor subLayer
    class Entity subLayer
```

## 示例

以一个简单的计数器为例，展示VIPER-VueC架构的实现。这个计数器包含以下功能：
- 显示当前计数值和名称
- 计数值的增加/减少操作
- 修改计数器名称
- 保存计数器数据
- 生命周期管理示例

### 1. viewmodel.ts - 定义视图状态和行为接口

```typescript:pages/counter/viewmodel.ts
import { ComputedRef } from 'vue'

// UI状态接口
export interface ICounterState {
  count: number
  name: string
}

// UI计算属性接口
export interface ICounterComputed {
  displayText: ComputedRef<string>     // 显示文本
  isEven: ComputedRef<boolean>         // 是否为偶数
}

// UI动作接口
export interface ICounterActions {
  increment(): void                     // 增加计数
  decrement(): void                     // 减少计数
  updateName(name: string): void        // 更新名称
  save(): Promise<void>                 // 保存数据
}

// 组合接口
export interface ICounterViewModel {
  state: ICounterState
  computes: ICounterComputed
  actions: ICounterActions
}
```

### 2. presenter.ts - 实现视图逻辑、状态管理和生命周期

```typescript:pages/counter/presenter.ts
import { reactive, computed, onMounted, onUnmounted } from 'vue'
import { onLoad, onShow, onHide, onUnload } from '@dcloudio/uni-app'
import type { ICounterViewModel } from './viewmodel'
import { CounterConverter } from './converter'
import { useCounterInteractor } from './interactor'

export class CounterPresenter implements ICounterViewModel {
  private interactor = useCounterInteractor()
  
  public state = reactive(CounterConverter.createInitialState())
  
  public computes = {
    displayText: computed(() => `${this.state.name}: ${this.state.count}`),
    isEven: computed(() => this.state.count % 2 === 0)
  }
  
  constructor() {
    this.setupLifecycles()
  }

  private setupLifecycles() {
    // Vue 生命周期
    onMounted(async () => {
      console.log('组件已挂载')
      await this.loadInitialData()
    })

    onUnmounted(() => {
      console.log('组件已卸载')
      this.cleanup()
    })

    // UniApp 页面生命周期
    onLoad((options) => {
      console.log('页面加载', options)
      if (options.id) {
        this.loadCounter(options.id)
      }
    })

    onShow(() => {
      console.log('页面显示')
      // 可以在这里刷新数据
    })

    onHide(() => {
      console.log('页面隐藏')
      // 可以在这里保存临时数据
    })

    onUnload(() => {
      console.log('页面卸载')
      // 可以在这里清理资源
    })
  }

  private async loadInitialData() {
    try {
      const data = await this.interactor.getInitialData()
      CounterConverter.toViewState(this.state, data)
    } catch (error) {
      console.error('加载初始数据失败:', error)
    }
  }

  private async loadCounter(id: string) {
    try {
      const data = await this.interactor.getCounter(id)
      CounterConverter.toViewState(this.state, data)
    } catch (error) {
      console.error('加载计数器数据失败:', error)
    }
  }

  private cleanup() {
    // 清理资源，比如取消订阅、清除定时器等
  }
  
  public actions = {
    increment: () => {
      this.state.count++
    },
    decrement: () => {
      this.state.count--
    },
    updateName: (name: string) => {
      this.state.name = name
    },
    save: async () => {
      try {
        const entityData = CounterConverter.toEntityData(this.state)
        await this.interactor.saveCounter(entityData)
      } catch (error) {
        console.error('save error', error)
      }
    }
  }
}

export function useCounterPresenter(): ICounterViewModel {
  return new CounterPresenter()
}
```

### 3. converter.ts - 处理视图状态和实体数据的转换

```typescript:pages/counter/converter.ts
import type { ICounterState } from './viewmodel'
import type { CounterEntity } from '@/entity/counter'

export class CounterConverter {
  // 创建初始状态
  static createInitialState(): ICounterState {
    return {
      count: 0,
      name: '计数器'
    }
  }

  // 实体数据转换为视图状态
  static toViewState(state: ICounterState, entity: CounterEntity): void {
    state.count = entity.count
    state.name = entity.name
  }

  // 视图状态转换为实体数据
  static toEntityData(state: ICounterState): CounterEntity {
    return {
      count: state.count,
      name: state.name,
      lastUpdated: new Date().toISOString()
    }
  }
}
```

### 4. interactor.ts - 处理业务逻辑和数据操作

```typescript:pages/counter/interactor.ts
import type { CounterEntity } from '@/entity/counter'
import { saveCounterData, getCounterData } from '@/api/counter'

export class CounterInteractor {
  async getInitialData(): Promise<CounterEntity> {
    return {
      count: 0,
      name: '默认计数器',
      lastUpdated: new Date().toISOString()
    }
  }

  async getCounter(id: string): Promise<CounterEntity> {
    return await getCounterData(id)
  }

  async saveCounter(data: CounterEntity): Promise<void> {
    try {
      await saveCounterData(data)
    } catch (error) {
      throw new Error('保存计数器数据失败')
    }
  }
}

export function useCounterInteractor() {
  return new CounterInteractor()
}
```

### 5. index.vue - 视图层实现

```vue:pages/counter/index.vue
<template>
  <view class="counter">
    <!-- 显示计数信息 -->
    <text class="display">{{ vm.computes.displayText }}</text>
    <text v-if="vm.computes.isEven">当前是偶数</text>
    
    <!-- 操作按钮 -->
    <view class="actions">
      <button @tap="vm.actions.decrement">-</button>
      <button @tap="vm.actions.increment">+</button>
    </view>
    
    <!-- 名称输入 -->
    <input 
      v-model="vm.state.name"
      placeholder="输入计数器名称"
    />
    
    <!-- 保存按钮 -->
    <button @tap="vm.actions.save">保存</button>
  </view>
</template>

<script setup lang="ts">
import { useCounterPresenter } from './presenter'
import type { ICounterViewModel } from './viewmodel'

const vm: ICounterViewModel = useCounterPresenter()
</script>
```

### 文件职责说明：

1. **viewmodel.ts**
   - 定义UI状态接口(ICounterState)
   - 定义UI计算属性接口(ICounterComputed)
   - 定义UI动作接口(ICounterActions)
   - 组合以上接口为视图模型接口(ICounterViewModel)

2. **presenter.ts**
   - 实现ICounterViewModel接口
   - 管理响应式状态
   - 实现业务逻辑和动作处理
   - 管理组件和页面生命周期
   - 处理数据加载和清理
   - 错误处理和加载状态管理

3. **converter.ts**
   - 创建视图初始状态
   - 处理视图状态和实体数据之间的转换
   - 确保数据格式的一致性

4. **interactor.ts**
   - 实现核心业务逻辑
   - 处理数据持久化
   - 封装API调用
   - 提供数据服务

5. **index.vue**
   - 实现UI界面结构
   - 使用视图模型进行数据绑定
   - 触发用户交互事件
   - 展示加载状态

这个简单的计数器示例展示了VIPER-VueC架构的核心概念和各层的职责分工，完整地展示了架构的主要特点：
- 清晰的职责分离
- 清晰的生命周期管理
- 数据初始化流程
- 类型安全的接口定义
- 统一的状态管理
- 数据转换的规范化
- 业务逻辑的独立性