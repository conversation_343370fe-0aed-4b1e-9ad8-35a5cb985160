# VIPER-VueC 架构检查清单

## 一、架构职责划分

### 1. View 层检查项
- [ ] 是否只负责 UI 渲染和用户交互
- [ ] 是否通过 props/emit 与外部通信
- [ ] 是否避免在 View 中直接处理业务逻辑
- [ ] 是否正确使用 ViewModel 定义的接口
- [ ] 是否避免直接调用 Interactor 或 Converter
- [ ] 是否使用 Element Plus + Tailwind 规范布局
- [ ] 是否处理了加载状态和错误提示
- [ ] 是否实现了响应式布局
- [ ] 是否避免在 View 中定义工具方法和辅助函数
- [ ] 是否所有的方法调用都通过 vm.actions 进行
- [ ] 是否所有的状态访问都通过 vm.state 和 vm.computes 进行

### 2. ViewModel 层检查项
- [ ] 是否完整定义了所有 UI 状态接口
- [ ] 是否定义了所有用户交互行为接口
- [ ] 是否定义了所有计算属性接口
- [ ] 是否使用 TypeScript 类型来增强接口定义
- [ ] 是否避免在接口中包含业务逻辑
- [ ] 是否定义了表单验证规则接口
- [ ] 是否定义了错误状态接口
- [ ] 是否使用规范的接口命名（I[ModuleName]State/Computed/Actions）
- [ ] 是否将 View 中的工具方法定义为计算属性
- [ ] 是否避免使用服务端实体类型，而是定义视图专用的数据类型
- [ ] 是否为所有的计算属性提供了明确的返回类型

### 3. Presenter 层检查项
- [ ] 是否作为 View 和 Interactor 的协调者
- [ ] 是否正确处理生命周期事件
- [ ] 是否通过 Converter 转换数据
- [ ] 是否避免直接操作 DOM
- [ ] 是否正确管理视图状态
- [ ] 是否将所有业务逻辑委托给 Interactor
- [ ] 是否只负责视图层面的路由跳转
- [ ] 是否正确处理错误展示
- [ ] 是否实现了表单验证逻辑
- [ ] 是否处理了加载状态
- [ ] 是否正确初始化和清理资源
- [ ] 是否实现了 ViewModel 中定义的所有计算属性
- [ ] 是否实现了 ViewModel 中定义的所有动作方法

### 4. Interactor 层检查项
- [ ] 是否集中处理所有业务逻辑
- [ ] 是否负责数据的获取和存储
- [ ] 是否处理状态管理（如 Pinia Store）
- [ ] 是否处理所有 API 调用
- [ ] 是否正确处理异步操作
- [ ] 是否处理所有业务错误
- [ ] 是否提供统一的错误处理机制
- [ ] 是否避免包含 UI 相关逻辑
- [ ] 是否实现了业务安全机制
- [ ] 是否处理了数据持久化
- [ ] 是否正确处理业务状态转换

### 5. Converter 层检查项
- [ ] 是否只负责数据转换
- [ ] 是否提供 DTO 到 ViewModel 的转换
- [ ] 是否提供 ViewModel 到 DTO 的转换
- [ ] 是否避免包含业务逻辑
- [ ] 是否使用纯函数进行转换
- [ ] 是否处理了数据默认值和数据类型安全
- [ ] 是否使用规范的方法命名（createInitialState/toViewStateFromXXX/toXXX）

## 二、常见问题检查

### 1. 数据流向问题
- [ ] 是否遵循单向数据流
- [ ] 是否避免跨层级调用和循环依赖
- [ ] 是否正确处理状态更新、错误数据流和表单数据流
- [ ] 是否处理好异步数据流

### 2. 生命周期管理
- [ ] 是否在正确的生命周期处理初始化
- [ ] 是否正确清理资源
- [ ] 是否处理组件卸载
- [ ] 是否正确处理异步操作的取消和数据加载时机
- [ ] 是否处理好路由切换时的状态和组件重用场景

### 3. 状态管理问题
- [ ] 业务状态是否由 Interactor 统一管理
- [ ] 视图状态是否由 Presenter 管理
- [ ] 是否正确处理全局状态和状态持久化
- [ ] 是否处理好状态同步问题
- [ ] 是否避免状态冗余
- [ ] 是否正确使用计算属性

### 4. 错误处理问题
- [ ] 是否在 Interactor 中统一处理业务错误
- [ ] 是否在 Presenter 中处理视图相关错误
- [ ] 是否提供友好的错误提示
- [ ] 是否正确处理异步错误
- [ ] 是否实现了错误恢复机制
- [ ] 是否处理好错误边界情况

## 三、代码规范检查

### 1. 命名规范
- [ ] 是否使用统一的命名约定
- [ ] 是否使用有意义的变量名
- [ ] 是否使用正确的文件、接口命名
- [ ] 是否使用规范的方法命名
- [ ] 是否使用规范的类型命名
- [ ] 是否使用规范的常量命名

### 2. 文件组织
- [ ] 是否按照 VIPER 结构组织文件
- [ ] 是否将相关文件放在同一目录
- [ ] 是否正确管理导入导出
- [ ] 是否合理拆分文件
- [ ] 是否正确组织类型定义文件
- [ ] 是否正确组织常量文件
- [ ] 是否正确组织工具函数文件

### 3. 类型定义
- [ ] 是否正确使用 TypeScript 类型
- [ ] 是否定义所有必要的接口
- [ ] 是否避免使用 any

## 四、VIPER-VueC 最佳实践

### 1. 分层职责最佳实践

#### View 层最佳实践
- [ ] 保持 template 纯展示，避免在模板中写复杂的表达式
- [ ] 使用 `vm.state`、`vm.computes`、`vm.actions` 标准结构访问视图模型
- [ ] 组件属性和事件命名要语义化，使用 `v-bind` 和 `@event` 标准语法
- [ ] 复杂组件应拆分为子组件，保持单一职责
- [ ] 样式使用 scoped 或 module 方式隔离

#### ViewModel 层最佳实践
- [ ] 接口命名规范：`I[ModuleName]ViewModel`
- [ ] 状态接口命名规范：`I[ModuleName]State`
- [ ] 计算属性接口命名规范：`I[ModuleName]Computed`
- [ ] 动作接口命名规范：`I[ModuleName]Actions`
- [ ] 每个动作方法都要有明确的返回类型定义

#### Presenter 层最佳实践
- [ ] 使用 `use[ModuleName]Presenter` 命名规范
- [ ] 在构造函数中初始化依赖（Interactor、Router等）
- [ ] 使用 `setupLifecycles` 方法统一管理生命周期
- [ ] 错误处理采用 try-catch 并转换为用户友好的提示
- [ ] 异步操作要处理加载状态和错误状态

#### Interactor 层最佳实践
- [ ] 使用 class 实现，方便依赖注入和单元测试
- [ ] 方法名要体现业务动作，如 `handleLogin`、`processOrder`
- [ ] 统一错误处理和错误转换
- [ ] 处理好异步操作的取消和超时
- [ ] 合理使用 private/protected/public 访问修饰符

#### Converter 层最佳实践
- [ ] 只包含纯函数，不含业务逻辑
- [ ] 使用静态方法实现转换功能
- [ ] 标准方法命名：`createInitialState`、`toViewState(state, fromObject)`、`toXXVO`、`toXXStorage`等
- [ ] 转换方法要处理空值和异常情况
- [ ] 使用类型断言确保类型安全

### 2. 数据流转最佳实践

#### 状态管理
- [ ] 全局状态统一使用 Pinia Store 管理
- [ ] 组件状态在 Presenter 中使用 reactive 管理
- [ ] 临时状态使用 ref/reactive 在组件内管理
- [ ] 使用 computed 处理派生状态
- [ ] watch 监听关键状态变化并处理副作用

#### 数据转换
- [ ] Entity -> ViewModel：在数据加载时转换
- [ ] ViewModel -> Entity：在数据提交时转换
- [ ] 转换时处理数据格式化和默认值
- [ ] 转换时进行数据校验
- [ ] 转换失败时提供错误处理

### 3. 生命周期管理最佳实践

#### 初始化流程
```typescript
export class SomePresenter {
    constructor() {
        this.setupLifecycles()
        this.setupWatchers()
        this.setupSubscriptions()
    }

    private setupLifecycles() {
        onMounted(() => {
            this.loadInitialData()
        })

        onUnmounted(() => {
            this.cleanup()
        })
    }

    private setupWatchers() {
        watch(() => this.state.someData, this.handleDataChange)
    }

    private async loadInitialData() {
        try {
            this.state.loading = true
            const data = await this.interactor.getInitialData()
            this.converter.toViewState(this.state, data)
        } catch (error) {
            this.handleError(error)
        } finally {
            this.state.loading = false
        }
    }
}
```

## 五、当前项目存在的问题

1. Presenter 层问题
   - 部分业务逻辑未委托给 Interactor 处理
   - 错误处理职责不清晰
   - 需要明确视图状态管理职责

2. Interactor 层问题
   - 业务逻辑不够集中，需要将所有业务逻辑集中到此层
   - 缺少统一的错误处理机制
   - 需要加强与 Store 的集成

3. Converter 层问题
   - 包含了一些业务逻辑，应该移到 Interactor
   - 转换方法不够纯粹，需要确保是纯函数

4. ViewModel 层问题
   - 接口定义不够完整
   - 类型定义不够严格

## 六、改进建议

1. 架构调整
   ```typescript
   // Presenter 负责协调和视图状态
   export function useLoginPresenter(): ILoginViewModel {
       const interactor = useLoginInteractor()
       const router = useRouter()
       const state = reactive(LoginConverter.createViewModelState())
       
       return {
           state,
           computes: createComputed(state, interactor),
           actions: {
               async handleSubmit(form: LoginForm) {
                   // 委托业务逻辑给 Interactor
                   const result = await interactor.handleLogin(
                       LoginConverter.toDTO(form)
                   )
                   
                   // Presenter 处理成功后的路由跳转
                   if (result.success) {
                       router.push('/')
                   }
               }
           }
       }
   }

   // Interactor 集中处理所有业务逻辑
   export class LoginInteractor {
       constructor(private store: AuthStore) {}
       
       async handleLogin(loginDto: LoginDto): Promise<Result> {
           try {
               const response = await this.login(loginDto)
               this.handleLoginSuccess(response)
               return { success: true }
           } catch (error) {
               return this.handleError(error)
           }
       }
       
       private handleLoginSuccess(response: LoginResponse) {
           // 处理登录成功的所有业务逻辑
           this.store.setToken(response.token)
           this.store.setUserInfo(response.userInfo)
       }
       
       private handleError(error: Error): ErrorResult {
           // 统一的错误处理逻辑
           return {
               success: false,
               error: this.formatError(error)
           }
       }
   }

   // Converter 只做纯粹的转换
   export class LoginConverter {
       static toDTO(form: LoginForm): LoginDto {
           return {
               mobile: form.mobile,
               password: form.password
           }
       }
   }
   ```

2. 状态管理优化
   - 业务状态统一在 Interactor 中管理
   - 视图状态由 Presenter 管理
   - 通过 Converter 处理数据转换

3. 错误处理优化
   - 业务错误统一在 Interactor 中处理
   - 视图相关错误在 Presenter 中处理
   - 提供统一的错误处理机制 

## 七、常见错误和注意事项

### 1. View 层常见错误
- [ ] 在 View 中直接定义工具方法和辅助函数
- [ ] 在 View 中直接处理数据转换和格式化
- [ ] 在 View 中直接处理业务逻辑
- [ ] 在模板中编写复杂的条件判断和计算逻辑
- [ ] 未使用 vm.actions 处理用户交互
- [ ] 未使用 vm.computes 处理派生数据
- [ ] 在 template 中直接访问 interactor 或 converter
- [ ] 在 template 中直接调用 API
- [ ] 在 template 中直接进行复杂的数据转换

### 2. ViewModel 层常见错误
- [ ] 直接使用服务端实体类型而不是视图专用类型
- [ ] 在接口中包含实现细节
- [ ] 计算属性返回类型不明确
- [ ] 未将 View 中的工具方法定义为计算属性
- [ ] 状态定义不完整或冗余
- [ ] 在 ViewModel 中引入 Interactor 或 Converter 的依赖
- [ ] 在接口定义中包含具体的业务逻辑
- [ ] 未定义完整的表单验证规则接口

### 3. Presenter 层常见错误
- [ ] 直接处理业务逻辑而不是委托给 Interactor
- [ ] 未正确处理计算属性的实现
- [ ] 未处理异步操作的加载状态
- [ ] 未处理错误状态和提示
- [ ] 生命周期处理不完整
- [ ] 在 Presenter 中直接调用 API 而不是通过 Interactor
- [ ] 未处理组件卸载时的资源清理
- [ ] 未正确处理异步操作的取消

### 4. Interactor 层常见错误
- [ ] 包含视图相关的逻辑
- [ ] 直接处理 UI 状态
- [ ] 未统一处理错误
- [ ] 未处理异步操作的取消
- [ ] 业务逻辑不够内聚
- [ ] 直接依赖 Converter 层（应该独立处理业务逻辑）
- [ ] 未处理并发请求的竞态条件
- [ ] 未实现正确的错误恢复机制

### 5. Converter 层常见错误
- [ ] 包含业务逻辑
- [ ] 直接调用 API
- [ ] 处理异步操作
- [ ] 维护状态
- [ ] 未处理数据转换的边界情况
- [ ] 在转换方法中包含副作用
- [ ] 未提供默认值处理
- [ ] 转换方法不是纯函数

### 6. 架构依赖错误
- [ ] Interactor 依赖 Converter（应该独立处理业务逻辑）
- [ ] View 直接依赖 Interactor（应该通过 Presenter）
- [ ] Converter 依赖 Interactor（应该只做纯数据转换）
- [ ] Presenter 直接调用 API（应该通过 Interactor）
- [ ] View 直接使用实体类型（应该使用 ViewModel 类型）

### 7. 生命周期处理错误
- [ ] 未在正确的生命周期初始化数据
- [ ] 未处理组件卸载时的清理工作
- [ ] 未取消进行中的异步操作
- [ ] 未处理路由切换时的状态保存
- [ ] 未处理组件重用时的状态重置

### 8. 状态管理错误
- [ ] 在多个层级维护重复状态
- [ ] 未使用响应式状态管理
- [ ] 状态更新不同步
- [ ] 未处理状态初始化
- [ ] 未处理状态重置
- [ ] 未处理状态持久化

### 9. 最佳实践建议
1. 状态管理最佳实践：
   ```typescript
   // Presenter 中的状态管理
   export class SomePresenter {
     private state = reactive({
       // 使用 Converter 创建初始状态
       ...SomeConverter.createInitialState(),
       // 本地UI状态
       loading: false,
       error: null
     })

     // 状态更新方法
     private updateState(newState: Partial<typeof this.state>) {
       Object.assign(this.state, newState)
     }

     // 异步操作状态管理
     private async loadData() {
       try {
         this.state.loading = true
         const data = await this.interactor.getData()
         SomeConverter.toViewState(this.state, data)
       } catch (error) {
         this.state.error = error
       } finally {
         this.state.loading = false
       }
     }
   }
   ```

2. 生命周期管理最佳实践：
   ```typescript
   export class SomePresenter {
     private abortController = new AbortController()

     constructor() {
       this.setupLifecycles()
     }

     private setupLifecycles() {
       onMounted(() => {
         this.initialize()
       })

       onBeforeUnmount(() => {
         this.cleanup()
       })

       onActivated(() => {
         this.resume()
       })

       onDeactivated(() => {
         this.pause()
       })
     }

     private cleanup() {
       this.abortController.abort()
       // 清理其他资源
     }
   }
   ```

3. 错误处理最佳实践：
   ```typescript
   export class SomePresenter {
     private async handleAsyncAction() {
       try {
         this.state.loading = true
         await this.interactor.someAction()
       } catch (error) {
         // 统一错误处理
         this.handleError(error)
       } finally {
         this.state.loading = false
       }
     }

     private handleError(error: unknown) {
       if (error instanceof BusinessError) {
         ElMessage.error(error.message)
       } else {
         ElMessage.error('操作失败，请稍后重试')
         console.error(error)
       }
     }
   }
   ```
