# 商务授权后台管理系统工程说明

## 全局布局规范
```txt
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 模块导航菜单         |  |  +-------------------------+  |
|  |                      |  |  | 模块功能区域            |  |
|  |                      |  |  |                         |  |
|  |                      |  |  |                         |  |
|  +----------------------+  |  |                         |  |
+----------------------------+--+-------------------------+--+

设计原则：
1. 响应式布局：侧边栏宽度固定宽度，主内容区自适应
2. 视觉层次：采用Z型视觉动线（LOGO→导航→操作栏→内容）
3. 状态指示：当前选中菜单高亮显示（蓝色边框+底色）
4. 信息密度：主内容区保持每屏3-5个核心信息区块
5. 操作热区：高频操作按钮固定在右下角浮动区域
```

## 技术栈
- 前端框架: Vue 3 + Composition API + TypeScript
- 架构模式: VIPER-VueC
- 构建工具: Vite 5
- UI 组件库: Element Plus 2.8
- 状态管理: Pinia
- 路由: Vue Router 4
- 样式方案: Tailwind CSS 3 + SCSS 模块
- 图标库: Element Plus Icons
- 代码规范: ESLint + Prettier + 架构分层规范

## 工程结构
```bash
src/
├── main.ts
│
├── modules/                  # 业务模块（每个模块独立VIPER结构）
│   ├── auth/                 # 认证模块（VIPER实现）
│   │   ├── login/            # 登录功能
│   │   │   ├── index.vue     # View层
│   │   │   ├── viewmodel.ts  # UI状态/computes/actions接口
│   │   │   ├── presenter.ts  # 协调层
│   │   │   ├── interactor.ts # 业务交互
│   │   │   └── converter.ts  # 数据转换
│   │   └── profile/          # 个人中心（相同结构）
│   │
│   ├── dashboard/            # 控制台模块（VIPER实现）
│   │   ├── components/       # 图表组件（常规组件）
│   │   ├── index.vue         # 视图入口
│   │   └── ...               # VIPER架构文件
│   │
│   ├── store/                # 门店管理（VIPER实现）
│   │   ├── audit/            # 审核子模块
│   │   │   ├── list/         # 审核列表（完整VIPER结构）
│   │   │   │   ├── index.vue     # View层（卡片列表布局）
│   │   │   │   ├── viewmodel.ts  # UI状态/computes/actions接口
│   │   │   │   ├── presenter.ts  # 协调层
│   │   │   │   ├── interactor.ts # API对接
│   │   │   │   └── converter.ts # 数据转换
│   │   │   └── detail/       # 审核详情
│   │   │       ├── index.vue     # View层（信息展示+操作区）
│   │   │       ├── viewmodel.ts  # UI状态/computes/actions接口
│   │   │       ├── ...
│   │   │       └── converter.ts  # 数据转换
│   │   ├── license/          # 授权管理子模块
│   │   │   ├── generate/     # 授权生成
│   │   │   └── history/      # 授权历史
│   │   └── list/             # 门店列表
│   │
│   └── system/               # 系统管理（VIPER实现）
│
├── shared/                   # 共享资源
│   ├── types/                # 前端全局类型定义
│   ├── components/           # 全局通用组件
│   ├── store/                # 持久化存储
│   └── utils/                # 工具类库
├── router/                   # 路由配置
│   └── index.ts
│
├── entities/                 # 服务端实体类型定义（服务端实体：DTO、VO）
│   ├── auth/                 # 认证模块实体
│   ├── dashboard/            # 控制台模块实体
│   ├── store/                # 门店管理实体
│   ├── license/              # 授权管理实体
│   └── system/               # 系统管理实体
│
└── assets/                   # 静态资源
```

## 核心功能模块

### 1. 认证模块（modules/auth）
- **View层 (index.vue)**
  - 登录页面布局与交互
  - 密码修改表单验证
- **ViewModel层**
  - 定义登录状态接口
  - 密码强度计算逻辑
- **Presenter层**
  - 协调登录流程
  - 处理鉴权失败锁定逻辑
- **Interactor层**
  - 对接认证API
  - Token管理
- **Converter层**
  - 登录表单DTO转换
  - 用户信息实体转换

### 2. 控制台模块（modules/dashboard）
```txt
---------------------------------------------
          控制台模块 完整页面布局           
---------------------------------------------
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台 ← 当前        |  |  +-------------------------+  |
|  | 门店审核             |  |  | 数据看板区域            |  |
|  | 授权管理             |  |  | +-----+ +-----+ +-----+ |  |
|  | 系统管理             |  |  | |统计| |统计| |图表| |  |
|  +----------------------+  |  | +-----+ +-----+ +-----+ |  |
|                            |  |                         |  |
|                            |  | 快捷操作区域            |  |
|                            |  | +-----+ +-----+ +-----+ |  |
|                            |  | |生成| |批量| |导出| |  |
|                            |  | +-----+ +-----+ +-----+ |  |
+----------------------------+--+-------------------------+--+

核心功能：
- 实时数据看板（待审核/即将到期/异常门店）
- 快捷入口（高频操作一键直达）
- 数据可视化（折线图/柱状图/饼图）
- 全局状态监控（API健康检查）
- 消息中心集成（审核通知/系统警报）
```

### 3. 门店审核模块（modules/store/audit）
```txt
---------------------------------------------
          门店审核模块 完整页面布局           
---------------------------------------------
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台               |  |  +-------------------------+  |
|  | 门店审核 ← 当前      |  |  | 筛选操作栏              |  |
|  | 授权管理             |  |  | [时间] [状态] [搜索]    |  |
|  | 系统管理             |  |  |                         |  |
|  +----------------------+  |  | 卡片列表（3列自适应）   |  |
|                            |  | +-----+ +-----+ +-----+ |  |
|                            |  | |门店1| |门店2| |门店3| |  |
|                            |  | +-----+ +-----+ +-----+ |  |
|                            |  |                         |  |
|                            |  | 分页控制区域            |  |
|                            |  | [页码] [数量] [总数]    |  |
+----------------------------+--+-------------------------+--+

核心功能：
- 多维度筛选（时间/状态/区域/关键词）
- 卡片式信息展示（关键信息一眼可视）
- 批量审核操作（支持多选+模板备注）
- 实时数据更新（WebSocket推送）
- 审核留痕（操作记录不可删除）
```

### 4. 授权管理模块（modules/store/license）
```txt
---------------------------------------------
          授权管理模块 完整页面布局           
---------------------------------------------
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台               |  |  +-------------------------+  |
|  | 门店审核             |  |  | 筛选操作区              |  |
|  | 授权管理 ← 当前      |  |  | [状态筛选] [门店搜索]   |  |
|  | 系统管理             |  |  +-------------------------+  |
|  +----------------------+  |  |                         |  |
|                            |  | 授权列表区（左60%）     |  |
|                            |  | +--------------------------------+ |  |
|                            |  | | 门店名称 | 类型 | 状态 | 操作 | |  |
|                            |  | +--------------------------------+ |  |
|                            |  | | 到期时间 | 剩余天数 | 合同信息 | |  |
|                            |  | +--------------------------------+ |  |
|                            |  |                         |  |
|                            |  | 操作区（右40%）         |  |
|                            |  | +---------------------+ |  |
|                            |  | | 授权码生成表单       | |  |
|                            |  | | 有效期天数*          | |  |
|                            |  | | 合同信息（可选）      | |  |
|                            |  | | [生成][强制失效]      | |  |
|                            |  | +---------------------+ |  |
+----------------------------+--+-------------------------+--+

核心功能：
- 智能优先级排序（已过期＞7天到期＞15天到期＞有效）
- 状态可视化方案（角标/呼吸灯/颜色编码）
- 单店精准操作（生成/失效/续期）
- 合同信息追溯（与授权码绑定存储）
- 安全操作防护（高危操作二次验证）
```

### 5. 系统管理模块（modules/system）
```txt
---------------------------------------------
          系统管理模块 完整页面布局           
---------------------------------------------
+----------------------------+-------------------------------+
|         侧边导航栏         |           主内容区            |
|  +----------------------+  |  +-------------------------+  |
|  |      系统LOGO        |  |  | 全局操作栏              |  |
|  +----------------------+  |  | [用户信息] [通知] [帮助]|  |
|  | 控制台               |  |  +-------------------------+  |
|  | 门店审核             |  |  | 用户管理表格            |  |
|  | 授权管理             |  |  | +---------------------+ |  |
|  | 系统管理 ← 当前      |  |  | | 账号 | 状态 | 操作   | |  |
|  +----------------------+  |  | +---------------------+ |  |
|                            |  |                         |  |
|                            |  | 操作日志区域            |  |
|                            |  | +---------------------+ |  |
|                            |  | | 时间 | 操作 | 详情   | |  |
|                            |  | +---------------------+ |  |
+----------------------------+--+-------------------------+--+

核心功能：
- 角色权限管理（菜单级/操作级权限控制）
- 操作日志审计（支持时间范围筛选）
- 系统参数配置（有效期规则/通知模板）
- 数据字典管理（状态枚举值维护）
- 系统健康监测（API响应时间监控）
```

## VIPER架构规范
1. **层级职责**
```markdown
| 层级        | 职责                          | 对应文件           |
|-------------|-------------------------------|--------------------|
| View        | 模板渲染/基础交互              | index.vue          |
| ViewModel   | UI状态/行为接口定义            | viewmodel.ts       |
| Presenter   | 业务逻辑协调/状态管理           | presenter.ts       |
| Interactor  | 数据获取/外部服务交互           | interactor.ts      |
| Converter   | 数据格式转换/实体映射           | converter.ts       |
```

2. **依赖规则**
- View层只能调用Presenter提供的方法
- Presenter通过Interactor获取数据
- 所有数据转换必须经过Converter
- 实体定义在shared/entities统一管理

3. **路由配置**
src/shared/router/index.ts

## 开发指南
1. **VIPER开发流程**
```mermaid
graph TD
    A[实现View的UI布局] --> B[定义ViewModel接口]
    B --> C[编写Interactor业务逻辑]
    C --> D[创建Converter转换器]
    D --> E[实现Presenter]
    E --> F[路由注册]
```

2. **代码校验规则**
- View层禁止出现业务逻辑
- Interactor层保持纯函数特性

## 一、账号管理功能

### 1.1 登录功能
1. **登录页面布局**
   - PC横版布局设计
   - 左侧品牌展示区（60%宽度）
     * 背景图
     * Logo
     * 品牌标语
   - 右侧登录表单区（40%宽度）
     * 系统名称
     * 登录表单
     * 底部版权信息

2. **登录方式**
   - 手机号 + 密码登录
   - 记住密码功能
   - 登录状态保持（24小时）

3. **安全机制**
   - 连续5次登录失败自动锁定（5分钟）
   - 密码加密传输
   - Token认证机制
   - 自动登出（Token过期）

4. **权限控制**
   - 完全权限（status=1 & permission_level=1）
   - 基础权限（status=1 & permission_level=0）
   - 无权限（status=0/2）

### 1.2 状态管理
1. **账号状态**
   - 待审核（status=0）
   - 正常（status=1）
   - 禁用（status=2）

2. **权限等级**
   - 无权限（permission_level=0）
   - 完全权限（permission_level=1）

3. **登录状态**
   - Token管理
   - 登录状态持久化
   - 状态同步机制

### 1.3 安全设计
1. **密码安全**
   - 密码强度校验
   - 密码加密传输

2. **登录保护**
   - 登录失败计数
   - 自动锁定机制
   - 解锁倒计时

3. **数据安全**
   - Token加密存储
   - 敏感信息脱敏
   - XSS/CSRF防护
```

## 工程结构更新
```bash
src/modules/store/license/
├── generate/            # 授权生成（完整VIPER结构）
│   ├── index.vue       # View层
│   ├── viewmodel.ts    # UI状态/computes/actions接口
│   ├── presenter.ts    # 协调层
│   ├── interactor.ts   # API对接
│   └── converter.ts    # 数据转换（entites<->viewmodel）
└── history/            # 授权历史（完整VIPER结构）
    ├── index.vue       # 列表视图
    ├── viewmodel.ts    # UI状态/computes/actions接口
    ├── presenter.ts    # 协调层
    ├── interactor.ts   # API对接
    └── converter.ts    # 数据转换（entites<->viewmodel）
```