# 📝 版本管理系统 API 文档

## 一、版本管理工具 API 文档（管理端使用）

### 🔧 **通用响应格式**
```json
{
  "code": 200,              // 状态码：200成功，其他失败
  "message": "success",     // 响应消息
  "data": {}               // 响应数据
}
```

### 🔧 **分页响应格式**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNum": 1,           // 当前页码
    "pageSize": 10,         // 每页大小
    "total": 100,           // 总记录数
    "data": []              // 数据列表
  }
}
```

---

## 📱 **1. APP版本管理接口**

### **1.1 创建APP版本**

**接口地址：** `POST /api/app-upgrade/create`

**请求参数：**
```json
{
  "clientType": "android",                    // 客户端类型：android/ios
  "versionCode": 1001,                        // 版本号（用于比较，必须是整数）
  "versionName": "1.0.1",                     // 版本名称（显示给用户）
  "upgradeTitle": "新版本发布",                // 升级标题
  "upgradeContent": "1.修复已知问题\n2.性能优化", // 升级内容描述
  "downloadUrl": "https://example.com/app-v1.0.1.apk", // APK下载地址
  "fileSize": 52428800,                       // 文件大小（字节）
  "fileMd5": "d41d8cd98f00b204e9800998ecf8427e", // 文件MD5校验值
  "forceUpgrade": false                       // 是否强制升级
}
```

**字段说明：**
- `clientType`: 必填，支持 "android", "ios"
- `versionCode`: 必填，用于版本比较的数字，新版本必须大于旧版本
- `versionName`: 必填，用户看到的版本号，如 "1.0.1"
- `upgradeTitle`: 可选，升级弹窗标题
- `upgradeContent`: 可选，升级内容说明，支持换行
- `downloadUrl`: 必填，APK/IPA文件下载地址
- `fileSize`: 可选，文件大小（字节）
- `fileMd5`: 可选，文件MD5值，用于完整性校验
- `forceUpgrade`: 必填，是否强制升级

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "1234567890123456",                 // 版本ID
    "clientType": "android",
    "versionCode": 1001,
    "versionName": "1.0.1",
    "upgradeTitle": "新版本发布",
    "upgradeContent": "1.修复已知问题\n2.性能优化",
    "downloadUrl": "https://example.com/app-v1.0.1.apk",
    "fileSize": 52428800,
    "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
    "forceUpgrade": false,
    "createTime": 1698123456789,              // 创建时间戳（毫秒）
    "updateTime": 1698123456789               // 更新时间戳（毫秒）
  }
}
```

### **1.2 更新APP版本**

**接口地址：** `POST /api/app-upgrade/update`

**请求参数：**
```json
{
  "id": "1234567890123456",                   // 要更新的版本ID（必填）
  "clientType": "android",
  "versionCode": 1001,
  "versionName": "1.0.1",
  "upgradeTitle": "新版本发布",
  "upgradeContent": "1.修复已知问题\n2.性能优化",
  "downloadUrl": "https://example.com/app-v1.0.1.apk",
  "fileSize": 52428800,
  "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
  "forceUpgrade": false
}
```

**响应示例：** 同创建接口

### **1.3 查询APP版本列表**

**接口地址：** `POST /api/app-upgrade/list`

**请求参数：**
```json
{
  "clientType": "android",                    // 客户端类型（必填）
  "pageNum": 1,                              // 页码（必填，从1开始）
  "pageSize": 10                             // 每页大小（必填）
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 5,
    "data": [
      {
        "id": "1234567890123456",
        "clientType": "android",
        "versionCode": 1001,
        "versionName": "1.0.1",
        "upgradeTitle": "新版本发布",
        "upgradeContent": "1.修复已知问题\n2.性能优化",
        "downloadUrl": "https://example.com/app-v1.0.1.apk",
        "fileSize": 52428800,
        "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
        "forceUpgrade": false,
        "createTime": 1698123456789,
        "updateTime": 1698123456789
      }
      // ... 更多版本数据
    ]
  }
}
```

---

## 🌐 **2. H5版本管理接口**

### **2.1 创建H5版本**

**接口地址：** `POST /api/app-h5-version/create`

**请求参数：**
```json
{
  "appUpgradeId": "1234567890123456",         // 关联的APP版本ID（必填）
  "h5Tag": "v1.0.1.2025",                    // H5版本标签（必填）
  "h5Url": "https://merp.dev.com/v1.0.1.2025/auth?deviceId=999", // H5链接地址（必填）
  "description": "新功能页面更新",              // 版本描述（可选）
  "isActive": true                           // 是否激活（必填）
}
```

**字段说明：**
- `appUpgradeId`: 必填，关联的APP版本ID
- `h5Tag`: 必填，H5版本标签，建议格式：v主版本.次版本.修订版本.年份
- `h5Url`: 必填，H5页面完整URL地址
- `description`: 可选，版本描述说明
- `isActive`: 必填，是否激活（同一个APP版本下只能有一个激活的H5版本）

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": "h5_1234567890123456",              // H5版本ID
    "appUpgradeId": "1234567890123456",
    "h5Tag": "v1.0.1.2025",
    "h5Url": "https://merp.dev.com/v1.0.1.2025/auth?deviceId=999",
    "description": "新功能页面更新",
    "isActive": true,
    "createTime": 1698123456789,
    "updateTime": 1698123456789
  }
}
```

### **2.2 更新H5版本**

**接口地址：** `POST /api/app-h5-version/update`

**请求参数：**
```json
{
  "id": "h5_1234567890123456",               // 要更新的H5版本ID（必填）
  "appUpgradeId": "1234567890123456",
  "h5Tag": "v1.0.1.2025",
  "h5Url": "https://merp.dev.com/v1.0.1.2025/auth?deviceId=999",
  "description": "新功能页面更新",
  "isActive": true
}
```

**响应示例：** 同创建接口

### **2.3 获取版本树形结构**

**接口地址：** `POST /api/app-upgrade/version-tree`

**请求参数：**
```json
{
  "clientType": "android",                    // 客户端类型（必填）
  "pageNum": 1,                              // 页码（必填）
  "pageSize": 10                             // 每页大小（必填）
}
```

**响应示例：**
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "pageNum": 1,
    "pageSize": 10,
    "total": 3,
    "data": [
      {
        // APP版本信息
        "id": "1234567890123456",
        "clientType": "android",
        "versionCode": 1001,
        "versionName": "1.0.1",
        "upgradeTitle": "新版本发布",
        "upgradeContent": "1.修复已知问题\n2.性能优化",
        "downloadUrl": "https://example.com/app-v1.0.1.apk",
        "fileSize": 52428800,
        "fileMd5": "d41d8cd98f00b204e9800998ecf8427e",
        "forceUpgrade": false,
        "createTime": 1698123456789,
        "updateTime": 1698123456789,
        // H5版本列表
        "children": [
          {
            "id": "h5_1234567890123456",
            "appUpgradeId": "1234567890123456",
            "h5Tag": "v1.0.1.2025",
            "h5Url": "https://merp.dev.com/v1.0.1.2025/auth?deviceId=999",
            "description": "主页面",
            "isActive": true,
            "createTime": 1698123456789,
            "updateTime": 1698123456789
          },
          {
            "id": "h5_1234567890123457",
            "appUpgradeId": "1234567890123456",
            "h5Tag": "v1.0.1.2025.beta",
            "h5Url": "https://merp.dev.com/v1.0.1.2025.beta/auth?deviceId=999",
            "description": "测试页面",
            "isActive": false,
            "createTime": 1698123456789,
            "updateTime": 1698123456789
          }
        ]
      }
      // ... 更多APP版本及其H5版本
    ]
  }
}
```