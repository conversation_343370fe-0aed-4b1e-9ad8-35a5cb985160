import os
import gym
import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from collections import deque
import random
from torch.nn.functional import mse_loss
from gym.wrappers import AtariPreprocessing, FrameStack
import pyglet
from pyglet.window import key
import time

# 检测设备类型
if torch.backends.mps.is_available():
    device = torch.device("mps")  # M1 GPU
elif torch.cuda.is_available():
    device = torch.device("cuda")  # NVIDIA GPU
else:
    device = torch.device("cpu")  # CPU

def wrap_atari(env):
    """Atari环境预处理封装"""
    env = AtariPreprocessing(env, 
                           frame_skip=4,
                           screen_size=84,
                           terminal_on_life_loss=True,
                           grayscale_obs=True,
                           scale_obs=False)  # 禁用自动缩放
    return env

class DQN(nn.Module):
    """深度Q网络定义"""
    def __init__(self, in_channels=4, num_actions=18):
        super(DQN, self).__init__()
        self.conv = nn.Sequential(
            nn.Conv2d(in_channels, 32, kernel_size=8, stride=4),
            nn.ReLU(),
            nn.Conv2d(32, 64, kernel_size=4, stride=2),
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, stride=1),
            nn.ReLU(),
            nn.Conv2d(64, 64, kernel_size=3, stride=1),
            nn.ReLU()
        )
        self.fc = nn.Sequential(
            nn.Linear(5 * 5 * 64, 512),
            nn.ReLU(),
            nn.Linear(512, num_actions)
        )

    def forward(self, x):
        # 形状验证 (可选，但有助于调试)
        if x.ndim != 4 or x.shape[1] != 4 or x.shape[2] != 84 or x.shape[3] != 84:
            raise ValueError(f"Invalid input shape: {x.shape}, expected (N, 4, 84, 84)")

        x = self.conv(x)
        x = x.view(x.size(0), -1)  # 将卷积层输出展平
        return self.fc(x)

class ReplayBuffer:
    """经验回放实现 对应文档3.1节"""
    def __init__(self, capacity):
        self.buffer = deque(maxlen=capacity)
    
    def push(self, state, action, reward, next_state, done):
        self.buffer.append( (state, action, reward, next_state, done) )
    
    def sample(self, batch_size):
        return random.sample(self.buffer, batch_size)
    
    def __len__(self):
        return len(self.buffer)

class DQNAgent:
    """DQN智能体 整合文档4.1算法流程"""
    def __init__(self, env, model_path="dqn_model"):
        self.env = env
        # 保留元数据配置
        self.env.metadata = {
            'render_fps': 30,
            'render_modes': ['human', 'rgb_array']
        }
        
        # 初始化双网络
        self.policy_net = DQN(num_actions=self.env.action_space.n).to(device)  # 传入动作数量
        self.target_net = DQN(num_actions=self.env.action_space.n).to(device)  # 传入动作数量
        self.target_net.load_state_dict(self.policy_net.state_dict())
        self.target_net.eval()
        
        # 优化器与经验回放
        self.optimizer = optim.Adam(self.policy_net.parameters(), lr=0.0001)
        self.memory = ReplayBuffer(10000)
        
        # 超参数设置
        self.batch_size = 32
        self.gamma = 0.99
        self.eps_start = 1.0
        self.eps_end = 0.01
        self.eps_decay = 0.995
        self.target_update = 1000
        self.steps_done = 0
        
        # 添加类型配置
        self.dtype = torch.float32  # 强制使用float32
        self.tensor_kwargs = {'device': device, 'dtype': self.dtype}
        
        # 添加设备属性
        self.device = device  # 使用全局检测到的设备

        # Pyglet 窗口和图像对象 (初始化为空)
        self.window = None
        self.image = None

        # 模型保存路径
        self.model_path = model_path

    def select_action(self, state, isTest=False):
        """ε-greedy动作选择 对应文档6.1节"""
        self.steps_done += 1
        eps_threshold = self.eps_end + (self.eps_start - self.eps_end) * \
                       np.exp(-1. * self.steps_done / 200000)
        
        if (self.steps_done % 1000 == 0):
            print(f"Epsilon: {eps_threshold:.4f}")
        
        if random.random() > eps_threshold or isTest:
            with torch.no_grad():
                # 确保返回整数类型
                action_idx = self.policy_net(state).max(1)[1].item()
                return torch.tensor([action_idx], 
                                  device=self.device, 
                                  dtype=torch.long).unsqueeze(0)  # 保持批次维度
        else:
            # 生成整数动作并确保类型正确
            action_idx = random.randrange(self.env.action_space.n)
            return torch.tensor([action_idx],
                              device=self.device, 
                              dtype=torch.long).unsqueeze(0)  # 保持批次维度

    def optimize_model(self):
        """模型优化核心逻辑 对应文档4.1步骤10-12"""
        if len(self.memory) < self.batch_size:
            return

        # 从回放缓冲区采样
        transitions = self.memory.sample(self.batch_size)
        batch = list(zip(*transitions))

        # 转换为 PyTorch 张量 (无需再 squeeze)
        state_batch = torch.cat(batch[0])
        action_batch = torch.cat([a.view(-1, 1) for a in batch[1]])
        reward_batch = torch.cat(batch[2]).view(-1, 1)
        next_state_batch = torch.cat([s for s in batch[3] if s is not None])
        non_final_mask = torch.tensor(tuple(map(lambda s: s is not None, batch[3])),
                                      device=self.device, dtype=torch.bool)

        # 计算 Q(s_t, a)
        state_action_values = self.policy_net(state_batch).gather(1, action_batch)

        # 计算 V(s_{t+1})
        next_state_values = torch.zeros(self.batch_size, device=self.device)
        if non_final_mask.any():  # 防止 non_final_mask 为空
            next_state_values[non_final_mask] = self.target_net(next_state_batch).max(1)[0].detach()

        # 计算期望 Q 值
        expected_state_action_values = (next_state_values.unsqueeze(1) * self.gamma) + reward_batch

        # 损失计算
        loss = mse_loss(state_action_values, expected_state_action_values)

        # 优化模型
        self.optimizer.zero_grad()
        loss.backward()
        nn.utils.clip_grad_norm_(self.policy_net.parameters(), 10)
        self.optimizer.step()
    def train(self, num_episodes=10000, start_episode=0):
        """训练循环 对应文档5.3节"""
        for i_episode in range(start_episode, num_episodes):
            # 每50个episode渲染一次
            render_episode = (i_episode % 50 == 0)
          
            # 环境初始化,直接利用framestack
            state = self.env.reset()
            state = self._process_state(state)  # 处理初始状态
          
            episode_reward = 0
            
            # Pyglet 窗口和事件处理 (仅在需要渲染时创建)
            if render_episode:
                if self.window is None:  # 首次创建窗口
                  self.window = pyglet.window.Window(width=self.env.render().shape[1], height=self.env.render().shape[0])
  
                  @self.window.event
                  def on_draw():
                      self.window.clear()
                      if self.image:
                          self.image.blit(0, 0)
  
                  @self.window.event
                  def on_close():
                      self.window.close()
                      self.window = None  # 重要：关闭后重置为 None
                # 初始帧
                img_array = self.env.render()
                self.image = pyglet.image.ImageData(img_array.shape[1], img_array.shape[0], 'RGB', img_array.tobytes(), pitch=img_array.shape[1] * -3)
                # python暂停0.05s
              
                
  
            while True:
                  if render_episode:
                      pyglet.clock.tick()  # 更新 Pyglet 时钟
                      self.window.dispatch_events()  # 处理窗口事件
                      self.window.dispatch_event("on_draw")  # 手动触发 on_draw
  
                  action = self.select_action(state)
                  if action.item() >= self.env.action_space.n:
                      print(f"非法动作值: {action.item()}, 重置为0")
                      action = torch.tensor([0], device=self.device, dtype=torch.long)
  
                  # 执行动作
                  next_state, reward, terminated, truncated, _ = self.env.step(action.item())
                  done = terminated or truncated
  
                  # 重要：直接处理 next_state
                  next_state = self._process_state(next_state)
                  reward = np.sign(reward) #奖励截断
                  episode_reward += reward
  
                  # 存储经验 (next_state 为 None 如果是终止状态)
                  self.memory.push(
                      state,
                      action,
                      torch.tensor([reward], **self.tensor_kwargs),
                      next_state if not done else None,
                      torch.tensor([done], **self.tensor_kwargs)
                    )
  
                  # 状态转移
                  state = next_state
  
                  # 执行一步优化
                  self.optimize_model()
  
                  # 更新目标网络
                  if self.steps_done % self.target_update == 0:
                      self.target_net.load_state_dict(self.policy_net.state_dict())
                  
                  if render_episode:
                    img_array = self.env.render()
                    self.image.set_data('RGB', img_array.shape[1] * -3, img_array.tobytes())
                    self.window.flip()  # 更新窗口显示
                    time.sleep(1/30.0)
                  if done:
                      break
  
            # 输出训练进度
            if i_episode % 10 == 0:
                print(f'Episode {i_episode}, Total steps: {self.steps_done}, Reward: {episode_reward}')
            if render_episode and self.window:
              self.window.close()  # 关闭窗口
              self.window = None #重置
              self.image = None

            # 保存模型 (每 100 个 episodes)
            if i_episode % 100 == 0:
                self.save_model()


    def _process_state(self, state):
        """处理来自 FrameStack 的状态"""
        if isinstance(state, tuple):
            state = state[0]  # 对于 reset 返回的元组
        
        # LazyFrames to numpy
        if isinstance(state, gym.wrappers.frame_stack.LazyFrames):
            state = np.array(state)

        # 确保是 numpy array
        if not isinstance(state, np.ndarray):
          state = np.array(state)

        # 转换为 float32 并归一化
        state = state.astype(np.float32) / 255.0
    
        # 转换为 torch tensor
        state = torch.from_numpy(state).to(**self.tensor_kwargs)
        return state.unsqueeze(0)  # 添加批次维度

    def save_model(self):
        path = self.model_path + str(int(self.steps_done / 1000)) + '.pth'
        """保存模型状态"""
        torch.save({
            'policy_net_state_dict': self.policy_net.state_dict(),
            'target_net_state_dict': self.target_net.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'steps_done': self.steps_done,
            # 可以选择性地保存 replay buffer (如果需要)
        }, path)
        print(f"Model saved to {path}")

    def load_model(self, model_path):
        """加载模型状态"""
        if os.path.isfile(model_path):
            checkpoint = torch.load(model_path, map_location=self.device)
            self.policy_net.load_state_dict(checkpoint['policy_net_state_dict'])
            self.target_net.load_state_dict(checkpoint['target_net_state_dict'])
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
            self.steps_done = checkpoint['steps_done']
            self.policy_net.train()  # 确保加载后处于训练模式
            self.target_net.eval()
            print(f"Model loaded from {model_path}, steps done: {self.steps_done}")
            return True  # 加载成功
        else:
            print(f"No model found at {model_path}")
            return False  # 加载失败

    def test(self, num_episodes=10):
        """测试模式 (不进行训练)"""
        self.policy_net.eval()  # 切换到评估模式

        for i_episode in range(num_episodes):
            state = self.env.reset()
            state = self._process_state(state)
            episode_reward = 0

            # Pyglet 窗口和事件处理
            if self.window is None:
                # 修改窗口大小为原始尺寸的2倍
                render_shape = self.env.render().shape
                self.window = pyglet.window.Window(width=render_shape[1]*4, height=render_shape[0]*4)

                @self.window.event
                def on_draw():
                    self.window.clear()
                    if self.image:
                        # 使用缩放参数放大图像
                        self.image.blit(0, 0, width=self.window.width, height=self.window.height)

                @self.window.event
                def on_close():
                    self.window.close()
                    self.window = None

            img_array = self.env.render()
            self.image = pyglet.image.ImageData(img_array.shape[1], img_array.shape[0], 'RGB', img_array.tobytes(), pitch=img_array.shape[1] * -3)

            while True:
                pyglet.clock.tick()
                self.window.dispatch_events()
                self.window.dispatch_event("on_draw")
                
                with torch.no_grad():  # 测试时不需要梯度
                    action = self.select_action(state, True)

                next_state, reward, terminated, truncated, _ = self.env.step(action.item())
                done = terminated or truncated
                next_state = self._process_state(next_state)
                episode_reward += reward

                state = next_state

                img_array = self.env.render()
                self.image.set_data('RGB', img_array.shape[1] * -3, img_array.tobytes())
                self.window.flip()
                time.sleep(1 / 30.0)

                if done:
                    break

            print(f'Test Episode {i_episode}, Reward: {episode_reward}')
            if self.window:
              self.window.close()
              self.window = None
              self.image = None

        self.policy_net.train()  # 切换回训练模式

if __name__ == '__main__':
    # 正确配置环境
    env = gym.make('BreakoutNoFrameskip-v4',
                   render_mode='rgb_array',
                   full_action_space=False,
                   frameskip=1,
                   repeat_action_probability=0.0)
    env = wrap_atari(env)
    env = FrameStack(env, 4)  # 帧堆叠
    env.unwrapped.ale.setInt(b'random_seed', 42)
    agent = DQNAgent(env)

    # 训练或测试 (选择一个)
    # 1. 从头开始训练
    # agent.train()

    # # 2. 加载并继续训练
    # if agent.load_model("doc/aishare/rl/dqn/dqn_model1695.pth"):
    #   agent.train(start_episode= (agent.steps_done // 10000) + 1 )  # 从上次保存的 episode 继续


    # 3. 加载并测试
    # module_path = "doc/aishare/rl/dqn/dqn_model36.pth"
    # module_path = "doc/aishare/rl/dqn/dqn_model180.pth"
    # module_path = "doc/aishare/rl/dqn/dqn_model1158.pth"
    # module_path = "doc/aishare/rl/dqn/dqn_model1695.pth"
    module_path = "doc/aishare/rl/dqn/dqn_model2094.pth"
    if agent.load_model(module_path):
        agent.test()