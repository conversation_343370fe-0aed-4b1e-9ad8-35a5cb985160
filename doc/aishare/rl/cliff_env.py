import numpy as np
import gymnasium as gym
import matplotlib.pyplot as plt

class CliffEnv(gym.Env):
    """悬崖迷宫环境实现"""
    def __init__(self, wall_penalty=False):
        self.wall_penalty = wall_penalty  # 是否对撞墙进行惩罚
        # 迷宫地图：0=安全路径 1=悬崖 2=目标
        self.maze = np.array([
            [0, 0, 0, 0, 0, 0, 0],
            [0, 1, 0, 1, 0, 0, 0],
            [0, 1, 0, 0, 1, 0, 0],
            [0, 1, 1, 0, 1, 0, 0],
            [0, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 0, 2]
        ])
        self.n_rows = self.maze.shape[0]  # 行数
        self.n_cols = self.maze.shape[1]  # 列数
        self.state = (0, 0)  # 起点坐标
        self.action_space = gym.spaces.Discrete(4)  # 动作空间：上下左右
        self.observation_space = gym.spaces.Discrete(self.n_rows * self.n_cols)

        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']  # 优先使用系统字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        plt.rcParams['font.size'] = 12  # 统一字体大小

    def reset(self):
        self.state = (0, 0)
        return self._get_state_index()
    
    def step(self, action):
        x, y = self.state
        # 动作映射：0=上 1=下 2=左 3=右
        action_map = {
            0: (-1, 0),
            1: (1, 0),
            2: (0, -1),
            3: (0, 1)
        }
        dx, dy = action_map[action]
        new_x = x + dx
        new_y = y + dy
        
        # 边界检查
        if 0 <= new_x < self.n_rows and 0 <= new_y < self.n_cols:
            cell_value = self.maze[new_x, new_y]
            # 悬崖检测
            if cell_value == 1:
                return self._get_state_index(), -10, True, {"termination": "fell into cliff"}
            # 更新状态
            self.state = (new_x, new_y)
        else:
            # 撞墙处理
            if self.wall_penalty:
                return self._get_state_index(), -10, True, {"termination": "hit wall"}
            else:
                # 保持当前状态
                pass
        
        # 目标检测
        done = (self.maze[self.state] == 2)
        reward = 10 if done else -0.1
        return self._get_state_index(), reward, done, {}

    def _get_state_index(self):
        return self.state[0] * self.n_cols + self.state[1]

    def render(self, mode='human', action=None, delay=0.1, epsilon=None):
        """保持与迷宫完全一致的交互模式"""
        plt.ion()  # 统一使用交互模式
        plt.figure(1)
        plt.clf()
        
        # 使用与迷宫示例相同的Pastel1配色
        color_map = np.zeros_like(self.maze, dtype=float)
        for i in range(self.n_rows):  # 改为使用self.n_rows
            for j in range(self.n_cols):  # 改为使用self.n_cols
                if self.maze[i,j] == 1:  # 悬崖
                    color_map[i,j] = 0.3
                elif self.maze[i,j] == 2:  # 目标
                    color_map[i,j] = 0.8
                else:  # 普通路径
                    color_map[i,j] = 0.6
        
        plt.imshow(color_map, cmap='Pastel1', vmin=0, vmax=1)
        ax = plt.gca()
        ax.set_xticks(np.arange(-0.5, self.n_cols, 1))  # 改为使用self.n_cols
        ax.set_yticks(np.arange(-0.5, self.n_rows, 1))  # 改为使用self.n_rows
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.grid(color='black', linestyle='-', linewidth=1)
        
        # 绘制小球和方向指示
        x, y = self.state
        plt.scatter(y, x, c='red', s=200, marker='o', edgecolors='black')
        
        # 如果存在动作，绘制方向指示
        if action is not None:
            # 修正方向箭头参数
            arrow_length = 0.3
            arrow_params = {
                0: (0, -arrow_length),  # 上
                1: (0, arrow_length),   # 下
                2: (-arrow_length, 0),  # 左
                3: (arrow_length, 0)    # 右
            }
            dx, dy = arrow_params[action]
            plt.arrow(y, x, dx, dy, head_width=0.2, head_length=0.2,  # 修正dx,dy顺序
                     fc='blue', ec='blue', linewidth=2)
        
        # 显示epsilon值
        if epsilon is not None:
            plt.text(0.5, -0.1, f"当前探索率 (ε): {epsilon:.3f}", 
                    ha='center', va='center', 
                    transform=ax.transAxes,
                    fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.8))
        
        plt.title("悬崖迷宫环境 - 智能体运动可视化")
        plt.pause(delay)  # 仅暂停不关闭窗口 