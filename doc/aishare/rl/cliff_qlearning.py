import numpy as np
import gymnasium as gym
import matplotlib.pyplot as plt
import seaborn as sns

class CliffEnv(gym.Env):
    """悬崖迷宫环境实现"""
    def __init__(self, wall_penalty=False):
        self.wall_penalty = wall_penalty  # 是否对撞墙进行惩罚
        # 迷宫地图：0=安全路径 1=悬崖 2=目标
        self.maze = np.array([
            [0, 0, 0, 0, 0, 0, 0],
            [0, 1, 0, 1, 0, 0, 0],
            [0, 1, 0, 0, 1, 0, 0],
            [0, 1, 1, 0, 1, 0, 0],
            [0, 0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 0, 2]
        ])
        self.n_rows = self.maze.shape[0]  # 行数
        self.n_cols = self.maze.shape[1]  # 列数
        self.state = (0, 0)  # 起点坐标
        self.action_space = gym.spaces.Discrete(4)  # 动作空间：上下左右
        self.observation_space = gym.spaces.Discrete(self.n_rows * self.n_cols)

        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']  # 优先使用系统字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        plt.rcParams['font.size'] = 12  # 统一字体大小

    def reset(self):
        self.state = (0, 0)
        return self._get_state_index()
    
    def step(self, action):
        x, y = self.state
        # 动作映射：0=上 1=下 2=左 3=右
        action_map = {
            0: (-1, 0),
            1: (1, 0),
            2: (0, -1),
            3: (0, 1)
        }
        dx, dy = action_map[action]
        new_x = x + dx
        new_y = y + dy
        
        # 边界检查
        if 0 <= new_x < self.n_rows and 0 <= new_y < self.n_cols:
            cell_value = self.maze[new_x, new_y]
            # 悬崖检测
            if cell_value == 1:
                return self._get_state_index(), -10, True, {"termination": "fell into cliff"}
            # 更新状态
            self.state = (new_x, new_y)
        else:
            # 撞墙处理
            if self.wall_penalty:
                return self._get_state_index(), -10, True, {"termination": "hit wall"}
            else:
                # 保持当前状态
                pass
        
        # 目标检测
        done = (self.maze[self.state] == 2)
        reward = 10 if done else -0.1
        return self._get_state_index(), reward, done, {}

    def _get_state_index(self):
        return self.state[0] * self.n_cols + self.state[1]

    def render(self, mode='human', action=None, delay=0.1, epsilon=None):
        """保持与迷宫完全一致的交互模式"""
        plt.ion()  # 统一使用交互模式
        plt.figure(1)
        plt.clf()
        
        # 使用与迷宫示例相同的Pastel1配色
        color_map = np.zeros_like(self.maze, dtype=float)
        for i in range(self.n_rows):  # 改为使用self.n_rows
            for j in range(self.n_cols):  # 改为使用self.n_cols
                if self.maze[i,j] == 1:  # 悬崖
                    color_map[i,j] = 0.3
                elif self.maze[i,j] == 2:  # 目标
                    color_map[i,j] = 0.8
                else:  # 普通路径
                    color_map[i,j] = 0.6
        
        plt.imshow(color_map, cmap='Pastel1', vmin=0, vmax=1)
        ax = plt.gca()
        ax.set_xticks(np.arange(-0.5, self.n_cols, 1))  # 改为使用self.n_cols
        ax.set_yticks(np.arange(-0.5, self.n_rows, 1))  # 改为使用self.n_rows
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.grid(color='black', linestyle='-', linewidth=1)
        
        # 绘制小球和方向指示
        x, y = self.state
        plt.scatter(y, x, c='red', s=200, marker='o', edgecolors='black')
        
        # 如果存在动作，绘制方向指示
        if action is not None:
            # 修正方向箭头参数
            arrow_length = 0.3
            arrow_params = {
                0: (0, -arrow_length),  # 上
                1: (0, arrow_length),   # 下
                2: (-arrow_length, 0),  # 左
                3: (arrow_length, 0)    # 右
            }
            dx, dy = arrow_params[action]
            plt.arrow(y, x, dx, dy, head_width=0.2, head_length=0.2,  # 修正dx,dy顺序
                     fc='blue', ec='blue', linewidth=2)
        
        # 显示epsilon值
        if epsilon is not None:
            plt.text(0.5, -0.1, f"当前探索率 (ε): {epsilon:.3f}", 
                    ha='center', va='center', 
                    transform=ax.transAxes,
                    fontsize=12,
                    bbox=dict(facecolor='white', alpha=0.8))
        
        plt.title("悬崖迷宫环境 - 智能体运动可视化")
        plt.pause(delay)  # 仅暂停不关闭窗口
        # plt.close()  # 统一在训练循环中管理窗口关闭

class QLearningAgent:
    def __init__(self, state_size, action_size, learning_rate=0.1, gamma=0.95, epsilon_start=1.0, epsilon_end=0.1, epsilon_decay=0.995):
        self.q_table = np.zeros((state_size, action_size))
        self.learning_rate = learning_rate
        self.gamma = gamma
        self.epsilon_start = epsilon_start
        self.epsilon = epsilon_start
        self.epsilon_end = epsilon_end
        self.epsilon_decay = epsilon_decay

    def choose_action(self, state, is_training=True):
        if is_training:
            if np.random.uniform(0, 1) < self.epsilon:
                return np.random.choice(4)  # 随机探索
            else:
                return np.argmax(self.q_table[state])  # 选择最优动作
        else:
            return np.argmax(self.q_table[state])  # 选择最优动作

    def update_q_table(self, state, action, reward, next_state, done):
        if done:
            target = reward
        else:
            target = reward + self.gamma * np.max(self.q_table[next_state])
        self.q_table[state, action] += self.learning_rate * (target - self.q_table[state, action])
        
    def decay_epsilon(self):
        """衰减epsilon值"""
        self.epsilon = max(self.epsilon_end, self.epsilon * self.epsilon_decay)

def train(episodes=3000, visualize_steps=False, wall_penalty=False):
    env = CliffEnv(wall_penalty=wall_penalty)
    agent = QLearningAgent(
        state_size=env.n_rows * env.n_cols,
        action_size=4,
        learning_rate=0.2,
        gamma=0.95,
        epsilon_start=1.0,  # 初始探索率
        epsilon_end=0.1,    # 最小探索率
        epsilon_decay=0.999 # 衰减率
    )
    
    rewards = []
    for episode in range(episodes):
        state = env.reset()
        total_reward = 0
        done = False
        
        while not done:
            action = agent.choose_action(state)
            next_state, reward, done, _ = env.step(action)
            agent.update_q_table(state, action, reward, next_state, done)
            state = next_state
            total_reward += reward
            
            # 统一渲染触发条件（每100轮且开启可视化时）
            if visualize_steps and (episode % 500 == 0 or episode < 5):
                env.render(action=action, delay=0.1, epsilon=agent.epsilon)
        
        rewards.append(total_reward)
        agent.decay_epsilon()  # 每轮结束后衰减epsilon
        
        # 每100轮调用可视化（保持与迷宫相同逻辑）
        if episode % 500 == 0 or episode < 5:
            visualize_training(env, agent, episode)
            if episode < 5:
                s = input("按任意键继续")
            # plt.close('all')  # 关闭所有训练中间窗口
            
    return agent

def visualize_training(env, agent, episode):
    plt.ioff()  # 使用非交互模式
    plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']  # 优先使用系统字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    plt.rcParams['font.size'] = 12  # 统一字体大小
    plt.figure(figsize=(15, 6))  # 调整画布大小适应两个子图
    plt.clf()
    
    # 最大Q值热力图（左图）- 展示动作价值函数
    plt.subplot(121)
    q_matrix = agent.q_table.reshape(env.n_rows, env.n_cols, 4)
    max_q = np.max(q_matrix, axis=2)
    sns.heatmap(max_q, annot=True, fmt=".2f", cmap="viridis",
                cbar_kws={'label': '最大Q值'})
    plt.title(f'各状态最大Q值 (Episode {episode})')
    
    # 动作价值明细图（右图） - 显示完整四个动作价值
    plt.subplot(122)
    directions = ['↑', '↓', '←', '→']  # 动作方向标识
    
    # 设置坐标轴范围（使用环境参数）
    plt.xlim(0, env.n_cols)
    plt.ylim(0, env.n_rows)
    plt.xticks([])
    plt.yticks([])
    
    # 动态获取Q表维度
    q_matrix = agent.q_table.reshape(env.n_rows, env.n_cols, 4)
    
    # 固定Q值范围
    min_q = -10
    max_q = 10
    
    # 遍历所有状态
    for row in range(env.n_rows):
        for col in range(env.n_cols):
            # 获取当前状态所有动作的Q值
            state_q_values = q_matrix[row, col]
            
            # 为每个动作创建注释框
            for action in range(4):
                q_value = state_q_values[action]
                
                # 根据固定范围计算颜色
                norm_value = (q_value - min_q) / (max_q - min_q)
                color = plt.cm.viridis(norm_value)  # 使用viridis颜色映射
                
                # 调整动作标注位置偏移
                arrow_length = 0.3
                offset = {
                    0: (0, -arrow_length),   # 上：在单元格上方
                    1: (0, arrow_length),  # 下：在单元格下方
                    2: (-arrow_length, 0),  # 左：在单元格左侧
                    3: (arrow_length, 0)    # 右：在单元格右侧
                }[action]
                
                # 绘制Q值标注
                plt.text(
                    x=col + 0.5 + offset[0],
                    y=row + 0.5 + offset[1],
                    s=f"{directions[action]}\n{q_value:.2f}",
                    ha='center', va='center',
                    color='white' if norm_value > 0.5 else 'black',  # 根据背景色调整文字颜色
                    fontsize=9,
                    bbox=dict(
                        facecolor=color,
                        edgecolor='none',
                        alpha=0.8,
                        boxstyle='round,pad=0.2'
                    )
                )
    
    plt.gca().invert_yaxis()  # 保持y轴方向一致
    plt.title('各动作Q值分布')
    
    # 添加颜色图例（右图）
    plt.subplot(122)
    plt.axis('off')
    legend_elements = [
        plt.Line2D([0], [0], marker='o', color='w', label='高Q值',
                  markerfacecolor='yellow', markersize=10),
        plt.Line2D([0], [0], marker='o', color='w', label='低Q值',
                  markerfacecolor='darkblue', markersize=10),
        plt.Line2D([0], [0], marker='^', color='w', label='上',  # 使用标准marker符号
                  markerfacecolor='gray', markersize=10),
        plt.Line2D([0], [0], marker='>', color='w', label='右',  # 使用标准marker符号
                  markerfacecolor='gray', markersize=10)
    ]
    # 调整图例位置到右上角
    plt.legend(handles=legend_elements, loc='upper right', bbox_to_anchor=(0.0, 1.0),
              title="图例说明", frameon=False)
    
    plt.tight_layout()
    plt.show(block=False)
    plt.pause(2)  # 保持显示2秒

def test_agent(agent, visualize_steps=True):
    """测试函数与迷宫示例保持一致"""
    env = CliffEnv()
    state = env.reset()
    done = False
    total_reward = 0
    while not done:
        action = agent.choose_action(state, is_training=False)
        next_state, reward, done, _ = env.step(action)
        total_reward += reward
        state = next_state

        if visualize_steps:
            env.render(action=action, delay=0.3)
    
    print("测试总奖励:", total_reward)
    return total_reward

if __name__ == "__main__":
    trained_agent = train(visualize_steps=True, wall_penalty=True)
    
    while True:
        test_agent(trained_agent)
        s = input("是否继续测试？（y/n）")
        if s == "n":
            break