# 强化学习教程文档结构

## 1. 强化学习基础概念 (rl_basics.md)
- 什么是强化学习
- 与监督、非监督学习的对比
- 强化学习的核心要素
- 现实生活中的例子
- 应用场景分析

## 2. 强化学习核心机制 (rl_mechanisms.md) 
- 马尔可夫决策过程详解
  - 为什么需要MDP框架
  - MDP的直观解释
  - 状态转移与奖励
  - 马尔可夫性质的实际意义
- 价值函数与策略详解
  - 价值函数的直观理解
  - 状态价值vs动作价值
  - 策略函数的作用机制
  - 值函数与策略的关系
- 探索与利用机制
  - 探索-利用困境详解
  - 常用探索策略对比
  - 实际案例分析

## 3. Q-Learning算法详解 (q_learning.md) 
- Q-Learning算法原理
- Q表格更新规则
- 贝尔曼方程推导
- 学习率与折扣因子
- 实战案例:迷宫寻路
  - 环境构建
  - 完整代码实现
  - 训练过程可视化
  - 结果分析

## 4. 深度Q网络(DQN) (dqn.md)
- 从Q-Learning到DQN的演进
- DQN网络结构
- Experience Replay机制
- Target Network
- DQN算法流程
- 实战案例:Atari游戏
  - PyTorch实现
  - 训练技巧
  - 性能优化

## 5. 策略梯度方法 (policy_gradient.md)
- 策略梯度定理
- REINFORCE算法
- Actor-Critic架构
- PPO算法详解
- TRPO算法概述
- 实战案例:连续控制
  - CartPole环境
  - 完整训练流程
  - 调参经验

## 6. 前沿算法与应用 (advanced.md)
- SAC(Soft Actor-Critic)
- TD3(Twin Delayed DDPG)
- 多智能体强化学习
- 逆强化学习
- 模仿学习
- 大模型与强化学习
  - PPO在大模型训练中的应用
  - RLHF原理

## 7. 工程实践指南 (engineering.md)
- 环境构建最佳实践
- 并行训练框架
- 超参数调优
- 训练稳定性
- 常见问题排查
- 部署与服务化
- 性能优化技巧

## 补充资料
- 数学基础
- 推荐阅读
- 开源项目推荐
- 学习路线图