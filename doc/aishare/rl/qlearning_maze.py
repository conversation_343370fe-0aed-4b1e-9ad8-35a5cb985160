import numpy as np
import gymnasium as gym
import matplotlib.pyplot as plt
import seaborn as sns

class MazeEnv(gym.Env):
    """迷宫环境实现"""
    def __init__(self):
        self.maze = np.array([
            [0, 0, 0, 1],
            [1, 1, 0, 1],
            [0, 0, 0, 0],
            [1, 1, 1, 2]
        ])  # 0:通道 1:墙壁 2:目标
        self.state = (0, 0)  # 起点坐标
        self.action_space = gym.spaces.Discrete(4)  # 动作空间：上下左右
        self.observation_space = gym.spaces.Discrete(16)  # 状态空间：4x4=16种可能位置

        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']  # 优先使用系统字体
        plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
        plt.rcParams['font.size'] = 12  # 统一字体大小

    def reset(self):
        """重置环境到初始状态"""
        self.state = (0, 0)
        return self._get_state_index()
    
    def step(self, action):
        """执行动作并返回新状态、奖励、是否终止"""
        x, y = self.state
        # 根据动作计算新坐标
        if action == 0:   new_state = (x-1, y)  # 上
        elif action == 1: new_state = (x+1, y)  # 下
        elif action == 2: new_state = (x, y-1)  # 左
        elif action == 3: new_state = (x, y+1)  # 右
        
        # 边界检查
        if (0 <= new_state[0] < 4) and (0 <= new_state[1] < 4):
            cell_value = self.maze[new_state]
            if cell_value != 1:  # 不是墙壁
                self.state = new_state
                
        # 计算奖励
        done = (self.maze[self.state] == 2)
        reward = 10 if done else -0.1
        return self._get_state_index(), reward, done, {}

    def _get_state_index(self):
        """将坐标转换为状态索引"""
        return self.state[0] * 4 + self.state[1]

    def render(self, mode='human', action=None, delay=0.1):
        """
        实时可视化当前迷宫状态和智能体位置。
        
        参数：
            mode: 渲染模式，此处仅支持 'human'
            action: 当前动作编号，用于显示对应文字描述（0:上, 1:下, 2:左, 3:右）
            delay: 帧间延时（秒），控制动画速度
        """
        plt.ion()  # 开启交互模式
        plt.figure(1)
        plt.clf()
        
        # 绘制迷宫结构
        maze_copy = self.maze.copy().astype(float)
        plt.imshow(maze_copy, cmap='Pastel1')
        ax = plt.gca()
        ax.set_xticks(np.arange(-0.5, self.maze.shape[1], 1))
        ax.set_yticks(np.arange(-0.5, self.maze.shape[0], 1))
        ax.set_xticklabels([])
        ax.set_yticklabels([])
        ax.grid(color='black', linestyle='-', linewidth=1)
        
        # 绘制智能体位置（注意：imshow中数组的行对应 y 轴）
        x, y = self.state
        plt.scatter(y, x, c='red', s=200, marker='o', label='Agent')
        
        # 显示当前执行的动作（映射: 0=上, 1=下, 2=左, 3=右）
        if action is not None:
            action_mapping = {0: "上", 1: "下", 2: "左", 3: "右"}
            plt.text(0, -0.3, f'当前动作: {action_mapping.get(action, "未知")}',
                     fontsize=12, color='blue')
        
        plt.title("迷宫环境 - 智能体运动可视化")
        plt.pause(delay)

class QLearningAgent:
    """Q-Learning智能体实现"""
    def __init__(self, state_size, action_size, 
                 learning_rate=0.1, gamma=0.99, epsilon=0.1):
        self.q_table = np.zeros((state_size, action_size))
        self.lr = learning_rate
        self.gamma = gamma
        self.epsilon = epsilon
        self.action_size = action_size  # 新增属性存储动作空间尺寸
    
    def select_action(self, state):
        """ε-greedy策略选择动作"""
        if np.random.random() < self.epsilon:
            return np.random.randint(self.action_size)
        return np.argmax(self.q_table[state])
    
    def update(self, state, action, reward, next_state):
        """Q值更新规则"""
        best_next_action = np.argmax(self.q_table[next_state])
        td_target = reward + self.gamma * self.q_table[next_state][best_next_action]
        td_error = td_target - self.q_table[state][action]
        self.q_table[state][action] += self.lr * td_error

def train(episodes=1000, visualize_steps=False):
    """训练主函数"""
    env = MazeEnv()
    agent = QLearningAgent(
        state_size=16, 
        action_size=4,
        learning_rate=0.1,
        gamma=0.99,
        epsilon=0.1
    )
    
    for episode in range(episodes):
        state = env.reset()
        done = False
        while not done:
            action = agent.select_action(state)
            next_state, reward, done, _ = env.step(action)
            agent.update(state, action, reward, next_state)
            state = next_state

            # 如果启用每步可视化，则调用 render 方法展示当前状态与动作
            if visualize_steps and episode % 100 == 0:
                env.render(action=action, delay=0.1)

        # 每 100 轮调用一次整体训练进度可视化（例如展示 Q-Table 及迷宫结构）
        if episode % 100 == 0:
            visualize_training(env, agent, episode)
    
    return agent

def visualize_training(env, agent, episode):
    """训练可视化（需要实现绘图函数）"""
    plt.figure(figsize=(15, 5))
    
    # Q表格热力图
    plt.subplot(121)
    sns.heatmap(agent.q_table, annot=True, fmt=".1f")
    plt.title(f'Q-Table (Episode {episode})')
    
    # 最优路径（需实现路径获取和绘制函数）
    plt.subplot(122)
    plt.imshow(env.maze, cmap='Pastel1')
    plt.title('Maze Structure')
    
    plt.tight_layout()
    plt.show()

def test_agent(agent, visualize_steps=True):
    """新增测试函数，用于展示训练后的智能体在迷宫中的表现，并实时可视化每一步动作选择"""
    env = MazeEnv()
    state = env.reset()
    done = False
    total_reward = 0
    while not done:
        action = agent.select_action(state)
        next_state, reward, done, _ = env.step(action)
        total_reward += reward
        state = next_state

        if visualize_steps:
            env.render(action=action, delay=0.3)
    
    print("测试总奖励:", total_reward)

if __name__ == "__main__":
    trained_agent = train(visualize_steps=True) 
    while True:
        test_agent(trained_agent, visualize_steps=True)
        s = input("是否继续测试？（y/n）")
        if s == "n":
            break