import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

export default defineConfig({
  plugins: [vue({
    reactivityTransform: true
  })],
  server: {
    port: 5174,
    open: true,
    host: true,
    proxy: {
      '/api': {
        target: 'http://your-api-server',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  },
  resolve: {
    alias: {
      '@': '/src'
    }
  }
})