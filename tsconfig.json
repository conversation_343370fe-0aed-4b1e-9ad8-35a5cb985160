{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "moduleResolution": "Node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "noEmit": true, "skipLibCheck": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}, "typeRoots": ["./node_modules/@types", "src/shared/types"]}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.vue"], "exclude": ["node_modules"]}