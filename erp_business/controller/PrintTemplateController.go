package controller

import (
	"voderpltvv/erp_managent/api/req"
	"voderpltvv/erp_managent/api/vo"
	"voderpltvv/erp_managent/application/print"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/erp_managent/service/po"
	"voderpltvv/util"

	"github.com/gin-gonic/gin"
)

type PrintTemplateController struct {
	printTemplateAppService *print.PrintTemplateAppService
	printTemplateService    *impl.PrintTemplateService
}

func NewPrintTemplateController() *PrintTemplateController {
	return &PrintTemplateController{
		printTemplateAppService: print.NewPrintTemplateAppService(),
		printTemplateService:    impl.NewPrintTemplateService(),
	}
}

// ListPrintTemplates 获取所有打印模板列表
// @Summary 获取所有打印模板列表
// @Description 获取系统中所有的打印布局模板，包括系统默认模板和自定义模板
// @Tags 打印模板管理
// @Accept json
// @Produce json
// @Param body body req.ListPrintTemplatesReqDto true "请求体"
// @Success 200 {object} Result[[]vo.PrintTemplateVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/print-template/list [post]
func (controller *PrintTemplateController) ListPrintTemplates(ctx *gin.Context) {
	reqDto := &req.ListPrintTemplatesReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	// 调用应用服务
	templateVOs, err := controller.printTemplateAppService.ListPrintTemplates(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, templateVOs)
}

// GetPrintTemplate 获取指定打印模板详情
// @Summary 获取指定打印模板详情
// @Description 根据模板ID获取打印模板的详细信息
// @Tags 打印模板管理
// @Accept json
// @Produce json
// @Param body body req.GetPrintTemplateReqDto true "请求体"
// @Success 200 {object} Result[vo.PrintTemplateVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/print-template/get [post]
func (controller *PrintTemplateController) GetPrintTemplate(ctx *gin.Context) {
	reqDto := &req.GetPrintTemplateReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	// 验证必填字段
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板ID不能为空")
		return
	}

	// 获取模板详情
	template, err := controller.printTemplateService.FindPrintTemplateById(ctx, *reqDto.Id)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	// 转换为VO
	templateVO := controller.convertPrintTemplateToVO(template)
	Result_success[any](ctx, templateVO)
}

// UpdatePrintTemplate 更新打印模板
// @Summary 更新打印模板
// @Description 更新现有的打印布局模板信息
// @Tags 打印模板管理
// @Accept json
// @Produce json
// @Param body body req.UpdatePrintTemplateReqDto true "请求体"
// @Success 200 {object} Result[vo.PrintTemplateVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/print-template/update [post]
func (controller *PrintTemplateController) UpdatePrintTemplate(ctx *gin.Context) {
	reqDto := &req.UpdatePrintTemplateReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	// 验证必填字段
	if reqDto.Id == nil || *reqDto.Id == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板ID不能为空")
		return
	}

	// 调用应用服务
	templateVO, err := controller.printTemplateAppService.UpdatePrintTemplate(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, templateVO)
}

// CreatePrintTemplate 创建自定义打印模板
// @Summary 创建自定义打印模板
// @Description 创建新的打印布局模板，用于门店自定义使用
// @Tags 打印模板管理
// @Accept json
// @Produce json
// @Param body body req.CreatePrintTemplateReqDto true "请求体"
// @Success 200 {object} Result[vo.PrintTemplateVO] "成功"
// @Failure 400 {object} Result[any] "参数错误"
// @Failure 500 {object} Result[any] "服务器错误"
// @Router /api/business/print-template/create [post]
func (controller *PrintTemplateController) CreatePrintTemplate(ctx *gin.Context) {
	reqDto := &req.CreatePrintTemplateReqDto{}
	if err := ctx.ShouldBindJSON(reqDto); err != nil {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "参数格式错误")
		return
	}

	// 验证必填字段
	if reqDto.TemplateCode == nil || *reqDto.TemplateCode == "" {
		Result_fail[any](ctx, GeneralCodes.ParamError.Code, "模板编码不能为空")
		return
	}

	// 调用应用服务
	templateVO, err := controller.printTemplateAppService.CreatePrintTemplate(ctx, reqDto)
	if err != nil {
		Result_fail[any](ctx, GeneralCodes.ServerIsBuzy.Code, err.Error())
		return
	}

	Result_success[any](ctx, templateVO)
}

// convertPrintTemplateToVO 转换打印模板为VO
func (controller *PrintTemplateController) convertPrintTemplateToVO(template *po.PrintTemplate) vo.PrintTemplateVO {
	return vo.PrintTemplateVO{
		Id:              template.GetId(),
		TemplateCode:    util.GetStringValue(template.TemplateCode),
		Name:            util.GetStringValue(template.Name),
		LayoutContent:   util.GetStringValue(template.LayoutContent),
		IsSystemDefault: util.GetBoolValue(template.IsSystemDefault),
		IsEnabled:       util.GetBoolValue(template.IsEnabled),
		Remark:          util.GetStringValue(template.Remark),
		Ctime:           util.GetInt64Value(template.Ctime),
		Utime:           util.GetInt64Value(template.Utime),
	}
}
