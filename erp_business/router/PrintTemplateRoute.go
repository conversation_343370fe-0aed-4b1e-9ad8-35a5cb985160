package router

import (
	"voderpltvv/erp_business/controller"

	"github.com/gin-gonic/gin"
)

type PrintTemplateRoute struct {
}

func (s *PrintTemplateRoute) InitPrintTemplateRouter(g *gin.Engine) {
	// 通信协议采用 HTTP 协议、POST 请求，数据格式 JSON，UTF-8 编码
	printTemplateController := controller.NewPrintTemplateController()
	route := g.Group("")
	{
		route.POST("/api/business/print-template/list", printTemplateController.ListPrintTemplates)       // 获取所有打印模板列表
		route.POST("/api/business/print-template/get", printTemplateController.GetPrintTemplate)         // 获取指定打印模板详情
		route.POST("/api/business/print-template/create", printTemplateController.CreatePrintTemplate)   // 创建自定义打印模板
		route.POST("/api/business/print-template/update", printTemplateController.UpdatePrintTemplate)   // 更新打印模板
	}
}
