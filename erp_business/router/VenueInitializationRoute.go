package router

import (
	"voderpltvv/erp_business/controller"

	"github.com/gin-gonic/gin"
)

// VenueInitializationRoute 商务后台门店初始化路由
type VenueInitializationRoute struct {
}

// InitVenueInitializationRouter 初始化门店初始化路由
func (s *VenueInitializationRoute) InitVenueInitializationRouter(g *gin.Engine) {
	// 创建门店初始化控制器
	venueInitController := controller.NewVenueInitializationController()

	// 商务后台门店初始化路由组
	venueInitGroup := g.Group("/api/business/venue")
	{
		// 批量初始化功能
		// POST /api/business/venue/batch-init-feature
		venueInitGroup.POST("/batch-init-feature", venueInitController.BatchInitFeature)

		// 查询初始化状态
		// POST /api/business/venue/init-status
		venueInitGroup.POST("/init-status", venueInitController.QueryInitStatus)

		// 初始化新门店（内部接口）
		// POST /api/business/venue/init-new
		venueInitGroup.POST("/init-new", venueInitController.InitializeNewVenue)
	}

	// 商务后台系统管理路由组
	systemGroup := g.Group("/api/business/system")
	{
		// 查询初始化进度
		// POST /api/business/system/init-progress
		systemGroup.POST("/init-progress", venueInitController.QueryInitProgress)

		// 重试失败的初始化
		// POST /api/business/system/retry-failed-init
		systemGroup.POST("/retry-failed-init", venueInitController.RetryFailedInit)
	}
}
