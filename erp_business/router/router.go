package router

import (
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/middleware"
	"voderpltvv/model"

	"github.com/gin-gonic/gin"
)

// Load 加载商务后台路由
func Load(g *gin.Engine, mw ...gin.HandlerFunc) {
	// 商务后台专用中间件栈
	businessMiddlewares := []gin.HandlerFunc{
		middleware.RequestID(),
		middleware.GlobalExceptionHandler(),
		middleware.Cors(),
		// 商务后台使用 BusinessAuthMiddleware 进行认证
		// 登录接口通过配置文件的白名单机制跳过认证
		middleware.BusinessAuthMiddleware(impl.NewTokenService(model.Redisdb)),
		middleware.DeviceAuthMiddleware(), // 解析 x-device
		middleware.Intercept(),
		middleware.CheckCommParam(),
		// 商务后台不需要 PostPermissionGuardMiddleware，因为它有独立的权限体系
	}

	// 创建商务后台路由组
	businessGroup := g.Group("/api/business")

	// 应用商务后台专用中间件
	for _, middleware := range businessMiddlewares {
		businessGroup.Use(middleware)
	}

	// 注册商务后台路由
	new(BusinessLoginRoute).InitBusinessRouter(g)
	new(VenueAuthRoute).InitVenueAuthRouter(g)
	new(RoomRoute).InitRoomRouter(g)
	new(VenueRoute).InitVenueRouter(g)
	new(RoomExceptionRoute).InitRoomExceptionRouter(g)
	new(BusinessInventoryRoute).InitBusinessInventoryRouter(g)
	new(BusinessProductRoute).InitBusinessProductRouter(g)
	new(VenueInitializationRoute).InitVenueInitializationRouter(g) // 门店初始化路由
	new(PrintTemplateRoute).InitPrintTemplateRouter(g)             // 打印模板管理路由
}
