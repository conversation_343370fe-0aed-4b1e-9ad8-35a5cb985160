package cmd

import (
	"context"
	"net/http"
	"os"
	"os/signal"
	"voderpltvv/erp_managent/service/impl"
	"voderpltvv/model"

	"syscall"
	"time"
	"voderpltvv/middleware"

	"voderpltvv/erp_managent/controller/check"
	featureDeploymentImpl "voderpltvv/erp_managent/domain/venue/initialization/service/impl"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	businessRouter "voderpltvv/erp_business/router" // 引入商务后台路由
	"voderpltvv/erp_client"
	"voderpltvv/erp_managent/router"
)

// 定义 rootCmd 命令的执行
func runServer() {
	wait := make(chan int, 1)
	quit := make(chan os.Signal, 1)

	// 初始化以下模块请在导入包中打开model 包注释
	// 初始化数据库

	model.InitDB()
	logrus.Info("db链接成功")

	// 权限数据初始化已移至门店创建时自动触发
	logrus.Info("权限数据初始化已移至门店创建时自动触发，无需手动初始化")

	defer model.DBMaster.DBMasterClose()
	defer model.DBSlave.DBSlaveClose()

	//-----
	// 初始化redis
	model.Redisdb.InitRedis()
	defer model.Redisdb.Close()

	//nats 长连接信息
	model.Redis_nats.InitRedisNats()
	defer model.Redis_nats.CloseNats()

	logrus.Info("redis链接成功")

	//初始化本地缓存
	model.LocalCache.Init()

	// 门店数据初始化框架：应用启动时自动检测新功能并触发批量初始化
	go func() {
		time.Sleep(3 * time.Second) // 等待数据库连接稳定

		// 创建上下文
		ctx := &gin.Context{}

		// 创建功能部署服务
		featureDeploymentService := featureDeploymentImpl.NewFeatureDeploymentService()

		// 自动检测并初始化新功能
		if err := featureDeploymentService.AutoDetectAndInitialize(ctx); err != nil {
			logrus.Errorf("应用启动时自动检测和初始化新功能失败: %v", err)
		} else {
			logrus.Info("应用启动时自动检测和初始化新功能完成")
		}
	}()

	if viper.GetString("env") != "local" && viper.GetString("disable_timer") == "" {
		go func() {
			time.Sleep(5 * time.Second)
			// 初始化所有房间的定时器
			// roomTimerService := impl.RoomTimerService{}
			// if err := roomTimerService.InitRoomTimers(&gin.Context{}); err != nil {
			// 	logrus.Fatalf("Failed to initialize room timers: %v", err)
			// }
		}()
	}

	// 在 runServer 函数中，初始化中间件之前，创建 TokenService
	// 初始化 TokenService
	tokenService := impl.NewTokenService(model.Redisdb)
	venueServiceTmp := impl.VenueService{}
	employeeServiceTmp := impl.EmployeeService{}
	cashierMachineServiceTmp := impl.CashierMachineService{}
	venueAuthCodeServiceTmp := impl.VenueAuthCodeService{}

	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	// 设置运行模式
	gin.SetMode(viper.GetString("runmode"))

	// 初始化空的服务器
	app := gin.New()

	// sentry Init,必须再app之后初始化
	model.InitSentry(app)

	// 初始化中间件
	middlewares := []gin.HandlerFunc{
		middleware.RequestID(),
		middleware.GlobalExceptionHandler(),
		middleware.Cors(),
		middleware.AuthTokenMiddleware(tokenService, &venueServiceTmp, &employeeServiceTmp, &cashierMachineServiceTmp, &venueAuthCodeServiceTmp), // 传入 tokenService
		middleware.DeviceAuthMiddleware(), // 解析 x-device
		// middleware.PermissionGuardMiddleware(), // 系统权限校验 - 已注释，改用后处理权限中间件
		middleware.Intercept(),
		middleware.CheckCommParam(),
		middleware.PostPermissionGuardMiddleware(), // 后处理权限中间件 - 在controller执行完成后进行权限检查
		// middleware.Logging(),
	}

	// 加载模版渲染
	app.LoadHTMLGlob("tpl/**/*")

	// 路由
	router.Load(
		app,
		middlewares...,
	)

	// 加载商务后台路由（使用独立的中间件栈）
	businessRouter.Load(app)

	erp_client.InitMainClient(app)

	// 检查服务器正常启动
	go func() {
		if err := check.PingServer(wait); err != nil {
			logrus.Fatal("服务器没有响应:", err)
		}
		logrus.Info("服务器正常启动")
	}()

	// 服务器的地址和端口
	addr := viper.GetString("addr")

	srv := &http.Server{
		Addr:    addr,
		Handler: app,
	}
	// 启动服务
	go func() {
		logrus.Infof("启动服务器在 http address: %s", addr)
		srv.Addr = addr
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("listen on http: %s\n", err)
		}
	}()

	// 等待配置改变 或者 收到退出的信号
	select {
	case <-configChange:
		if err := srv.Shutdown(context.Background()); err != nil {
			logrus.Fatal("Server Shutdown:", err)
		}
		defer runServer()
		return
	case <-quit:
		logrus.Info("收到退出的信号")
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := srv.Shutdown(ctx); err != nil {
			logrus.Fatal("Server Shutdown: ", err)
		}
		logrus.Println("Server exiting")
		return
	}
}
