-- 新打印架构数据库表初始化脚本
-- 基于 "能力硬编码，配置数据库化" 的设计理念

-- ============================================================================
-- 表一：print_templates (全局打印布局模板表)
-- 职责：存储系统全局共享的、可被门店选用的打印布局和样式
-- ============================================================================
CREATE TABLE `print_templates` (
  `id` varchar(64) NOT NULL COMMENT '模板唯一标识',
  `template_code` varchar(50) NOT NULL COMMENT '关联元模板的编码，如SHIFT_CHANGE、BILL等',
  `name` varchar(255) NOT NULL COMMENT '模板名称，供用户选择时显示',
  `layout_content` json NOT NULL COMMENT '布局和样式的JSON定义，由小程序编辑器生成',
  `is_system_default` tinyint(1) DEFAULT '0' COMMENT '是否为系统预设的默认模板，用于新店初始化',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '模板本身是否在系统中可见和可选',
  `remark` varchar(500) DEFAULT '' COMMENT '模板备注说明',
  `ctime` int DEFAULT 0 COMMENT '创建时间',
  `utime` int DEFAULT 0 COMMENT '更新时间',
  `state` int DEFAULT 0 COMMENT '状态',
  `version` int DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`),
  KEY `idx_template_code` (`template_code`) COMMENT '按模板类型查询索引',
  KEY `idx_is_system_default` (`is_system_default`) COMMENT '系统默认模板查询索引',
  KEY `idx_is_enabled` (`is_enabled`) COMMENT '启用状态查询索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='全局打印布局模板表';

-- ============================================================================
-- 表二：venue_print_configs (门店打印配置表)
-- 职责：存储每个门店对特定打印类型的个性化使用偏好
-- ============================================================================
CREATE TABLE `venue_print_configs` (
  `id` varchar(64) NOT NULL COMMENT '配置唯一标识',
  `venue_id` varchar(64) NOT NULL COMMENT '门店ID',
  `template_code` varchar(50) NOT NULL COMMENT '打印类型编码，与元模板的编码对应',
  `selected_template_id` varchar(64) NOT NULL COMMENT '选择使用的print_templates表ID',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '门店级业务开关，控制该门店是否执行此类型的打印',
  `copies` int(11) DEFAULT '1' COMMENT '打印份数',
  `business_config` json DEFAULT NULL COMMENT '业务逻辑开关配置JSON，如{"split_by_type": true, "show_discount_detail": true}',
  `remark` varchar(500) DEFAULT '' COMMENT '配置备注说明',
  `ctime` int DEFAULT 0 COMMENT '创建时间',
  `utime` int DEFAULT 0 COMMENT '更新时间',
  `state` int DEFAULT 0 COMMENT '状态',
  `version` int DEFAULT 0 COMMENT '版本',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_venue_template_code` (`venue_id`,`template_code`) COMMENT '门店+模板类型唯一约束',
  KEY `idx_venue_id` (`venue_id`) COMMENT '按门店查询索引',
  KEY `idx_template_code` (`template_code`) COMMENT '按模板类型查询索引',
  KEY `idx_is_enabled` (`is_enabled`) COMMENT '启用状态查询索引',
  KEY `idx_selected_template` (`selected_template_id`) COMMENT '关联模板查询索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店打印配置表';

-- ============================================================================
-- 外键约束
-- ============================================================================
ALTER TABLE `venue_print_configs` 
ADD CONSTRAINT `fk_venue_print_configs_template` 
FOREIGN KEY (`selected_template_id`) REFERENCES `print_templates` (`id`) 
ON DELETE RESTRICT ON UPDATE CASCADE
COMMENT '门店配置关联的布局模板外键约束';

-- ============================================================================
-- 初始化系统默认模板数据
-- ============================================================================

-- 交班单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_shift_change_default', 'SHIFT_CHANGE', '交班单-标准样式', '{"layout": "standard", "components": ["header", "summary", "details", "footer"]}', 1, 1, '系统预设的交班单标准布局模板');

-- 结账单默认模板  
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_bill_58mm_default', 'BILL', '结账单-58mm标准样式', '{"layout": "58mm", "components": ["header", "order_info", "product_list", "payment_info", "footer"]}', 1, 1, '系统预设的58mm结账单标准布局模板'),
('tpl_bill_80mm_default', 'BILL', '结账单-80mm标准样式', '{"layout": "80mm", "components": ["header", "order_info", "product_list", "payment_info", "footer"]}', 0, 1, '系统预设的80mm结账单标准布局模板');

-- 开台单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_open_table_default', 'OPEN_TABLE', '开台单-标准样式', '{"layout": "standard", "components": ["header", "room_info", "customer_info", "footer"]}', 1, 1, '系统预设的开台单标准布局模板');

-- 退款单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_refund_default', 'REFUND', '退款单-标准样式', '{"layout": "standard", "components": ["header", "refund_info", "original_order", "footer"]}', 1, 1, '系统预设的退款单标准布局模板');

-- 出品单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_product_out_default', 'PRODUCT_OUT', '出品单-标准样式', '{"layout": "standard", "components": ["header", "order_info", "product_list", "footer"]}', 1, 1, '系统预设的出品单标准布局模板');

-- 存酒单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_wine_storage_default', 'WINE_STORAGE', '存酒单-标准样式', '{"layout": "standard", "components": ["header", "wine_info", "storage_info", "footer"]}', 1, 1, '系统预设的存酒单标准布局模板');

-- 取酒单默认模板
INSERT INTO `print_templates` (`id`, `template_code`, `name`, `layout_content`, `is_system_default`, `is_enabled`, `remark`) VALUES
('tpl_wine_withdraw_default', 'WINE_WITHDRAW', '取酒单-标准样式', '{"layout": "standard", "components": ["header", "wine_info", "withdraw_info", "footer"]}', 1, 1, '系统预设的取酒单标准布局模板'); 